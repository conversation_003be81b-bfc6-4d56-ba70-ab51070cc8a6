<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="light dark" />
    <script>
      // Set initial theme before paint to prevent flash
      (function () {
        try {
          var key = 'theme';
          var stored = localStorage.getItem(key);
          var dark = stored ? stored === 'dark' : window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
          var root = document.documentElement;
          if (dark) root.classList.add('dark'); else root.classList.remove('dark');
        } catch (e) { /* noop */ }
      })();
    </script>
    <title>Vite + React + TS</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
