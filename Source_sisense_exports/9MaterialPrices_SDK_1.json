{"sourceFile": "9MaterialPrices_SDK (1).dash", "dashboard": {"title": "9. Material Prices_SDK", "id": "686554e7099a11833ea60c53", "type": "dashboard"}, "hasWidgetsTabber": true, "script": null, "widgetCount": 22, "widgets": [{"id": "686554e7099a11833ea60c54", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554e7099a11833ea60c55", "title": "'", "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Highway & Street Construction Inputs", "% Change From Last Year:"], "dimensions": ["[material_prices.csv].[series_name]"], "measures": ["[material_prices.csv].[month_current]", "[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c56", "title": "Construction, Highway and Street Inputs", "type": "pivot", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Indices", "Index Value", "By Month", "Highway Inputs"], "dimensions": ["[material_prices.csv.series_name]", "[material_prices.csv].[series_name]", "[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]"], "measures": ["[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c57", "title": "Highway & Street Construction Inputs", "type": "chart/line", "subtype": "line/basic", "isMap": false, "jaql": {"titles": ["Months in Date", "PPI Value", "Months in MonthStart"], "dimensions": ["[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]", "[material_prices.csv].[series_name]", "[dim_ppi_month_date.csv.MonthStart (Calendar)]", "[dim_ppi_month_date.csv].[MonthStart]"], "measures": ["[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c58", "title": "Major Components of the Highway & Street Construction Inputs", "type": "chart/line", "subtype": "line/basic", "isMap": false, "jaql": {"titles": ["Months in Date", "Highway and street inputs: Energy", "Highway and street inputs: Goods", "Highway and street inputs: Services"], "dimensions": ["[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]", "[material_prices.csv].[series_name]"], "measures": ["[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c59", "title": "Major Inputs and Commodities for Transportation Construction (base years may differ)", "type": "chart/line", "subtype": "line/basic", "isMap": false, "jaql": {"titles": ["Month", "Index Value", "Input/Commodity"], "dimensions": ["[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]", "[material_prices.csv.value]", "[material_prices.csv].[value]", "[material_prices.csv.series_name]", "[material_prices.csv].[series_name]"], "measures": []}}, {"id": "686554e7099a11833ea60c5a", "title": "'", "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Index Average", "% Change From Last Year:"], "dimensions": [], "measures": ["[material_prices.csv].[month_current]", "[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c5b", "title": "Producer Price Indices for Transportation Construction and Commodities", "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["All Indices and Commodities", "Index Value", "Months in Date"], "dimensions": ["[material_prices.csv.series_name]", "[material_prices.csv].[series_name]", "[material_prices.csv.value]", "[material_prices.csv].[value]", "[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]"], "measures": []}}, {"id": "686554e7099a11833ea60c5c", "title": "Producer Price Indices for Major Commodities", "type": "pivot", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Commodity/Service", "Index Value", "Months in Date"], "dimensions": ["[material_prices.csv.series_name]", "[material_prices.csv].[series_name]", "[material_prices.csv.value]", "[material_prices.csv].[value]", "[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]"], "measures": []}}, {"id": "686554e7099a11833ea60c5d", "title": "<PERSON><PERSON><PERSON>", "type": "chart/line", "subtype": "line/basic", "isMap": false, "jaql": {"titles": ["Months in Date", "PPI Value"], "dimensions": ["[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]", "[material_prices.csv].[series_name]"], "measures": ["[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c5e", "title": "Construction sand and gravel (aggregates)", "type": "chart/line", "subtype": "line/basic", "isMap": false, "jaql": {"titles": ["Months in Date", "PPI Value"], "dimensions": ["[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]", "[material_prices.csv].[series_name]"], "measures": ["[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c5f", "title": "Machinery and equipment: mixers, pavers, and related equipment", "type": "chart/line", "subtype": "line/basic", "isMap": false, "jaql": {"titles": ["Months in Date", "PPI Value"], "dimensions": ["[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]", "[material_prices.csv].[series_name]"], "measures": ["[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c60", "title": "Diesel Fuel", "type": "chart/line", "subtype": "line/basic", "isMap": false, "jaql": {"titles": ["Months in Date", "PPI Value"], "dimensions": ["[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]", "[material_prices.csv].[series_name]"], "measures": ["[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c61", "title": "Ready-Mix Concrete", "type": "chart/line", "subtype": "line/basic", "isMap": false, "jaql": {"titles": ["Months in Date", "PPI Value"], "dimensions": ["[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]", "[material_prices.csv].[series_name]"], "measures": ["[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c62", "title": "Concrete Block and Brick", "type": "chart/line", "subtype": "line/basic", "isMap": false, "jaql": {"titles": ["Months in Date", "PPI Value"], "dimensions": ["[material_prices.csv.Date (Calendar)]", "[material_prices.csv].[Date]", "[material_prices.csv].[series_name]"], "measures": ["[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c63", "title": "'", "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["General Construction Inputs", "% Change From Last Year:"], "dimensions": ["[material_prices.csv].[series_name]"], "measures": ["[material_prices.csv].[month_current]", "[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c64", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554e7099a11833ea60c65", "title": "'", "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Consumer Price Index", "% Change From Last Year:", "series_name"], "dimensions": ["[material_prices.csv].[series_name]", "[material_prices.csv.series_name]"], "measures": ["[material_prices.csv].[month_current]", "[material_prices.csv].[value]"]}}, {"id": "686554e7099a11833ea60c66", "title": null, "type": "WidgetsTabber", "subtype": "WidgetsTabber", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554e7099a11833ea60c67", "title": "'", "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["Select a Commodity"], "dimensions": ["[material_prices.csv.series_name]", "[material_prices.csv].[series_name]"], "measures": []}}, {"id": "686554e7099a11833ea60c68", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554e7099a11833ea60c69", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}], "mapWidgets": []}