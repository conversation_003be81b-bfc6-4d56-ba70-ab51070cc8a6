{"sourceFile": "8StateDOTBudgets_SDK.dash", "dashboard": {"title": "8. State DOT Budgets_SDK", "id": "686554d1099a11833ea60c20", "type": "dashboard"}, "hasWidgetsTabber": true, "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwindow.resetFilters = function(d) { //Function to reset the dashboard filters to the default filters.  Takes parameter 'd' which is a reference to the dashboard\n\t\n\td.filters.clear(); //Clears current filters\n\t\n\td.defaultFilters.forEach(function(filter, index){ //Loop through each default filter and apply to current dashboard filters\n\t\tif(index != d.defaultFilters.length - 1){ //Does not refresh filter if it is not the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:false, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t\telse{//Only refresh dashboard on the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:true, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t})\n\t\n}\n\ndashboard.on('initialized', function(d){    //Resets filters to default when dashboard is first loaded (or refreshed)\n\t\n\t\tresetFilters(prism.activeDashboard); //Resets filters\n\t\n})", "widgetCount": 16, "widgets": [{"id": "686554d1099a11833ea60c21", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554d1099a11833ea60c22", "title": null, "type": "map/area", "subtype": "areamap/usa", "isMap": true, "jaql": {"titles": ["State", "FY 2023 Spending", "fy", "dashboard_choice"], "dimensions": ["[dim_state.csv.stname]", "[dim_state.csv].[stname]", "[budget.value]", "[budget].[value]", "[budget.fy]", "[budget].[fy]", "[budget.dashboard_choice]", "[budget].[dashboard_choice]"], "measures": []}}, {"id": "686554d1099a11833ea60c23", "title": "State Transportation Program/Budget Expenditures (Default Plan Selected)", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["fy", "Total Program Expenditures", "Highway & Bridge Capital Spending", "dashboard_choice"], "dimensions": ["[budget.fy]", "[budget].[fy]", "[budget.dashboard_choice]", "[budget].[dashboard_choice]"], "measures": ["[budget].[value]", "[budget].[hwy_const]"]}}, {"id": "686554d1099a11833ea60c24", "title": "Transportation Budget or Work Program Details", "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["State", "Type of Plan", "Program", "Transportation Budget or Work Program Details", "Fiscal Year"], "dimensions": ["[dim_state.csv.stname]", "[dim_state.csv].[stname]", "[budget.plan]", "[budget].[plan]", "[budget.category]", "[budget].[category]", "[budget.value]", "[budget].[value]", "[budget.fy]", "[budget].[fy]"], "measures": []}}, {"id": "686554d1099a11833ea60c25", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": ["state", "description", "type"], "dimensions": ["[budget.state]", "[budget].[state]", "[state_analysis.description]", "[state_analysis].[description]", "[state_analysis.type]", "[state_analysis].[type]"], "measures": []}}, {"id": "686554d1099a11833ea60c26", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554d1099a11833ea60c27", "title": "Details of State Budgets, Workplans and Revenues", "type": "WidgetsTabber", "subtype": "WidgetsTabber", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554d1099a11833ea60c28", "title": "Select Items - Highway & Bridge Capital Spending", "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["State", "Type of Plan", "Program", "Highway & Bridge Capital Spending", "Fiscal Year"], "dimensions": ["[dim_state.csv.stname]", "[dim_state.csv].[stname]", "[budget.plan]", "[budget].[plan]", "[budget.category]", "[budget].[category]", "[budget.fy]", "[budget].[fy]"], "measures": ["[budget].[hwy_const]"]}}, {"id": "686554d1099a11833ea60c29", "title": "State Transportation Program Revenues (Default Plan Selected)", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Fiscal Year", "Total (or selected) Revenues", "Federal Reimbursements", "fy", "dashboard_choice"], "dimensions": ["[Sheet1.fy]", "[Sheet1].[fy]", "[Sheet1.value]", "[Sheet1].[value]", "[Sheet1.federal]", "[Sheet1].[federal]", "[budget.fy]", "[budget].[fy]", "[Sheet1.dashboard_choice]", "[Sheet1].[dashboard_choice]"], "measures": []}}, {"id": "686554d1099a11833ea60c2a", "title": "Revenue Details", "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["State", "Type of Plan", "Revenue Source", "Program Revenues", "Fiscal Year"], "dimensions": ["[dim_state.csv.stname]", "[dim_state.csv].[stname]", "[Sheet1.plan]", "[Sheet1].[plan]", "[Sheet1.source]", "[Sheet1].[source]", "[Sheet1.value]", "[Sheet1].[value]", "[Sheet1.fy]", "[Sheet1].[fy]"], "measures": []}}, {"id": "686554d1099a11833ea60c2b", "title": "Federal Reimbursements", "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["State", "Type of Plan", "Program Revenues", "Total federal", "Fiscal Year"], "dimensions": ["[dim_state.csv.stname]", "[dim_state.csv].[stname]", "[Sheet1.plan]", "[Sheet1].[plan]", "[Sheet1.source]", "[Sheet1].[source]", "[Sheet1.federal]", "[Sheet1].[federal]", "[Sheet1.fy]", "[Sheet1].[fy]"], "measures": []}}, {"id": "686554d1099a11833ea60c2c", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["Select a State"], "dimensions": ["[dim_state.csv.stname]", "[dim_state.csv].[stname]"], "measures": []}}, {"id": "686554d1099a11833ea60c2d", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["Select a Spending Plan"], "dimensions": ["[budget.plan]", "[budget].[plan]"], "measures": []}}, {"id": "686554d1099a11833ea60c2e", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["Select a Revenue Source"], "dimensions": ["[Sheet1.type]", "[Sheet1].[type]"], "measures": []}}, {"id": "686554d1099a11833ea60c2f", "title": "FY 2023 Spending by Type for (based on Selected Budget/Workplan)", "type": "chart/pie", "subtype": "pie/classic", "isMap": false, "jaql": {"titles": ["type_2", "Total value", "fy"], "dimensions": ["[budget.type_2]", "[budget].[type_2]", "[budget.value]", "[budget].[value]", "[budget.fy]", "[budget].[fy]"], "measures": []}}, {"id": "686554d1099a11833ea60c30", "title": "FY 2023 Revenues by Type, (Federal Funds Include GARVEE Bonds)", "type": "chart/pie", "subtype": "pie/classic", "isMap": false, "jaql": {"titles": ["type", "Revenues", "fy", "dashboard_choice"], "dimensions": ["[Sheet1.type]", "[Sheet1].[type]", "[Sheet1.fy]", "[Sheet1].[fy]", "[Sheet1.dashboard_choice]", "[Sheet1].[dashboard_choice]"], "measures": ["[Sheet1].[value]", "[Sheet1].[fy]"]}}], "mapWidgets": [{"id": "686554d1099a11833ea60c22", "title": null, "subtype": "areamap/usa"}]}