{"sourceFile": "3ValuePutinPlace_SDK (1).dash", "dashboard": {"title": "3. Value Put in Place_SDK", "id": "68655384099a11833ea60aa4", "type": "dashboard"}, "hasWidgetsTabber": true, "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwindow.resetFilters = function(d) { //Function to reset the dashboard filters to the default filters.  Takes parameter 'd' which is a reference to the dashboard\n\t\n\td.filters.clear(); //Clears current filters\n\t\n\td.defaultFilters.forEach(function(filter, index){ //Loop through each default filter and apply to current dashboard filters\n\t\tif(index != d.defaultFilters.length - 1){ //Does not refresh filter if it is not the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:false, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t\telse{//Only refresh dashboard on the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:true, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t})\n\t\n}\n\ndashboard.on('initialized', function(d){    //Resets filters to default when dashboard is first loaded (or refreshed)\n\t\n\t\tresetFilters(prism.activeDashboard); //Resets filters\n\t\n})", "widgetCount": 24, "widgets": [{"id": "68655384099a11833ea60aa5", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Monthly Value", "% Change from Last Year:"], "dimensions": ["[vpip.csv].[MonthStart]"], "measures": ["[vpip.csv].[month_current]", "[vpip.csv].[value]", "[vpip.csv].[year]"]}}, {"id": "68655384099a11833ea60aa6", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Trailing 12-Month (TTM) Total", "% Change from Last Year:", "year", "month_current"], "dimensions": ["[vpip.csv.year]", "[vpip.csv].[year]", "[vpip.csv.month_current]", "[vpip.csv].[month_current]"], "measures": ["[vpip.csv].[ttm]", "[vpip.csv].[month_current]", "[vpip.csv].[year]"]}}, {"id": "68655384099a11833ea60aa7", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Year to Date (YTD) Value", "% Change from Last Year:"], "dimensions": [], "measures": ["[vpip.csv].[ytd_value]", "[vpip.csv].[month_current]", "[vpip.csv].[year]"]}}, {"id": "68655384099a11833ea60aa8", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655384099a11833ea60aa9", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655384099a11833ea60aaa", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655384099a11833ea60aab", "title": "Monthly Value", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["year", "Monthly Value of Construction Work", "month_current"], "dimensions": ["[vpip.csv.year]", "[vpip.csv].[year]", "[vpip.csv.value]", "[vpip.csv].[value]", "[vpip.csv.month_current]", "[vpip.csv].[month_current]"], "measures": []}}, {"id": "68655384099a11833ea60aac", "title": "YTD Value", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Years in MonthStart", "YTD Value of Construction Work"], "dimensions": ["[vpip.csv.MonthStart (Calendar)]", "[vpip.csv].[MonthStart]"], "measures": ["[vpip.csv].[ytd_value]", "[vpip.csv].[month_current]"]}}, {"id": "68655384099a11833ea60aad", "title": "Monthly Value Breakdown by Mode", "type": "chart/pie", "subtype": "pie/donut", "isMap": false, "jaql": {"titles": ["Mode Detail", "Total value", "type", "month_current", "year"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]", "[vpip.csv.value]", "[vpip.csv].[value]", "[vpip.csv.month_current]", "[vpip.csv].[month_current]", "[vpip.csv.year]", "[vpip.csv].[year]"], "measures": []}}, {"id": "68655384099a11833ea60aae", "title": "% Change in Monthly Value by Mode", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["% Change in Value", "Type of Construction"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]"], "measures": ["[vpip.csv].[month_current]", "[vpip.csv].[value]", "[vpip.csv].[year]"]}}, {"id": "68655384099a11833ea60aaf", "title": null, "type": "WidgetsTabber", "subtype": "WidgetsTabber", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655384099a11833ea60ab0", "title": "YTD Value", "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Type of Construction", "YTD Value", "year", "Years in Date"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]", "[vpip.csv.year]", "[vpip.csv].[year]", "[Dates.Date (Calendar)]", "[Dates].[Date]"], "measures": ["[vpip.csv].[value]", "[vpip.csv].[ytd]"]}}, {"id": "68655384099a11833ea60ab1", "title": "% Change in YTD Value by Mode", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["% Change in Value", "Type of Construction"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]"], "measures": ["[vpip.csv].[ytd_value]", "[vpip.csv].[month_current]", "[vpip.csv].[year]"]}}, {"id": "68655384099a11833ea60ab2", "title": "TTM Value", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Months in Date", "TTM Value of Construction Work"], "dimensions": ["[Dates.Date (Calendar)]", "[Dates].[Date]"], "measures": ["[vpip.csv].[ttm]"]}}, {"id": "68655384099a11833ea60ab3", "title": "Monthly Value", "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Type of Construction", "Total Value", "Years"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]", "[Dates.Date (Calendar)]", "[Dates].[Date]"], "measures": ["[vpip.csv].[month_current]", "[vpip.csv].[value]"]}}, {"id": "68655384099a11833ea60ab4", "title": "TTM Value", "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Type of Construction", "Trailing 12-Month Total", "Months in Date"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]", "[Dates.Date (Calendar)]", "[Dates].[Date]"], "measures": ["[vpip.csv].[ttm]"]}}, {"id": "68655384099a11833ea60ab5", "title": "% Change in TTM Value by Mode", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["% Change in TTM Totals", "Type of Construction"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]"], "measures": ["[vpip.csv].[month_current]", "[vpip.csv].[ttm]", "[vpip.csv].[year]"]}}, {"id": "68655384099a11833ea60ab6", "title": "YTD Value Breakdown by Mode", "type": "chart/pie", "subtype": "pie/donut", "isMap": false, "jaql": {"titles": ["Mode Detail", "Total value", "type", "ytd", "year"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]", "[vpip.csv.value]", "[vpip.csv].[value]", "[vpip.csv.ytd]", "[vpip.csv].[ytd]", "[vpip.csv.year]", "[vpip.csv].[year]"], "measures": []}}, {"id": "68655384099a11833ea60ab7", "title": "TTM Value Breakdown by Mode", "type": "chart/pie", "subtype": "pie/donut", "isMap": false, "jaql": {"titles": ["Mode Detail", "TTM Value", "type", "year", "month_current"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]", "[vpip.csv.year]", "[vpip.csv].[year]", "[vpip.csv.month_current]", "[vpip.csv].[month_current]"], "measures": ["[vpip.csv].[ttm]"]}}, {"id": "68655384099a11833ea60ab8", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655384099a11833ea60ab9", "title": "Annual Value", "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Type of Construction", "Total Value", "Years"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]", "[Dates.Date (Calendar)]", "[Dates].[Date]"], "measures": ["[vpip.csv].[value]"]}}, {"id": "68655384099a11833ea60aba", "title": "% Change in Annual Value by Mode, 2024 vs. 2023", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["% Change in Value", "Type of Construction"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]"], "measures": ["[vpip.csv].[value]", "[vpip.csv].[year]"]}}, {"id": "68655384099a11833ea60abb", "title": "2024 Annual Value Breakdown by Mode", "type": "chart/pie", "subtype": "pie/donut", "isMap": false, "jaql": {"titles": ["Mode Detail", "Total value", "type", "year"], "dimensions": ["[vpip.csv.type]", "[vpip.csv].[type]", "[vpip.csv.value]", "[vpip.csv].[value]", "[vpip.csv.year]", "[vpip.csv].[year]"], "measures": []}}, {"id": "68655384099a11833ea60abc", "title": "Annual Value", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["year", "Annual Value of Construction Work"], "dimensions": ["[vpip.csv.year]", "[vpip.csv].[year]", "[vpip.csv.value]", "[vpip.csv].[value]"], "measures": []}}], "mapWidgets": []}