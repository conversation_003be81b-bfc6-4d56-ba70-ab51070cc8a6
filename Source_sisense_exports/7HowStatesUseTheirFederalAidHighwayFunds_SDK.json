{"sourceFile": "7HowStatesUseTheirFederalAidHighwayFunds_SDK.dash", "dashboard": {"title": "7. How States Use Their Federal Aid Highway Funds_SDK", "id": "686554bc099a11833ea60be4", "type": "dashboard"}, "hasWidgetsTabber": false, "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\ndashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') .css('background-color','#ffffff');\n\n$('.dashboard-layout-subcell-vertical-divider') .css('background-color','#ffffff');\n\t\n$(\".dashboard-layout-cell:first-of-type\").css('max-height','60px !important');\n\n})\n\n\n", "widgetCount": 19, "widgets": [{"id": "686554bc099a11833ea60be5", "title": "Type of Work Performed", "type": "chart/pie", "subtype": "pie/classic", "isMap": false, "jaql": {"titles": ["Type of Work:", "Total Project Cost"], "dimensions": ["[dim_worktype.csv.work_type]", "[dim_worktype.csv].[work_type]", "[fact_dataprojects.csv.totalcost]", "[fact_dataprojects.csv].[totalcost]"], "measures": []}}, {"id": "686554bc099a11833ea60be6", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["Type of Work"], "dimensions": ["[dim_worktype.csv.work_type]", "[dim_worktype.csv].[work_type]"], "measures": []}}, {"id": "686554bc099a11833ea60be7", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["State", "c<PERSON><PERSON>"], "dimensions": ["[dim_location.csv.stname]", "[dim_location.csv].[stname]", "[data_projects.ctyname]", "[data_projects].[ctyname]"], "measures": []}}, {"id": "686554bc099a11833ea60be8", "title": "Value by Mode", "type": "chart/pie", "subtype": "pie/classic", "isMap": false, "jaql": {"titles": ["Mode", "Total Project Cost"], "dimensions": ["[dim_mode.csv.mode]", "[dim_mode.csv].[mode]", "[fact_dataprojects.csv.totalcost]", "[fact_dataprojects.csv].[totalcost]"], "measures": []}}, {"id": "686554bc099a11833ea60be9", "title": "Value by Federal-Aid System", "type": "chart/pie", "subtype": "pie/classic", "isMap": false, "jaql": {"titles": ["Federal-Aid System", "Total Project Cost"], "dimensions": ["[dim_system.csv.system]", "[dim_system.csv].[system]", "[fact_dataprojects.csv.totalcost]", "[fact_dataprojects.csv].[totalcost]"], "measures": []}}, {"id": "686554bc099a11833ea60bea", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["Fiscal Year Work Started:"], "dimensions": ["[dim_date.csv.date (Calendar)]", "[dim_date.csv].[date]"], "measures": []}}, {"id": "686554bc099a11833ea60beb", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["Federal-Aid System"], "dimensions": ["[dim_system.csv.system]", "[dim_system.csv].[system]"], "measures": []}}, {"id": "686554bc099a11833ea60bec", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554bc099a11833ea60bed", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554bc099a11833ea60bee", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554bc099a11833ea60bef", "title": "Federal Aid Highway Funds by State", "type": "map/area", "subtype": "areamap/usa", "isMap": true, "jaql": {"titles": ["State", "Federal Funds"], "dimensions": ["[dim_location.csv.stname]", "[dim_location.csv].[stname]", "[fact_dataprojects.csv.federalfunds]", "[fact_dataprojects.csv].[federalfunds]"], "measures": []}}, {"id": "686554bc099a11833ea60bf0", "title": "Value by Area", "type": "chart/pie", "subtype": "pie/classic", "isMap": false, "jaql": {"titles": ["Area", "Total Project Cost"], "dimensions": ["[fact_dataprojects.csv.area]", "[fact_dataprojects.csv].[area]", "[fact_dataprojects.csv.totalcost]", "[fact_dataprojects.csv].[totalcost]"], "measures": []}}, {"id": "686554bc099a11833ea60bf1", "title": "Source of Funding", "type": "pivot", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Type of Work", "Federal Funds", "State Funds", "Local Funds", "Private & Other Funds", "Advance Construction Funds", "Total Costs"], "dimensions": ["[dim_worktype.csv.work_type]", "[dim_worktype.csv].[work_type]", "[fact_dataprojects.csv.federalfunds]", "[fact_dataprojects.csv].[federalfunds]", "[fact_dataprojects.csv.statefunds]", "[fact_dataprojects.csv].[statefunds]", "[fact_dataprojects.csv.localfunds]", "[fact_dataprojects.csv].[localfunds]", "[fact_dataprojects.csv.privatefunds]", "[fact_dataprojects.csv].[privatefunds]", "[fact_dataprojects.csv.acfunds]", "[fact_dataprojects.csv].[acfunds]"], "measures": ["[fact_dataprojects.csv].[privatefunds]", "[fact_dataprojects.csv].[federalfunds]", "[fact_dataprojects.csv].[statefunds]", "[fact_dataprojects.csv].[localfunds]", "[fact_dataprojects.csv].[acfunds]"]}}, {"id": "686554bc099a11833ea60bf2", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554bc099a11833ea60bf3", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "686554bc099a11833ea60bf4", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["County", "c<PERSON><PERSON>"], "dimensions": ["[dim_location.csv.ctyname]", "[dim_location.csv].[ctyname]", "[data_projects.ctyname]", "[data_projects].[ctyname]"], "measures": []}}, {"id": "686554bc099a11833ea60bf5", "title": "Federal Funds as a % of State Highway & Bridge Program Capital Outlays", "type": "map/area", "subtype": "areamap/usa", "isMap": true, "jaql": {"titles": ["State", "Fed Cap"], "dimensions": ["[dim_location.csv.stname]", "[dim_location.csv].[stname]"], "measures": ["[dim_pcnt.csv].[fed_cap]"]}}, {"id": "686554bc099a11833ea60bf6", "title": "Value of Project by Fiscal Year Major Work Began", "type": "chart/column", "subtype": "column/stackedcolumn", "isMap": false, "jaql": {"titles": ["Year", "Federal Funds", "Advanced Construction Funds", "State Funds", "Local Funds", "Private & Other Funds"], "dimensions": ["[dim_date.csv.date (Calendar)]", "[dim_date.csv].[date]", "[fact_dataprojects.csv.federalfunds]", "[fact_dataprojects.csv].[federalfunds]", "[fact_dataprojects.csv.acfunds]", "[fact_dataprojects.csv].[acfunds]", "[fact_dataprojects.csv.statefunds]", "[fact_dataprojects.csv].[statefunds]", "[fact_dataprojects.csv.localfunds]", "[fact_dataprojects.csv].[localfunds]", "[fact_dataprojects.csv.privatefunds]", "[fact_dataprojects.csv].[privatefunds]"], "measures": []}}, {"id": "686554bc099a11833ea60bf7", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}], "mapWidgets": [{"id": "686554bc099a11833ea60bef", "title": "Federal Aid Highway Funds by State", "subtype": "areamap/usa"}, {"id": "686554bc099a11833ea60bf5", "title": "Federal Funds as a % of State Highway & Bridge Program Capital Outlays", "subtype": "areamap/usa"}]}