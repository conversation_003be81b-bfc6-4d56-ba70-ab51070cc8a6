{"sourceFile": "6StateLegislativeInitiatives_SDK.dash", "dashboard": {"title": "6. State Legislative Initiatives_SDK", "id": "68655309099a11833ea60a2a", "type": "dashboard"}, "hasWidgetsTabber": false, "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\ndashboard.on('widgetready', function(sender, ev){ \n\n$('.dashboard-layout-cell-horizontal-divider') .css('background-color','#ffffff');\n\n$('.dashboard-layout-subcell-vertical-divider') .css('background-color','#ffffff');\n\t\n$(\".dashboard-layout-cell:first-of-type\").css('max-height','60px !important');\n\n})\n\n\n", "widgetCount": 14, "widgets": [{"id": "68655309099a11833ea60a2b", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Bills Introduced:", "0 (default)", "100 (default)"], "dimensions": ["[dim_id.csv.id]", "[dim_id.csv].[id]"], "measures": []}}, {"id": "68655309099a11833ea60a2c", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Passed a Chamber:", "status"], "dimensions": ["[dim_id.csv.id]", "[dim_id.csv].[id]", "[dim_status.csv.status]", "[dim_status.csv].[status]"], "measures": []}}, {"id": "68655309099a11833ea60a2d", "title": "Revenue Increases Proposed in Legislation*", "type": "chart/pie", "subtype": "pie/classic", "isMap": false, "jaql": {"titles": ["Type of Bill", "Number of Bills"], "dimensions": ["[fact_category.csv.category]", "[fact_category.csv].[category]", "[fact_category.csv.id]", "[fact_category.csv].[id]"], "measures": []}}, {"id": "68655309099a11833ea60a2e", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["State"], "dimensions": ["[dim_state.csv.stname]", "[dim_state.csv].[stname]"], "measures": []}}, {"id": "68655309099a11833ea60a2f", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655309099a11833ea60a30", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655309099a11833ea60a31", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655309099a11833ea60a32", "title": "Number of Measures Introduced in 2025", "type": "map/area", "subtype": "areamap/usa", "isMap": true, "jaql": {"titles": ["State", "Number of Bills"], "dimensions": ["[dim_state.csv.stname]", "[dim_state.csv].[stname]", "[dim_id.csv.id]", "[dim_id.csv].[id]"], "measures": []}}, {"id": "68655309099a11833ea60a33", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Signed Into Law:", "status"], "dimensions": ["[dim_id.csv.id]", "[dim_id.csv].[id]", "[dim_status.csv.status]", "[dim_status.csv].[status]"], "measures": []}}, {"id": "68655309099a11833ea60a34", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["Status"], "dimensions": ["[dim_status.csv.status_major]", "[dim_status.csv].[status_major]"], "measures": []}}, {"id": "68655309099a11833ea60a35", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Revenue Approved:", "status"], "dimensions": ["[dim_id.csv.revenue]", "[dim_id.csv].[revenue]", "[dim_status.csv.status]", "[dim_status.csv].[status]"], "measures": []}}, {"id": "68655309099a11833ea60a36", "title": "Legislation Introduced", "type": "tablewidget", "subtype": "tablewidget", "isMap": false, "jaql": {"titles": ["State", "<PERSON>", "Type(s) of Measure", "Status", "Description", "Revenue Approved"], "dimensions": ["[dim_id.csv.state]", "[dim_id.csv].[state]", "[dim_id.csv.bill]", "[dim_id.csv].[bill]", "[dim_id.csv.category_sum]", "[dim_id.csv].[category_sum]", "[dim_status.csv.status_major]", "[dim_status.csv].[status_major]", "[dim_id.csv.description]", "[dim_id.csv].[description]", "[dim_id.csv.revenue]", "[dim_id.csv].[revenue]"], "measures": []}}, {"id": "68655309099a11833ea60a37", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655309099a11833ea60a38", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}], "mapWidgets": [{"id": "68655309099a11833ea60a32", "title": "Number of Measures Introduced in 2025", "subtype": "areamap/usa"}]}