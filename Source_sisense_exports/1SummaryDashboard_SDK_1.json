{"sourceFile": "1SummaryDashboard_SDK (1).dash", "dashboard": {"title": "1. Summary Dashboard_SDK", "id": "6865541f099a11833ea60aef", "type": "dashboard"}, "hasWidgetsTabber": false, "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwindow.resetFilters = function(d) { //Function to reset the dashboard filters to the default filters.  Takes parameter 'd' which is a reference to the dashboard\n\t\n\td.filters.clear(); //Clears current filters\n\t\n\td.defaultFilters.forEach(function(filter, index){ //Loop through each default filter and apply to current dashboard filters\n\t\tif(index != d.defaultFilters.length - 1){ //Does not refresh filter if it is not the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:false, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t\telse{//Only refresh dashboard on the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:true, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t})\n\t\n}\n\ndashboard.on('initialized', function(d){    //Resets filters to default when dashboard is first loaded (or refreshed)\n\t\n\t\tresetFilters(prism.activeDashboard); //Resets filters\n\t\n})", "widgetCount": 21, "widgets": [{"id": "6865541f099a11833ea60af0", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["TTM Contract Awards", "% Change from last year:"], "dimensions": ["[executive_summary.csv].[MonthStart]"], "measures": ["[executive_summary.csv].[month_current_awards]", "[executive_summary.csv].[ttm_awards]", "[executive_summary.csv].[year_dum]"]}}, {"id": "6865541f099a11833ea60af1", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60af2", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["TTM Construction Activity", "% Change From Last Year"], "dimensions": [], "measures": ["[executive_summary.csv].[month_current_vpip]", "[executive_summary.csv].[year_dum_vpip]", "[executive_summary.csv].[ttm_vpip]", "[vpip.csv].[month_current]", "[vpip.csv].[ttm]", "[vpip.csv].[year]"]}}, {"id": "6865541f099a11833ea60af3", "title": null, "type": "indicator", "subtype": "indicator/gauge", "isMap": false, "jaql": {"titles": ["FY 2025 Formula Funds Committed", "Invalid Formula", "1"], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60af4", "title": "TTM U.S. Transp. Const. Work", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Months in MonthStart", "TTM U.S. Transportation Const. Work"], "dimensions": ["[executive_summary.csv.MonthStart (Calendar)]", "[executive_summary.csv].[MonthStart]", "[executive_summary.csv.ttm_vpip]", "[executive_summary.csv].[ttm_vpip]"], "measures": []}}, {"id": "6865541f099a11833ea60af5", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60af6", "title": "ARTBA 2025 Transp. Const. Market Outlook", "type": "chart/column", "subtype": "column/stackedcolumn", "isMap": false, "jaql": {"titles": ["Years in date", "Value of Construction Activity", "Type of Construction"], "dimensions": ["[forecast.csv.date (Calendar)]", "[forecast.csv].[date]", "[forecast.csv.Type of Construction]", "[forecast.csv].[Type of Construction]"], "measures": ["[forecast.csv].[Total Value]"]}}, {"id": "6865541f099a11833ea60af7", "title": "U.S. Transp. Const. Work by Mode", "type": "chart/pie", "subtype": "pie/donut", "isMap": false, "jaql": {"titles": ["mode", "Trailing 12-Month Value"], "dimensions": ["[executive_summary.csv.mode]", "[executive_summary.csv].[mode]"], "measures": ["[executive_summary.csv].[month_current_vpip]", "[executive_summary.csv].[year_dum_vpip]", "[executive_summary.csv].[ttm_vpip]"]}}, {"id": "6865541f099a11833ea60af8", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60af9", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60afa", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60afb", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60afc", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60afd", "title": "Number of State Legislative Funding Measures Introduced in 2025", "type": "map/area", "subtype": "areamap/usa", "isMap": true, "jaql": {"titles": ["State", "Number of Bills"], "dimensions": ["[dim_state.csv.stname]", "[dim_state.csv].[stname]", "[dim_id.csv.id]", "[dim_id.csv].[id]"], "measures": []}}, {"id": "6865541f099a11833ea60afe", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60aff", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60b00", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60b01", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60b02", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6865541f099a11833ea60b03", "title": "TTM State & Local Govt. Transp. Contract Awards", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Months in MonthStart", "Trailing 12-Month Total Value of Awards", "Months"], "dimensions": ["[executive_summary.csv.MonthStart (Calendar)]", "[executive_summary.csv].[MonthStart]"], "measures": ["[executive_summary.csv].[ttm_awards]"]}}, {"id": "6865541f099a11833ea60b04", "title": "General Election Transportation Funding Ballot Measures, 2014 - 2024", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Years in year_date", "Number of Finalized Measures", "Percent of Measures Approved", "result"], "dimensions": ["[ballot.year_date (Calendar)]", "[ballot].[year_date]", "[ballot.ballot]", "[ballot].[ballot]", "[ballot.result]", "[ballot].[result]"], "measures": ["[ballot].[ballot]", "[ballot].[win]"]}}], "mapWidgets": [{"id": "6865541f099a11833ea60afd", "title": "Number of State Legislative Funding Measures Introduced in 2025", "subtype": "areamap/usa"}]}