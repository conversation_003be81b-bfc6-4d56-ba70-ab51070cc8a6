{"sourceFile": "4Federal-AidObligations_SDK (1).dash", "dashboard": {"title": "4. Federal-Aid Obligations_SDK", "id": "68655464099a11833ea60b57", "type": "dashboard"}, "hasWidgetsTabber": true, "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwindow.resetFilters = function(d) { //Function to reset the dashboard filters to the default filters.  Takes parameter 'd' which is a reference to the dashboard\n\t\n\td.filters.clear(); //Clears current filters\n\t\n\td.defaultFilters.forEach(function(filter, index){ //Loop through each default filter and apply to current dashboard filters\n\t\tif(index != d.defaultFilters.length - 1){ //Does not refresh filter if it is not the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:false, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t\telse{//Only refresh dashboard on the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:true, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t})\n\t\n}\n\ndashboard.on('initialized', function(d){    //Resets filters to default when dashboard is first loaded (or refreshed)\n\t\n\t\tresetFilters(prism.activeDashboard); //Resets filters\n\t\n})", "widgetCount": 17, "widgets": [{"id": "68655464099a11833ea60b58", "title": "'", "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Monthly Obligations", "month_current"], "dimensions": ["[obligations_box.csv.monthly]", "[obligations_box.csv].[monthly]", "[awards.month_current]", "[awards].[month_current]"], "measures": []}}, {"id": "68655464099a11833ea60b59", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655464099a11833ea60b5a", "title": "'", "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Fiscal YTD Obligations", "month_current", "Year"], "dimensions": ["[obligations_box.csv.ytd]", "[obligations_box.csv].[ytd]", "[vpip.csv.month_current]", "[vpip.csv].[month_current]", "[Dates.Year]", "[Dates].[Year]"], "measures": []}}, {"id": "68655464099a11833ea60b5b", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655464099a11833ea60b5c", "title": "'", "type": "indicator", "subtype": "indicator/gauge", "isMap": false, "jaql": {"titles": ["FY25 Percent Formula Funds Committed", "Invalid Formula", "1"], "dimensions": [], "measures": []}}, {"id": "68655464099a11833ea60b5d", "title": "State Obligations of Federal-Aid Highway and Highway Safety Construction Funds", "type": "tablewidget", "subtype": "tablewidget", "isMap": false, "jaql": {"titles": ["State", "Amount Apportioned or Allocated So Far During FY 2025", "Amount Carried Over from Prior Years", "Total Amount Available for Obligation in FY 2025", "Year-to-Date Obligations (Formula and Exempt Programs)", "Year-to-Date Funds Flexed to Transit and Other Programs", "Amount Remaining Subject to Annual Obligation", "Amount Re<PERSON>ining from Special Programs", "Total Amount Remaining", "order"], "dimensions": ["[obligations_state.csv.stname]", "[obligations_state.csv].[stname]", "[obligations_state.csv.column1]", "[obligations_state.csv].[column1]", "[obligations_state.csv.column2]", "[obligations_state.csv].[column2]", "[obligations_state.csv.column3]", "[obligations_state.csv].[column3]", "[obligations_state.csv.column4]", "[obligations_state.csv].[column4]", "[obligations_state.csv.column5]", "[obligations_state.csv].[column5]", "[obligations_state.csv.column6]", "[obligations_state.csv].[column6]", "[obligations_state.csv.column7]", "[obligations_state.csv].[column7]", "[obligations_state.csv.column8]", "[obligations_state.csv].[column8]", "[obligations_state.csv.order]", "[obligations_state.csv].[order]"], "measures": []}}, {"id": "68655464099a11833ea60b5e", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655464099a11833ea60b5f", "title": "Fiscal Year 2025 YTD Obligations", "type": "map/area", "subtype": "areamap/usa", "isMap": true, "jaql": {"titles": ["State", "Obligations", "stname"], "dimensions": ["[obligations_state.csv.stname]", "[obligations_state.csv].[stname]"], "measures": ["[obligations_state.csv].[column4]"]}}, {"id": "68655464099a11833ea60b60", "title": "Fiscal Year Obligation of Federal-Aid Highway & Highway Safety Construction Funds", "type": "tablewidget", "subtype": "tablewidget", "isMap": false, "jaql": {"titles": ["Type of Funds/Obligation", "Amount Apportioned or Allocated So Far During FY 2025", "Amout Carried Over From Previous Years", "Total Amount Available for Obligation in FY 2025, To Date", "Year-to-Date Obligations", "Remaining Amount Available for Obligation, To Date", "Percent of Funds Remaining"], "dimensions": ["[obligations_table.csv.category]", "[obligations_table.csv].[category]", "[obligations_table.csv.column1]", "[obligations_table.csv].[column1]", "[obligations_table.csv.column2]", "[obligations_table.csv].[column2]", "[obligations_table.csv.column3]", "[obligations_table.csv].[column3]", "[obligations_table.csv.column4]", "[obligations_table.csv].[column4]", "[obligations_table.csv.column5]", "[obligations_table.csv].[column5]", "[obligations_table.csv.column6]", "[obligations_table.csv].[column6]"], "measures": []}}, {"id": "68655464099a11833ea60b61", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655464099a11833ea60b62", "title": "Federal-Aid Commitments by Major Type, FY 2022 to Date", "type": "chart/pie", "subtype": "pie/classic", "isMap": false, "jaql": {"titles": ["Type of FHWA Funds", "Type of Funds:", "submission_year"], "dimensions": ["[fhwa_fact.csv.type_major]", "[fhwa_fact.csv].[type_major]", "[Dates].[Date]", "[fhwa_projects.submission_year]", "[fhwa_projects].[submission_year]"], "measures": ["[fhwa_fact.csv].[ob]"]}}, {"id": "68655464099a11833ea60b63", "title": "Federal-Aid Commitments by Program, FY 2022 to Date", "type": "chart/pie", "subtype": "pie/classic", "isMap": false, "jaql": {"titles": ["FHWA Program", "Value of Obligations"], "dimensions": ["[fhwa_fact.csv.program_activity_name]", "[fhwa_fact.csv].[program_activity_name]"], "measures": ["[fhwa_fact.csv].[ob]"]}}, {"id": "68655464099a11833ea60b64", "title": "Details for Major Projects Receiving IIJA Funds, FY 2022 to Date", "type": "tablewidget", "subtype": "tablewidget", "isMap": false, "jaql": {"titles": ["State", "Project Description", "Total Federal Funds Committed", "Total Amount Reimbursed to State DOT", "Formula Funds", "Bridge Formula Funds", "Discretionary Funds", "COVID Funds", "Supplemental Approps", "Emergency Relief"], "dimensions": ["[fhwa_projects.state]", "[fhwa_projects].[state]", "[fhwa_projects.desc]", "[fhwa_projects].[desc]", "[fhwa_projects.total_obligation]", "[fhwa_projects].[total_obligation]", "[fhwa_projects.total_spend]", "[fhwa_projects].[total_spend]", "[fhwa_projects.formula_ob]", "[fhwa_projects].[formula_ob]", "[fhwa_projects.bridge_ob]", "[fhwa_projects].[bridge_ob]", "[fhwa_projects.disc_ob]", "[fhwa_projects].[disc_ob]", "[fhwa_projects.covid_ob]", "[fhwa_projects].[covid_ob]", "[fhwa_projects.approp_ob]", "[fhwa_projects].[approp_ob]", "[fhwa_projects.emerg_ob]", "[fhwa_projects].[emerg_ob]"], "measures": []}}, {"id": "68655464099a11833ea60b65", "title": null, "type": "WidgetsTabber", "subtype": "WidgetsTabber", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68655464099a11833ea60b66", "title": "Total Value of Federal-Aid Highway Obligations (Lagged)", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["submission_year", "Total Value of Federal Aid Commitments"], "dimensions": ["[fhwa_projects.submission_year]", "[fhwa_projects].[submission_year]"], "measures": ["[fhwa_projects].[total_obligation]"]}}, {"id": "68655464099a11833ea60b67", "title": "Fiscal Year 2025 Obligations", "type": "chart/area", "subtype": "area/basic", "isMap": false, "jaql": {"titles": ["Months in month_year", "Monthly Obligation", "Total YTD Obligations"], "dimensions": ["[graph_obytd.csv.month_year (Calendar)]", "[graph_obytd.csv].[month_year]", "[graph_obytd.csv.total_month]", "[graph_obytd.csv].[total_month]", "[graph_obytd.csv.total_ytd]", "[graph_obytd.csv].[total_ytd]"], "measures": []}}, {"id": "68655464099a11833ea60b68", "title": "Breakdown of Federal Aid Highway Projects by Type of Spending, FY 2022- FY 2024", "type": "chart/pie", "subtype": "pie/classic", "isMap": false, "jaql": {"titles": ["Type of Spending", "Type of Work"], "dimensions": ["[spending_breakdown.csv.type]", "[spending_breakdown.csv].[type]"], "measures": ["[spending_breakdown.csv].[totalcost]"]}}], "mapWidgets": [{"id": "68655464099a11833ea60b5f", "title": "Fiscal Year 2025 YTD Obligations", "subtype": "areamap/usa"}]}