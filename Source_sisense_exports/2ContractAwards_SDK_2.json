{"sourceFile": "2ContractAwards_SDK (2).dash", "dashboard": {"title": "2. Contract Awards_SDK", "id": "68654950099a11833ea60935", "type": "dashboard"}, "hasWidgetsTabber": true, "script": "/*\nWelcome to your Dashboard's Script.\n\nTo learn how you can access the Widget and Dashboard objects, see the online documentation at https://developer.sisense.com/pages/viewpage.action?pageId=557127\n*/\n\nwindow.resetFilters = function(d) { //Function to reset the dashboard filters to the default filters.  Takes parameter 'd' which is a reference to the dashboard\n\t\n\td.filters.clear(); //Clears current filters\n\t\n\td.defaultFilters.forEach(function(filter, index){ //Loop through each default filter and apply to current dashboard filters\n\t\tif(index != d.defaultFilters.length - 1){ //Does not refresh filter if it is not the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:false, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t\telse{//Only refresh dashboard on the last filter\n\t\t\td.filters.update(filter,{\n\t\t\t\tsave:true, \n\t\t\t\trefresh:true, \n\t\t\t\tunionIfSameDimensionAndSameType:true\n\t\t\t});\n\t\t} \n\t})\n\t\n}\n\ndashboard.on('initialized', function(d){    //Resets filters to default when dashboard is first loaded (or refreshed)\n\t\n\t\tresetFilters(prism.activeDashboard); //Resets filters\n\t\n})", "widgetCount": 41, "widgets": [{"id": "68654950099a11833ea60936", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["Monthly Value", "Number of Projects:"], "dimensions": [], "measures": ["[awards].[month_current]", "[awards].[value]", "[awards].[yeardum]", "[awards].[numpro]"]}}, {"id": "68654950099a11833ea60937", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["YTD Value", "Number of Projects:"], "dimensions": [], "measures": ["[awards].[value]", "[awards].[yeardum]", "[awards].[numpro]", "[awards].[ytd]"]}}, {"id": "68654950099a11833ea60938", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68654950099a11833ea60939", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Mode", "Monthly Value", "Years in MonthStart"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]", "[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[month_current]", "[awards].[value]"]}}, {"id": "68654950099a11833ea6093a", "title": "Monthly Value by State", "type": "map/area", "subtype": "areamap/usa", "isMap": true, "jaql": {"titles": ["State", "Value of Awards:", "Year1"], "dimensions": ["[Geography.state]", "[Geography].[state]", "[Dates.Year]", "[Dates].[Year]"], "measures": ["[awards].[month_current]", "[awards].[value]", "[awards].[yeardum]"]}}, {"id": "68654950099a11833ea6093b", "title": "YTD Value", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Years in MonthStart", "YTD Value of Awards", "month_current"], "dimensions": ["[awards.MonthStart (Calendar)]", "[awards].[MonthStart]", "[vpip.csv.month_current]", "[vpip.csv].[month_current]"], "measures": ["[awards].[value]", "[awards].[ytd]"]}}, {"id": "68654950099a11833ea6093c", "title": "YTD Value by Mode", "type": "chart/pie", "subtype": "pie/donut", "isMap": false, "jaql": {"titles": ["Mode", "Value of Awards"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]"], "measures": ["[awards].[value]", "[awards].[yeardum]", "[awards].[ytd]"]}}, {"id": "68654950099a11833ea6093d", "title": "Monthly Number of Contract Awards", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Years in MonthStart", "Value"], "dimensions": ["[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[month_current]", "[awards].[numpro]"]}}, {"id": "68654950099a11833ea6093e", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["State", "Monthly Value", "Years in MonthStart"], "dimensions": ["[Geography.state]", "[Geography].[state]", "[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[month_current]", "[awards].[value]"]}}, {"id": "68654950099a11833ea6093f", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Mode", "YTD Value", "Years in MonthStart"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]", "[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[value]", "[awards].[ytd]"]}}, {"id": "68654950099a11833ea60940", "title": "YTD Value by State", "type": "map/area", "subtype": "areamap/usa", "isMap": true, "jaql": {"titles": ["State", "YTD Value:"], "dimensions": ["[Geography.state]", "[Geography].[state]"], "measures": ["[awards].[ytd_value]", "[awards].[month_current]", "[awards].[yeardum]"]}}, {"id": "68654950099a11833ea60941", "title": null, "type": "indicator", "subtype": "indicator/numeric", "isMap": false, "jaql": {"titles": ["TTM Value", "Number of Projects:"], "dimensions": [], "measures": ["[awards].[month_current]", "[awards].[ttm]", "[awards].[yeardum]", "[awards].[ttm_numpro]"]}}, {"id": "68654950099a11833ea60942", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["State", "YTD Value", "year"], "dimensions": ["[Geography.state]", "[Geography].[state]", "[awards.year]", "[awards].[year]"], "measures": ["[awards].[value]", "[awards].[ytd]"]}}, {"id": "68654950099a11833ea60943", "title": "YTD Number of Contract Awards", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Years in MonthStart", "Number of Awards"], "dimensions": ["[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[numpro]", "[awards].[ytd]"]}}, {"id": "68654950099a11833ea60944", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["State", "Trailing 12-Month Totals", "Months in MonthStart", "Months"], "dimensions": ["[Geography.state]", "[Geography].[state]", "[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[ttm]"]}}, {"id": "68654950099a11833ea60945", "title": "TTM Value", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Months in MonthStart", "Trailing 12-Month Total Value of Awards", "Years in MonthStart"], "dimensions": ["[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[ttm]"]}}, {"id": "68654950099a11833ea60946", "title": "TTM Number of Contract Awards", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["Months in MonthStart", "Value", "Years in MonthStart"], "dimensions": ["[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[ttm_numpro]"]}}, {"id": "68654950099a11833ea60947", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Mode", "TTM Value", "Months in MonthStart"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]", "[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[ttm]"]}}, {"id": "68654950099a11833ea60948", "title": "TTM Value of Contract Awards by State", "type": "map/area", "subtype": "areamap/usa", "isMap": true, "jaql": {"titles": ["State", "TTM Value:"], "dimensions": ["[Geography.state]", "[Geography].[state]"], "measures": ["[awards].[month_current]", "[awards].[ttm]", "[awards].[yeardum]"]}}, {"id": "68654950099a11833ea60949", "title": null, "type": "WidgetsTabber", "subtype": "WidgetsTabber", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68654950099a11833ea6094a", "title": "Monthly Value", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["year", "Value"], "dimensions": ["[awards.year]", "[awards].[year]"], "measures": ["[awards].[month_current]", "[awards].[value]"]}}, {"id": "68654950099a11833ea6094b", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Mode", "Monthly % Change", "Years in MonthStart"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]", "[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[month_current]", "[awards].[value]"]}}, {"id": "68654950099a11833ea6094c", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Mode", "YTD % Change", "Years in MonthStart"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]", "[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[ytd_value]", "[awards].[month_current]"]}}, {"id": "68654950099a11833ea6094d", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Mode", "TTM % Change from Same Month Last Year", "Years in MonthStart"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]", "[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[month_current]", "[awards].[ttm]"]}}, {"id": "68654950099a11833ea6094e", "title": "Monthly Value by Mode", "type": "chart/pie", "subtype": "pie/donut", "isMap": false, "jaql": {"titles": ["Mode", "Value of Awards"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]"], "measures": ["[awards].[month_current]", "[awards].[value]", "[awards].[yeardum]"]}}, {"id": "68654950099a11833ea6094f", "title": "TTM Value by Mode", "type": "chart/pie", "subtype": "pie/donut", "isMap": false, "jaql": {"titles": ["Mode", "Value of Awards"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]"], "measures": ["[awards].[month_current]", "[awards].[ttm]", "[awards].[yeardum]"]}}, {"id": "68654950099a11833ea60950", "title": null, "type": "BloX", "subtype": "BloX", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68654950099a11833ea60951", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68654950099a11833ea60952", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68654950099a11833ea60953", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Mode", "Annual Value", "Years in MonthStart"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]", "[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[value]"]}}, {"id": "68654950099a11833ea60954", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["Mode", "Annual % Change", "Years in MonthStart"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]", "[awards.MonthStart (Calendar)]", "[awards].[MonthStart]"], "measures": ["[awards].[value]"]}}, {"id": "68654950099a11833ea60955", "title": "2024 Annual Value by Mode", "type": "chart/pie", "subtype": "pie/donut", "isMap": false, "jaql": {"titles": ["Mode", "Value of Awards", "year"], "dimensions": ["[awards.major_mode]", "[awards].[major_mode]", "[awards.year]", "[awards].[year]"], "measures": ["[awards].[value]"]}}, {"id": "68654950099a11833ea60956", "title": "Annual Value", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["year", "Value"], "dimensions": ["[awards.year]", "[awards].[year]"], "measures": ["[awards].[value]"]}}, {"id": "68654950099a11833ea60957", "title": "Annual Number of Contract Awards", "type": "chart/column", "subtype": "column/classic", "isMap": false, "jaql": {"titles": ["year", "Value"], "dimensions": ["[awards.year]", "[awards].[year]"], "measures": ["[awards].[numpro]"]}}, {"id": "68654950099a11833ea60958", "title": null, "type": "pivot2", "subtype": "pivot", "isMap": false, "jaql": {"titles": ["State", "Annual Value", "year", "Year"], "dimensions": ["[Geography.state]", "[Geography].[state]", "[awards.year]", "[awards].[year]"], "measures": ["[awards].[value]"]}}, {"id": "68654950099a11833ea60959", "title": "2024 Value by State", "type": "map/area", "subtype": "areamap/usa", "isMap": true, "jaql": {"titles": ["State", "Value of Awards:"], "dimensions": ["[Geography.state]", "[Geography].[state]"], "measures": ["[awards].[value]", "[awards].[year]"]}}, {"id": "68654950099a11833ea6095a", "title": null, "type": "chart/bar", "subtype": "bar/classic", "isMap": false, "jaql": {"titles": ["# of unique Major Mode"], "dimensions": [], "measures": ["[awards].[month_current]", "[awards].[value]", "[awards].[yeardum]"]}}, {"id": "68654950099a11833ea6095b", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "68654950099a11833ea6095c", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}, {"id": "6887a188523d2964a1dd694c", "title": null, "type": "filterWidget", "subtype": "filterWidget", "isMap": false, "jaql": {"titles": ["state"], "dimensions": ["[awards.state]", "[awards].[state]"], "measures": []}}, {"id": "6888e4fa523d2964a1dd6c18", "title": "RICHTEXT_MAIN.TITLE", "type": "richtexteditor", "subtype": "richtexteditor", "isMap": false, "jaql": {"titles": [], "dimensions": [], "measures": []}}], "mapWidgets": [{"id": "68654950099a11833ea6093a", "title": "Monthly Value by State", "subtype": "areamap/usa"}, {"id": "68654950099a11833ea60940", "title": "YTD Value by State", "subtype": "areamap/usa"}, {"id": "68654950099a11833ea60948", "title": "TTM Value of Contract Awards by State", "subtype": "areamap/usa"}, {"id": "68654950099a11833ea60959", "title": "2024 Value by State", "subtype": "areamap/usa"}]}