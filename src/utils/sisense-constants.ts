/**
 * Sisense Constants and Utilities
 * Centralized constants and helper functions for Sisense widget management
 */

// Widget IDs to exclude from rendering (duplicates, unwanted widgets, etc.)
export const EXCLUDED_WIDGET_IDS: string[] = [
  // Add specific widget IDs here that should be excluded
  // Example: '507f1f77bcf86cd799439011'
];

/**
 * Check if a widget is a BloX widget (should be excluded)
 */
export function isBloX(widget: any): boolean {
  return widget?.type === 'BloX' || widget?.widgetType === 'BloX';
}

/**
 * Check if a widget is a WidgetsTabber (tab container)
 */
export function isTabber(widget: any): boolean {
  return widget?.type === 'WidgetsTabber' || widget?.widgetType === 'WidgetsTabber';
}

/**
 * Check if a widget is a filter widget
 */
export function isFilterWidget(widget: any): boolean {
  return widget?.type === 'filterWidget' || widget?.widgetType === 'filterWidget';
}

/**
 * Parse tab script content from WidgetsTabber widgets
 * Extracts tab structure for rendering
 */
export function parseTabScript(script: string): { name: string; widgets: string[] }[] {
  try {
    // This would need to be implemented based on your specific tab script format
    // For now, return empty array
    return [];
  } catch (error) {
    console.error('Error parsing tab script:', error);
    return [];
  }
}

/**
 * Check if a widget should be excluded from rendering
 */
export function shouldExcludeWidget(widget: any): boolean {
  if (!widget) return true;
  
  // Exclude by ID
  if (EXCLUDED_WIDGET_IDS.includes(widget.oid || widget.id)) {
    return true;
  }
  
  // Exclude BloX widgets
  if (isBloX(widget)) {
    return true;
  }
  
  // Exclude filter widgets
  if (isFilterWidget(widget)) {
    return true;
  }
  
  return false;
}

/**
 * Filter widgets array to remove excluded widgets
 */
export function filterWidgets(widgets: any[]): any[] {
  return widgets.filter(widget => !shouldExcludeWidget(widget));
}