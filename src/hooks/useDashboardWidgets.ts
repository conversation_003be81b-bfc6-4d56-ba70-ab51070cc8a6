import { useState, useEffect, useCallback, useRef } from 'react';
import type { 
  ApiWidget, 
  TabDefinition, 
  DashboardWidgetsResponse
} from '@/types/sisense';
import { fetchDashboardWidgets } from '@/services/sisenseApi';
import { LOADING_STATES, PERFORMANCE_CONFIG } from '@/constants/sisense';

type LoadingState = typeof LOADING_STATES[keyof typeof LOADING_STATES];

// Hook state interface
interface UseDashboardWidgetsState {
  widgets: ApiWidget[];
  tabs: TabDefinition[];
  isLoading: boolean;
  error: string | null;
  loadingState: LoadingState;
}

// Hook options interface
interface UseDashboardWidgetsOptions {
  enabled?: boolean;
  refetchInterval?: number;
  retryOnError?: boolean;
  maxRetries?: number;
  onSuccess?: (data: DashboardWidgetsResponse) => void;
  onError?: (error: string) => void;
}

// Hook return interface
interface UseDashboardWidgetsReturn extends UseDashboardWidgetsState {
  refetch: () => Promise<void>;
  reset: () => void;
  isRefetching: boolean;
  lastFetchTime: number | null;
}

export const useDashboardWidgets = (
  dashboardId: string | null,
  options: UseDashboardWidgetsOptions = {}
): UseDashboardWidgetsReturn => {
  const {
    enabled = true,
    refetchInterval,
    retryOnError = true,
    maxRetries = 3,
    onSuccess,
    onError,
  } = options;

  // State management
  const [state, setState] = useState<UseDashboardWidgetsState>({
    widgets: [],
    tabs: [],
    isLoading: false,
    error: null,
    loadingState: LOADING_STATES.IDLE,
  });

  const [isRefetching, setIsRefetching] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState<number | null>(null);

  // Refs for cleanup and cancellation
  const abortControllerRef = useRef<AbortController | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const refetchIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef(0);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
    if (refetchIntervalRef.current) {
      clearInterval(refetchIntervalRef.current);
      refetchIntervalRef.current = null;
    }
  }, []);

  // Memoize stable callback references to prevent unnecessary re-renders
  const stableOnSuccess = useCallback((response: DashboardWidgetsResponse) => {
    onSuccess?.(response);
  }, [onSuccess]);

  const stableOnError = useCallback((error: string) => {
    onError?.(error);
  }, [onError]);

  // Fetch widgets function with optimized dependencies
  const fetchWidgets = useCallback(async (isRefetch = false): Promise<void> => {
    if (!dashboardId || !enabled) return;

    // Cancel any ongoing request
    cleanup();

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      setState(prev => ({
        ...prev,
        isLoading: !isRefetch,
        loadingState: LOADING_STATES.LOADING,
        error: null,
      }));

      if (isRefetch) {
        setIsRefetching(true);
      }

      const response = await fetchDashboardWidgets(dashboardId);

      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      if (response.error) {
        throw new Error(response.error);
      }

      setState({
        widgets: response.widgets,
        tabs: response.tabs,
        isLoading: false,
        error: null,
        loadingState: LOADING_STATES.SUCCESS,
      });

      setLastFetchTime(Date.now());
      retryCountRef.current = 0;

      stableOnSuccess(response);
    } catch (error) {
      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
        loadingState: LOADING_STATES.ERROR,
      }));

      stableOnError(errorMessage);

      // Retry logic
      if (retryOnError && retryCountRef.current < maxRetries) {
        retryCountRef.current += 1;
        const retryDelay = Math.min(1000 * Math.pow(2, retryCountRef.current - 1), 10000);

        retryTimeoutRef.current = setTimeout(() => {
          fetchWidgets(isRefetch);
        }, retryDelay);
      }
    } finally {
      if (isRefetch) {
        setIsRefetching(false);
      }
    }
  }, [dashboardId, enabled, retryOnError, maxRetries, stableOnSuccess, stableOnError, cleanup]);

  // Manual refetch function
  const refetch = useCallback(async (): Promise<void> => {
    await fetchWidgets(true);
  }, [fetchWidgets]);

  // Reset function
  const reset = useCallback(() => {
    cleanup();
    setState({
      widgets: [],
      tabs: [],
      isLoading: false,
      error: null,
      loadingState: LOADING_STATES.IDLE,
    });
    setIsRefetching(false);
    setLastFetchTime(null);
    retryCountRef.current = 0;
  }, [cleanup]);

  // Effect for initial fetch and dashboard ID changes
  useEffect(() => {
    if (dashboardId && enabled) {
      fetchWidgets();
    } else {
      reset();
    }

    return cleanup;
  }, [dashboardId, enabled]); // Removed function dependencies to prevent infinite loops

  // Effect for refetch interval
  useEffect(() => {
    if (refetchInterval && refetchInterval > 0 && dashboardId && enabled) {
      refetchIntervalRef.current = setInterval(() => {
        fetchWidgets(true);
      }, refetchInterval);

      return () => {
        if (refetchIntervalRef.current) {
          clearInterval(refetchIntervalRef.current);
          refetchIntervalRef.current = null;
        }
      };
    }
  }, [refetchInterval, dashboardId, enabled]); // Removed fetchWidgets to prevent infinite loops

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, []); // Empty dependency array - cleanup only on unmount

  return {
    ...state,
    refetch,
    reset,
    isRefetching,
    lastFetchTime,
  };
};

// Hook for managing multiple dashboards
interface UseMultipleDashboardsOptions {
  enabled?: boolean;
  maxConcurrent?: number;
  cacheTimeout?: number;
}

interface DashboardCache {
  data: DashboardWidgetsResponse;
  timestamp: number;
}

export const useMultipleDashboards = (
  dashboardIds: string[],
  options: UseMultipleDashboardsOptions = {}
) => {
  const {
    enabled = true,
    maxConcurrent = 3,
    cacheTimeout = PERFORMANCE_CONFIG.CACHE_TTL,
  } = options;

  const [cache, setCache] = useState<Map<string, DashboardCache>>(new Map());
  const [loadingStates, setLoadingStates] = useState<Map<string, boolean>>(new Map());
  const [errors, setErrors] = useState<Map<string, string>>(new Map());

  const fetchDashboard = useCallback(async (dashboardId: string) => {
    // Check cache first
    const cached = cache.get(dashboardId);
    if (cached && Date.now() - cached.timestamp < cacheTimeout) {
      return cached.data;
    }

    setLoadingStates(prev => new Map(prev).set(dashboardId, true));
    setErrors(prev => {
      const newErrors = new Map(prev);
      newErrors.delete(dashboardId);
      return newErrors;
    });

    try {
      const response = await fetchDashboardWidgets(dashboardId);
      
      if (response.error) {
        throw new Error(response.error);
      }

      // Update cache
      setCache(prev => new Map(prev).set(dashboardId, {
        data: response,
        timestamp: Date.now(),
      }));

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setErrors(prev => new Map(prev).set(dashboardId, errorMessage));
      throw error;
    } finally {
      setLoadingStates(prev => {
        const newStates = new Map(prev);
        newStates.delete(dashboardId);
        return newStates;
      });
    }
  }, [cache, cacheTimeout]);

  const fetchAllDashboards = useCallback(async () => {
    if (!enabled || dashboardIds.length === 0) return;

    // Process dashboards in batches to avoid overwhelming the API
    const batches: string[][] = [];
    for (let i = 0; i < dashboardIds.length; i += maxConcurrent) {
      batches.push(dashboardIds.slice(i, i + maxConcurrent));
    }

    for (const batch of batches) {
      await Promise.allSettled(
        batch.map(dashboardId => fetchDashboard(dashboardId))
      );
    }
  }, [enabled, dashboardIds, maxConcurrent]); // Removed fetchDashboard to prevent infinite loops

  // Effect for initial fetch
  useEffect(() => {
    fetchAllDashboards();
  }, []); // Empty dependency array - only run once on mount

  // Get dashboard data from cache
  const getDashboardData = useCallback((dashboardId: string): DashboardWidgetsResponse | null => {
    const cached = cache.get(dashboardId);
    if (cached && Date.now() - cached.timestamp < cacheTimeout) {
      return cached.data;
    }
    return null;
  }, [cache, cacheTimeout]);

  return {
    getDashboardData,
    fetchDashboard,
    fetchAllDashboards,
    isLoading: (dashboardId: string) => loadingStates.get(dashboardId) || false,
    getError: (dashboardId: string) => errors.get(dashboardId) || null,
    clearCache: () => setCache(new Map()),
    cacheSize: cache.size,
  };
};