import { useState, useEffect, useCallback } from 'react';

// Generic hook for managing state with localStorage persistence
export function useLocalStorageState<T>(
  key: string,
  defaultValue: T,
  options: {
    serialize?: (value: T) => string;
    deserialize?: (value: string) => T;
  } = {}
): [T, (value: T | ((prev: T) => T)) => void] {
  const {
    serialize = JSON.stringify,
    deserialize = JSON.parse,
  } = options;

  // Initialize state from localStorage or default value
  const [state, setState] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? deserialize(item) : defaultValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return defaultValue;
    }
  });

  // Update localStorage when state changes
  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      setState(prevState => {
        const newValue = typeof value === 'function' ? (value as (prev: T) => T)(prevState) : value;
        
        try {
          window.localStorage.setItem(key, serialize(newValue));
        } catch (error) {
          console.warn(`Error setting localStorage key "${key}":`, error);
        }
        
        return newValue;
      });
    } catch (error) {
      console.warn(`Error updating state for localStorage key "${key}":`, error);
    }
  }, [key, serialize]);

  // Listen for localStorage changes from other tabs/windows
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setState(deserialize(e.newValue));
        } catch (error) {
          console.warn(`Error deserializing localStorage key "${key}":`, error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, deserialize]);

  return [state, setValue];
}

// Enhanced dashboard preferences interface
export interface DashboardPreferences {
  activeDashboardId: string;
  collapsedTabs: string[];
  widgetSizes: Record<string, { width: number; height: number }>;
  lastRefreshTime: number;
  // New preference fields
  gridColumns: Record<string, number>; // Dashboard-specific column preferences
  autoRefreshEnabled: boolean;
  refreshInterval: number;
  showWidgetTitles: boolean;
  showWidgetIds: boolean;
  compactMode: boolean;
  theme: 'light' | 'dark' | 'system';
  // Schema version for migrations
  schemaVersion: number;
}

// Current schema version
const CURRENT_SCHEMA_VERSION = 2;

// Migration functions
const migrateDashboardPreferences = (preferences: any): DashboardPreferences => {
  const version = preferences.schemaVersion || 1;
  
  // Migration from v1 to v2
  if (version < 2) {
    return {
      ...preferences,
      gridColumns: preferences.gridColumns || {},
      autoRefreshEnabled: preferences.autoRefreshEnabled ?? false,
      refreshInterval: preferences.refreshInterval || 300000, // 5 minutes
      showWidgetTitles: preferences.showWidgetTitles ?? true,
      showWidgetIds: preferences.showWidgetIds ?? false,
      compactMode: preferences.compactMode ?? false,
      theme: preferences.theme || 'system',
      schemaVersion: 2,
    };
  }
  
  return preferences;
};

export function useDashboardPreferences(defaultDashboardId: string) {
  const defaultPreferences: DashboardPreferences = {
    activeDashboardId: defaultDashboardId,
    collapsedTabs: [],
    widgetSizes: {},
    lastRefreshTime: 0,
    gridColumns: {},
    autoRefreshEnabled: false,
    refreshInterval: 300000, // 5 minutes
    showWidgetTitles: true,
    showWidgetIds: false,
    compactMode: false,
    theme: 'system',
    schemaVersion: CURRENT_SCHEMA_VERSION,
  };

  return useLocalStorageState('sisense-dashboard-preferences', defaultPreferences, {
    deserialize: (value: string) => {
      const parsed = JSON.parse(value);
      return migrateDashboardPreferences(parsed);
    }
  });
}

// Hook for widget-specific preferences
export interface WidgetPreferences {
  collapsed: boolean;
  customHeight?: number;
  customWidth?: number;
  position?: { x: number; y: number };
  lastInteraction: number;
}

export function useWidgetPreferences(widgetId: string) {
  const defaultPreferences: WidgetPreferences = {
    collapsed: false,
    lastInteraction: 0,
  };

  return useLocalStorageState(`sisense-widget-${widgetId}`, defaultPreferences);
}

// Hook for dashboard layout preferences
export interface DashboardLayoutPreferences {
  layout: 'grid' | 'list' | 'masonry';
  density: 'compact' | 'comfortable' | 'spacious';
  sortBy: 'default' | 'title' | 'type' | 'lastModified';
  sortOrder: 'asc' | 'desc';
  filters: {
    showOnlyFavorites: boolean;
    hideErrorWidgets: boolean;
    widgetTypes: string[];
  };
  schemaVersion: number;
}

const migrateDashboardLayoutPreferences = (preferences: any): DashboardLayoutPreferences => {
  const version = preferences.schemaVersion || 1;
  
  if (version < 2) {
    return {
      ...preferences,
      filters: preferences.filters || {
        showOnlyFavorites: false,
        hideErrorWidgets: false,
        widgetTypes: [],
      },
      schemaVersion: 2,
    };
  }
  
  return preferences;
};

export function useDashboardLayoutPreferences(dashboardId: string) {
  const defaultPreferences: DashboardLayoutPreferences = {
    layout: 'grid',
    density: 'comfortable',
    sortBy: 'default',
    sortOrder: 'asc',
    filters: {
      showOnlyFavorites: false,
      hideErrorWidgets: false,
      widgetTypes: [],
    },
    schemaVersion: CURRENT_SCHEMA_VERSION,
  };

  return useLocalStorageState(`sisense-layout-${dashboardId}`, defaultPreferences, {
    deserialize: (value: string) => {
      const parsed = JSON.parse(value);
      return migrateDashboardLayoutPreferences(parsed);
    }
  });
}

// Hook for application-wide preferences
export interface AppPreferences {
  debugMode: boolean;
  performanceMonitoring: boolean;
  errorReporting: boolean;
  analyticsEnabled: boolean;
  keyboardShortcuts: boolean;
  animations: boolean;
  reducedMotion: boolean;
  highContrast: boolean;
  fontSize: 'small' | 'medium' | 'large';
  language: string;
  timezone: string;
  schemaVersion: number;
}

const migrateAppPreferences = (preferences: any): AppPreferences => {
  const version = preferences.schemaVersion || 1;
  
  if (version < 2) {
    return {
      ...preferences,
      reducedMotion: preferences.reducedMotion ?? false,
      highContrast: preferences.highContrast ?? false,
      fontSize: preferences.fontSize || 'medium',
      language: preferences.language || 'en',
      timezone: preferences.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      schemaVersion: 2,
    };
  }
  
  return preferences;
};

export function useAppPreferences() {
  const defaultPreferences: AppPreferences = {
    debugMode: process.env.NODE_ENV === 'development',
    performanceMonitoring: false,
    errorReporting: true,
    analyticsEnabled: false,
    keyboardShortcuts: true,
    animations: true,
    reducedMotion: false,
    highContrast: false,
    fontSize: 'medium',
    language: 'en',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    schemaVersion: CURRENT_SCHEMA_VERSION,
  };

  return useLocalStorageState('sisense-app-preferences', defaultPreferences, {
    deserialize: (value: string) => {
      const parsed = JSON.parse(value);
      return migrateAppPreferences(parsed);
    }
  });
}

// Utility functions for preference management
export const preferenceUtils = {
  // Clear all preferences
  clearAllPreferences: () => {
    const keys = Object.keys(localStorage).filter(key => 
      key.startsWith('sisense-')
    );
    keys.forEach(key => localStorage.removeItem(key));
  },

  // Export preferences to JSON
  exportPreferences: () => {
    const preferences: Record<string, any> = {};
    const keys = Object.keys(localStorage).filter(key => 
      key.startsWith('sisense-')
    );
    
    keys.forEach(key => {
      try {
        preferences[key] = JSON.parse(localStorage.getItem(key) || '{}');
      } catch (error) {
        console.warn(`Error parsing preference ${key}:`, error);
      }
    });
    
    return JSON.stringify(preferences, null, 2);
  },

  // Import preferences from JSON
  importPreferences: (jsonString: string) => {
    try {
      const preferences = JSON.parse(jsonString);
      Object.entries(preferences).forEach(([key, value]) => {
        if (key.startsWith('sisense-')) {
          localStorage.setItem(key, JSON.stringify(value));
        }
      });
      return true;
    } catch (error) {
      console.error('Error importing preferences:', error);
      return false;
    }
  },

  // Get preference storage usage
  getStorageUsage: () => {
    const keys = Object.keys(localStorage).filter(key => 
      key.startsWith('sisense-')
    );
    
    let totalSize = 0;
    const breakdown: Record<string, number> = {};
    
    keys.forEach(key => {
      const value = localStorage.getItem(key) || '';
      const size = new Blob([value]).size;
      totalSize += size;
      breakdown[key] = size;
    });
    
    return {
      totalSize,
      totalSizeFormatted: `${(totalSize / 1024).toFixed(2)} KB`,
      breakdown,
      keyCount: keys.length
    };
  },

  // Validate preference schema
  validatePreferences: () => {
    const keys = Object.keys(localStorage).filter(key => 
      key.startsWith('sisense-')
    );
    
    const issues: string[] = [];
    
    keys.forEach(key => {
      try {
        const value = JSON.parse(localStorage.getItem(key) || '{}');
        
        // Check for schema version
        if (typeof value === 'object' && value !== null) {
          if (!value.schemaVersion) {
            issues.push(`${key}: Missing schema version`);
          } else if (value.schemaVersion < CURRENT_SCHEMA_VERSION) {
            issues.push(`${key}: Outdated schema version ${value.schemaVersion}`);
          }
        }
      } catch (error) {
        issues.push(`${key}: Invalid JSON format`);
      }
    });
    
    return {
      isValid: issues.length === 0,
      issues
    };
  }
};