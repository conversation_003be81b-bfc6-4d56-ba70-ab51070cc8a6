import { useState, useEffect, useCallback, useRef } from 'react';
import type { 
  DashboardConfig, 
  ApiWidget, 
  TabDefinition, 
  SisenseErrorInfo,
  DashboardState 
} from '@/types/sisense';
import { SisenseErrorType } from '@/types/sisense';
import { useDashboardMetadata } from './useDashboardMetadata';
import { useDashboardWidgets } from './useDashboardWidgets';
import { useDashboardCache } from './useDashboardMetadata';
import { PERFORMANCE_CONFIG } from '@/constants/sisense';
import { DEFAULT_DASHBOARD_CONFIG } from '@/config/dashboards';

// Dashboard manager options
interface UseDashboardManagerOptions {
  autoLoadWidgets?: boolean;
  enableCaching?: boolean;
  enableHealthCheck?: boolean;
  refreshInterval?: number;
  maxRetries?: number;
  onDashboardChange?: (dashboardId: string) => void;
  onError?: (error: SisenseErrorInfo) => void;
  onLoadingChange?: (isLoading: boolean) => void;
}

// Dashboard manager return interface
interface UseDashboardManagerReturn {
  // State
  dashboards: DashboardConfig[];
  selectedDashboardId: string | null;
  selectedDashboard: DashboardConfig | undefined;
  widgets: ApiWidget[];
  tabs: TabDefinition[];
  
  // Loading states
  isLoadingMetadata: boolean;
  isLoadingWidgets: boolean;
  isRefreshing: boolean;
  
  // Error states
  metadataError: SisenseErrorInfo | null;
  widgetsError: string | null;
  
  // Health and config
  healthStatus: 'unknown' | 'healthy' | 'unhealthy';
  configValidation: { isValid: boolean; errors: string[] };
  
  // Actions
  selectDashboard: (dashboardId: string) => void;
  refreshAll: () => Promise<void>;
  refreshMetadata: () => Promise<void>;
  refreshWidgets: () => Promise<void>;
  retryLastOperation: () => Promise<void>;
  clearErrors: () => void;
  reset: () => void;
  
  // Utilities
  getDashboardById: (id: string) => DashboardConfig | undefined;
  getDashboardBySlug: (slug: string) => DashboardConfig | undefined;
  getNextDashboard: () => DashboardConfig | undefined;
  getPreviousDashboard: () => DashboardConfig | undefined;
  
  // Cache management
  cacheStats: {
    size: number;
    maxSize: number;
    entries: Array<{
      dashboardId: string;
      timestamp: number;
      age: number;
      isExpired: boolean;
    }>;
  };
  clearCache: () => void;
}

export const useDashboardManager = (
  options: UseDashboardManagerOptions = {}
): UseDashboardManagerReturn => {
  const {
    autoLoadWidgets = true,
    enableCaching = true,
    enableHealthCheck = true,
    refreshInterval,
    maxRetries = DEFAULT_DASHBOARD_CONFIG.maxRetries,
    onDashboardChange,
    onError,
    onLoadingChange
  } = options;

  // State for coordinating between hooks
  const [isRefreshingAll, setIsRefreshingAll] = useState(false);
  const lastOperationRef = useRef<(() => Promise<void>) | null>(null);

  // Dashboard metadata hook
  const {
    dashboards,
    selectedDashboardId,
    isLoading: isLoadingMetadata,
    isRefreshing: isRefreshingMetadata,
    error: metadataError,
    healthStatus,
    configValidation,
    selectDashboard: selectDashboardMetadata,
    refreshMetadata,
    getDashboardById,
    getDashboardBySlug,
    getNextDashboard,
    getPreviousDashboard,
    retryLastOperation: retryMetadataOperation,
    clearError: clearMetadataError,
    reset: resetMetadata
  } = useDashboardMetadata({
    autoSelect: true,
    persistSelection: true,
    enableHealthCheck,
    refreshInterval,
    maxRetries,
    onDashboardChange,
    onError
  });

  // Dashboard widgets hook
  const {
    widgets,
    tabs,
    isLoading: isLoadingWidgets,
    isRefetching: isRefreshingWidgets,
    error: widgetsError,
    refetch: refreshWidgets,
    reset: resetWidgets
  } = useDashboardWidgets(selectedDashboardId, {
    enabled: autoLoadWidgets && !!selectedDashboardId,
    retryOnError: true,
    maxRetries,
    onError: (error) => {
      const sisenseError: SisenseErrorInfo = {
        type: SisenseErrorType.WIDGET_LOAD,
        message: error,
        originalError: new Error(error)
      };
      onError?.(sisenseError);
    }
  });

  // Dashboard cache hook
  const {
    getCachedDashboard,
    setCachedDashboard,
    clearCache,
    getCacheStats
  } = useDashboardCache({
    maxCacheSize: 10,
    cacheTimeout: PERFORMANCE_CONFIG.CACHE_TTL
  });

  // Get selected dashboard
  const selectedDashboard = selectedDashboardId ? getDashboardById(selectedDashboardId) : undefined;

  // Enhanced dashboard selection with caching
  const selectDashboard = useCallback(async (dashboardId: string) => {
    try {
      // Check cache first if caching is enabled
      if (enableCaching) {
        const cached = getCachedDashboard(dashboardId);
        if (cached) {
          // Use cached data temporarily while fetching fresh data
          selectDashboardMetadata(dashboardId);
          return;
        }
      }

      // Select dashboard and trigger widget loading
      selectDashboardMetadata(dashboardId);

      // Cache the dashboard data after successful load
      if (enableCaching && widgets.length > 0) {
        const dashboard = getDashboardById(dashboardId);
        if (dashboard) {
          setCachedDashboard(dashboardId, {
            metadata: dashboard,
            widgets,
            tabs
          });
        }
      }
    } catch (error) {
      const sisenseError: SisenseErrorInfo = {
        type: SisenseErrorType.DASHBOARD_LOAD,
        message: error instanceof Error ? error.message : 'Failed to select dashboard',
        originalError: error instanceof Error ? error : undefined
      };
      onError?.(sisenseError);
    }
  }, [
    enableCaching,
    getCachedDashboard,
    selectDashboardMetadata,
    widgets,
    tabs,
    getDashboardById,
    setCachedDashboard,
    onError
  ]);

  // Refresh all data
  const refreshAll = useCallback(async (): Promise<void> => {
    lastOperationRef.current = refreshAll;
    setIsRefreshingAll(true);
    
    try {
      // Refresh metadata first
      await refreshMetadata();
      
      // Then refresh widgets if we have a selected dashboard
      if (selectedDashboardId) {
        await refreshWidgets();
      }
    } catch (error) {
      // Errors are handled by individual hooks
      throw error;
    } finally {
      setIsRefreshingAll(false);
    }
  }, [refreshMetadata, refreshWidgets, selectedDashboardId]);

  // Retry last operation
  const retryLastOperation = useCallback(async (): Promise<void> => {
    if (lastOperationRef.current) {
      await lastOperationRef.current();
    } else {
      // Try metadata operation first, then refresh all
      try {
        await retryMetadataOperation();
      } catch {
        await refreshAll();
      }
    }
  }, [refreshAll, retryMetadataOperation]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    clearMetadataError();
    // Note: widgets hook doesn't have a clear error function, 
    // but errors are cleared on successful refetch
  }, [clearMetadataError]);

  // Reset all state
  const reset = useCallback(() => {
    resetMetadata();
    resetWidgets();
    clearCache();
    setIsRefreshingAll(false);
    lastOperationRef.current = null;
  }, [resetMetadata, resetWidgets, clearCache]);

  // Effect to notify loading state changes
  useEffect(() => {
    const isLoading = isLoadingMetadata || isLoadingWidgets || isRefreshingAll;
    onLoadingChange?.(isLoading);
  }, [isLoadingMetadata, isLoadingWidgets, isRefreshingAll]); // Removed onLoadingChange to prevent infinite loops

  // Effect to cache dashboard data when widgets are loaded
  useEffect(() => {
    if (enableCaching && selectedDashboardId && selectedDashboard && widgets.length > 0) {
      setCachedDashboard(selectedDashboardId, {
        metadata: selectedDashboard,
        widgets,
        tabs
      });
    }
  }, [
    enableCaching,
    selectedDashboardId,
    selectedDashboard,
    widgets,
    tabs
    // Removed setCachedDashboard to prevent infinite loops
  ]);

  return {
    // State
    dashboards,
    selectedDashboardId,
    selectedDashboard,
    widgets,
    tabs,
    
    // Loading states
    isLoadingMetadata,
    isLoadingWidgets,
    isRefreshing: isRefreshingMetadata || isRefreshingWidgets || isRefreshingAll,
    
    // Error states
    metadataError,
    widgetsError,
    
    // Health and config
    healthStatus,
    configValidation,
    
    // Actions
    selectDashboard,
    refreshAll,
    refreshMetadata,
    refreshWidgets,
    retryLastOperation,
    clearErrors,
    reset,
    
    // Utilities
    getDashboardById,
    getDashboardBySlug,
    getNextDashboard,
    getPreviousDashboard,
    
    // Cache management
    cacheStats: getCacheStats(),
    clearCache
  };
};

// Hook for managing dashboard state across components
export const useDashboardState = () => {
  const [state, setState] = useState<DashboardState>({
    activeDashboardId: '',
    dashboards: [],
    widgetsByDashboard: {},
    tabsByDashboard: {},
    loadingStates: {},
    errors: {}
  });

  const updateDashboardState = useCallback((
    dashboardId: string,
    updates: Partial<{
      widgets: ApiWidget[];
      tabs: TabDefinition[];
      isLoading: boolean;
      error: string | null;
    }>
  ) => {
    setState(prev => ({
      ...prev,
      widgetsByDashboard: updates.widgets ? {
        ...prev.widgetsByDashboard,
        [dashboardId]: updates.widgets
      } : prev.widgetsByDashboard,
      tabsByDashboard: updates.tabs ? {
        ...prev.tabsByDashboard,
        [dashboardId]: updates.tabs
      } : prev.tabsByDashboard,
      loadingStates: updates.isLoading !== undefined ? {
        ...prev.loadingStates,
        [dashboardId]: updates.isLoading
      } : prev.loadingStates,
      errors: updates.error !== undefined ? {
        ...prev.errors,
        [dashboardId]: updates.error
      } : prev.errors
    }));
  }, []);

  const setActiveDashboard = useCallback((dashboardId: string) => {
    setState(prev => ({ ...prev, activeDashboardId: dashboardId }));
  }, []);

  const resetDashboardState = useCallback(() => {
    setState({
      activeDashboardId: '',
      dashboards: [],
      widgetsByDashboard: {},
      tabsByDashboard: {},
      loadingStates: {},
      errors: {}
    });
  }, []);

  return {
    state,
    updateDashboardState,
    setActiveDashboard,
    resetDashboardState
  };
};