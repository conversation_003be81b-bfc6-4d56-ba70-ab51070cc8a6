import { describe, it, expect, beforeEach } from 'vitest';

// Simple integration test to verify preferences functionality
describe('Preferences Integration', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  it('should store and retrieve dashboard preferences', () => {
    const testPrefs = {
      activeDashboardId: 'test-dashboard',
      autoRefreshEnabled: true,
      theme: 'dark',
      schemaVersion: 2,
    };

    // Store preferences
    localStorage.setItem('sisense-dashboard-preferences', JSON.stringify(testPrefs));

    // Retrieve preferences
    const stored = localStorage.getItem('sisense-dashboard-preferences');
    const parsed = stored ? JSON.parse(stored) : null;

    expect(parsed).toEqual(testPrefs);
    expect(parsed.activeDashboardId).toBe('test-dashboard');
    expect(parsed.autoRefreshEnabled).toBe(true);
    expect(parsed.theme).toBe('dark');
  });

  it('should handle widget preferences', () => {
    const widgetPrefs = {
      collapsed: false,
      customHeight: 400,
      lastInteraction: Date.now(),
    };

    localStorage.setItem('sisense-widget-123', JSON.stringify(widgetPrefs));

    const stored = localStorage.getItem('sisense-widget-123');
    const parsed = stored ? JSON.parse(stored) : null;

    expect(parsed).toEqual(widgetPrefs);
    expect(parsed.collapsed).toBe(false);
    expect(parsed.customHeight).toBe(400);
  });

  it('should handle layout preferences', () => {
    const layoutPrefs = {
      layout: 'grid',
      density: 'comfortable',
      sortBy: 'title',
      sortOrder: 'asc',
      filters: {
        showOnlyFavorites: false,
        hideErrorWidgets: true,
        widgetTypes: ['chart', 'table'],
      },
      schemaVersion: 2,
    };

    localStorage.setItem('sisense-layout-dashboard-1', JSON.stringify(layoutPrefs));

    const stored = localStorage.getItem('sisense-layout-dashboard-1');
    const parsed = stored ? JSON.parse(stored) : null;

    expect(parsed).toEqual(layoutPrefs);
    expect(parsed.layout).toBe('grid');
    expect(parsed.filters.hideErrorWidgets).toBe(true);
    expect(parsed.filters.widgetTypes).toEqual(['chart', 'table']);
  });

  it('should handle app preferences', () => {
    const appPrefs = {
      debugMode: false,
      performanceMonitoring: true,
      animations: true,
      fontSize: 'large',
      language: 'en',
      schemaVersion: 2,
    };

    localStorage.setItem('sisense-app-preferences', JSON.stringify(appPrefs));

    const stored = localStorage.getItem('sisense-app-preferences');
    const parsed = stored ? JSON.parse(stored) : null;

    expect(parsed).toEqual(appPrefs);
    expect(parsed.debugMode).toBe(false);
    expect(parsed.performanceMonitoring).toBe(true);
    expect(parsed.fontSize).toBe('large');
  });

  it('should handle preference migration scenarios', () => {
    // Simulate v1 preferences without schema version
    const v1Prefs = {
      activeDashboardId: 'old-dashboard',
      collapsedTabs: ['tab1', 'tab2'],
      widgetSizes: { widget1: { width: 100, height: 200 } },
    };

    localStorage.setItem('sisense-dashboard-preferences', JSON.stringify(v1Prefs));

    const stored = localStorage.getItem('sisense-dashboard-preferences');
    const parsed = stored ? JSON.parse(stored) : null;

    expect(parsed).toBeDefined();
    expect(parsed.activeDashboardId).toBe('old-dashboard');
    expect(parsed.collapsedTabs).toEqual(['tab1', 'tab2']);
    
    // Migration would add missing fields
    expect(parsed.schemaVersion).toBeUndefined(); // Would be added by migration logic
  });

  it('should handle JSON serialization edge cases', () => {
    const complexPrefs = {
      activeDashboardId: 'test',
      lastRefreshTime: Date.now(),
      widgetSizes: {
        'widget-with-special-chars!@#': { width: 100, height: 200 },
        'widget-with-unicode-🎯': { width: 150, height: 250 },
      },
      collapsedTabs: [],
      gridColumns: { 'dashboard-1': 3, 'dashboard-2': 4 },
      schemaVersion: 2,
    };

    localStorage.setItem('sisense-dashboard-preferences', JSON.stringify(complexPrefs));

    const stored = localStorage.getItem('sisense-dashboard-preferences');
    const parsed = stored ? JSON.parse(stored) : null;

    expect(parsed).toEqual(complexPrefs);
    expect(parsed.widgetSizes['widget-with-special-chars!@#']).toEqual({ width: 100, height: 200 });
    expect(parsed.widgetSizes['widget-with-unicode-🎯']).toEqual({ width: 150, height: 250 });
  });

  it('should handle storage quota and cleanup', () => {
    // Test storage with multiple preference keys
    const keys = [
      'sisense-dashboard-preferences',
      'sisense-widget-1',
      'sisense-widget-2',
      'sisense-layout-dashboard-1',
      'sisense-app-preferences',
    ];

    keys.forEach((key, index) => {
      const prefs = { id: key, data: `test-data-${index}`, schemaVersion: 2 };
      localStorage.setItem(key, JSON.stringify(prefs));
    });

    // Verify all keys are stored
    keys.forEach(key => {
      const stored = localStorage.getItem(key);
      expect(stored).toBeDefined();
      
      const parsed = JSON.parse(stored!);
      expect(parsed.id).toBe(key);
      expect(parsed.schemaVersion).toBe(2);
    });

    // Verify storage count
    expect(localStorage.length).toBeGreaterThanOrEqual(keys.length);
  });
});