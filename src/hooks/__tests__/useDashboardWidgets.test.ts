import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useDashboardWidgets } from '../useDashboardWidgets';

// Mock the API service
vi.mock('@/services/sisenseApi', () => ({
  fetchDashboardWidgets: vi.fn(),
}));

// Mock error logging service
vi.mock('@/services/errorLoggingService', () => ({
  errorLoggingService: {
    logNetworkError: vi.fn(),
  },
}));

describe('useDashboardWidgets', () => {
  let mockFetchDashboardWidgets: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetchDashboardWidgets = vi.mocked(await import('@/services/sisenseApi')).fetchDashboardWidgets;
  });

  it('returns initial state correctly', () => {
    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    expect(result.current.widgets).toEqual([]);
    expect(result.current.tabs).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.lastFetch).toBe(null);
  });

  it('fetches widgets successfully', async () => {
    const mockWidgets = [
      { oid: 'widget-1', title: 'Widget 1', type: 'chart' },
      { oid: 'widget-2', title: 'Widget 2', type: 'table' },
    ];
    const mockTabs = [
      { title: 'Tab 1', displayWidgetIds: ['widget-1'], hideWidgetIds: [] },
    ];

    mockFetchDashboardWidgets.mockResolvedValue({
      widgets: mockWidgets,
      tabs: mockTabs,
    });

    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    // Trigger fetch
    result.current.refetch();

    // Should be loading
    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.widgets).toEqual(mockWidgets);
    expect(result.current.tabs).toEqual(mockTabs);
    expect(result.current.error).toBe(null);
    expect(result.current.lastFetch).toBeInstanceOf(Date);
  });

  it('handles fetch errors correctly', async () => {
    const mockError = new Error('Network error');
    mockFetchDashboardWidgets.mockRejectedValue(mockError);

    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    // Trigger fetch
    result.current.refetch();

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.widgets).toEqual([]);
    expect(result.current.tabs).toEqual([]);
    expect(result.current.error).toBe('Network error');
  });

  it('auto-fetches when dashboardId changes', async () => {
    const mockWidgets1 = [{ oid: 'widget-1', title: 'Widget 1' }];
    const mockWidgets2 = [{ oid: 'widget-2', title: 'Widget 2' }];

    mockFetchDashboardWidgets
      .mockResolvedValueOnce({ widgets: mockWidgets1, tabs: [] })
      .mockResolvedValueOnce({ widgets: mockWidgets2, tabs: [] });

    const { result, rerender } = renderHook(
      ({ dashboardId }) => useDashboardWidgets(dashboardId, { autoFetch: true }),
      { initialProps: { dashboardId: 'dashboard-1' } }
    );

    await waitFor(() => {
      expect(result.current.widgets).toEqual(mockWidgets1);
    });

    // Change dashboard ID
    rerender({ dashboardId: 'dashboard-2' });

    await waitFor(() => {
      expect(result.current.widgets).toEqual(mockWidgets2);
    });

    expect(mockFetchDashboardWidgets).toHaveBeenCalledTimes(2);
    expect(mockFetchDashboardWidgets).toHaveBeenCalledWith('dashboard-1');
    expect(mockFetchDashboardWidgets).toHaveBeenCalledWith('dashboard-2');
  });

  it('does not auto-fetch when autoFetch is false', () => {
    renderHook(() => useDashboardWidgets('dashboard-1', { autoFetch: false }));

    expect(mockFetchDashboardWidgets).not.toHaveBeenCalled();
  });

  it('handles retry functionality', async () => {
    const mockError = new Error('Network error');
    const mockWidgets = [{ oid: 'widget-1', title: 'Widget 1' }];

    mockFetchDashboardWidgets
      .mockRejectedValueOnce(mockError)
      .mockResolvedValueOnce({ widgets: mockWidgets, tabs: [] });

    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    // First fetch fails
    result.current.refetch();

    await waitFor(() => {
      expect(result.current.error).toBe('Network error');
    });

    // Retry succeeds
    result.current.refetch();

    await waitFor(() => {
      expect(result.current.widgets).toEqual(mockWidgets);
      expect(result.current.error).toBe(null);
    });
  });

  it('clears error when clearError is called', async () => {
    const mockError = new Error('Network error');
    mockFetchDashboardWidgets.mockRejectedValue(mockError);

    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    // Trigger error
    result.current.refetch();

    await waitFor(() => {
      expect(result.current.error).toBe('Network error');
    });

    // Clear error
    result.current.clearError();

    expect(result.current.error).toBe(null);
  });

  it('handles concurrent requests correctly', async () => {
    const mockWidgets = [{ oid: 'widget-1', title: 'Widget 1' }];
    
    // Simulate slow response
    mockFetchDashboardWidgets.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({ widgets: mockWidgets, tabs: [] }), 100)
      )
    );

    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    // Trigger multiple concurrent requests
    result.current.refetch();
    result.current.refetch();
    result.current.refetch();

    await waitFor(() => {
      expect(result.current.widgets).toEqual(mockWidgets);
    });

    // Should only make one actual API call due to request deduplication
    expect(mockFetchDashboardWidgets).toHaveBeenCalledTimes(1);
  });

  it('cancels requests when component unmounts', async () => {
    const mockWidgets = [{ oid: 'widget-1', title: 'Widget 1' }];
    
    mockFetchDashboardWidgets.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({ widgets: mockWidgets, tabs: [] }), 100)
      )
    );

    const { result, unmount } = renderHook(() => useDashboardWidgets('dashboard-1'));

    // Start fetch
    result.current.refetch();

    // Unmount before completion
    unmount();

    // Wait for potential completion
    await new Promise(resolve => setTimeout(resolve, 150));

    // Should not update state after unmount
    expect(mockFetchDashboardWidgets).toHaveBeenCalledTimes(1);
  });

  it('respects cache options', async () => {
    const mockWidgets = [{ oid: 'widget-1', title: 'Widget 1' }];
    mockFetchDashboardWidgets.mockResolvedValue({ widgets: mockWidgets, tabs: [] });

    const { result } = renderHook(() => 
      useDashboardWidgets('dashboard-1', { 
        cacheTime: 5000,
        staleTime: 1000 
      })
    );

    // First fetch
    result.current.refetch();

    await waitFor(() => {
      expect(result.current.widgets).toEqual(mockWidgets);
    });

    // Second fetch within stale time should use cache
    result.current.refetch();

    expect(mockFetchDashboardWidgets).toHaveBeenCalledTimes(1);
  });

  it('handles empty dashboard ID gracefully', () => {
    const { result } = renderHook(() => useDashboardWidgets(''));

    expect(result.current.widgets).toEqual([]);
    expect(result.current.tabs).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);

    // Should not make API call with empty ID
    result.current.refetch();
    expect(mockFetchDashboardWidgets).not.toHaveBeenCalled();
  });

  it('provides loading state during fetch', async () => {
    mockFetchDashboardWidgets.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({ widgets: [], tabs: [] }), 100)
      )
    );

    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    expect(result.current.isLoading).toBe(false);

    // Start fetch
    result.current.refetch();

    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
  });

  it('handles enabled/disabled state correctly', () => {
    const { result } = renderHook(() => 
      useDashboardWidgets('dashboard-1', { enabled: false })
    );

    // Should not fetch when disabled
    result.current.refetch();
    expect(mockFetchDashboardWidgets).not.toHaveBeenCalled();
  });

  it('handles refetch interval correctly', async () => {
    vi.useFakeTimers();
    
    mockFetchDashboardWidgets.mockResolvedValue({ widgets: [], tabs: [] });

    renderHook(() => 
      useDashboardWidgets('dashboard-1', { 
        enabled: true,
        refetchInterval: 1000 
      })
    );

    // Initial fetch
    expect(mockFetchDashboardWidgets).toHaveBeenCalledTimes(1);

    // Fast forward time
    vi.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(mockFetchDashboardWidgets).toHaveBeenCalledTimes(2);
    });

    vi.useRealTimers();
  });

  it('calls success callback on successful fetch', async () => {
    const onSuccess = vi.fn();
    const mockData = { widgets: [{ oid: 'widget-1' }], tabs: [] };
    
    mockFetchDashboardWidgets.mockResolvedValue(mockData);

    const { result } = renderHook(() => 
      useDashboardWidgets('dashboard-1', { onSuccess })
    );

    result.current.refetch();

    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalledWith(mockData);
    });
  });

  it('calls error callback on failed fetch', async () => {
    const onError = vi.fn();
    const mockError = new Error('Network error');
    
    mockFetchDashboardWidgets.mockRejectedValue(mockError);

    const { result } = renderHook(() => 
      useDashboardWidgets('dashboard-1', { onError })
    );

    result.current.refetch();

    await waitFor(() => {
      expect(onError).toHaveBeenCalledWith('Network error');
    });
  });

  it('handles retry with exponential backoff', async () => {
    vi.useFakeTimers();
    
    const mockError = new Error('Network error');
    mockFetchDashboardWidgets
      .mockRejectedValueOnce(mockError)
      .mockRejectedValueOnce(mockError)
      .mockResolvedValueOnce({ widgets: [], tabs: [] });

    const { result } = renderHook(() => 
      useDashboardWidgets('dashboard-1', { 
        retryOnError: true,
        maxRetries: 3 
      })
    );

    // Start fetch
    result.current.refetch();

    // First retry after 1 second
    vi.advanceTimersByTime(1000);
    
    await waitFor(() => {
      expect(mockFetchDashboardWidgets).toHaveBeenCalledTimes(2);
    });

    // Second retry after 2 seconds
    vi.advanceTimersByTime(2000);
    
    await waitFor(() => {
      expect(mockFetchDashboardWidgets).toHaveBeenCalledTimes(3);
    });

    vi.useRealTimers();
  });

  it('provides isRefetching state correctly', async () => {
    mockFetchDashboardWidgets.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({ widgets: [], tabs: [] }), 100)
      )
    );

    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    expect(result.current.isRefetching).toBe(false);

    // Start refetch
    result.current.refetch();

    expect(result.current.isRefetching).toBe(true);

    await waitFor(() => {
      expect(result.current.isRefetching).toBe(false);
    });
  });

  it('provides lastFetchTime correctly', async () => {
    const mockData = { widgets: [], tabs: [] };
    mockFetchDashboardWidgets.mockResolvedValue(mockData);

    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    expect(result.current.lastFetchTime).toBe(null);

    result.current.refetch();

    await waitFor(() => {
      expect(result.current.lastFetchTime).toBeInstanceOf(Date);
    });
  });

  it('resets state correctly', () => {
    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    // Set some state
    result.current.refetch();

    // Reset
    result.current.reset();

    expect(result.current.widgets).toEqual([]);
    expect(result.current.tabs).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.lastFetchTime).toBe(null);
  });

  it('handles API response with error field', async () => {
    mockFetchDashboardWidgets.mockResolvedValue({
      widgets: [],
      tabs: [],
      error: 'API Error'
    });

    const { result } = renderHook(() => useDashboardWidgets('dashboard-1'));

    result.current.refetch();

    await waitFor(() => {
      expect(result.current.error).toBe('API Error');
    });
  });
});