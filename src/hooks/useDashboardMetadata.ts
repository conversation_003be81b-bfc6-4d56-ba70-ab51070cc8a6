import { useState, useEffect, useCallback, useRef } from 'react';
import type { DashboardConfig, SisenseErrorInfo } from '@/types/sisense';
import { SisenseErrorType } from '@/types/sisense';
import { IIJA_DASHBOARDS, DEFAULT_DASHBOARD_CONFIG } from '@/config/dashboards';
import { STORAGE_KEYS, ERROR_MESSAGES } from '@/constants/sisense';
import { checkSisenseHealth, validateSisenseConfig } from '@/services/sisenseApi';

// Dashboard metadata state interface
interface DashboardMetadataState {
  dashboards: DashboardConfig[];
  selectedDashboardId: string | null;
  isLoading: boolean;
  isRefreshing: boolean;
  error: SisenseErrorInfo | null;
  lastUpdated: number | null;
  lastRefreshTime: number | null;
  healthStatus: 'unknown' | 'healthy' | 'unhealthy';
  configValidation: { isValid: boolean; errors: string[] };
}

// Dashboard metadata options
interface UseDashboardMetadataOptions {
  autoSelect?: boolean;
  persistSelection?: boolean;
  enableHealthCheck?: boolean;
  refreshInterval?: number;
  maxRetries?: number;
  retryDelay?: number;
  onDashboardChange?: (dashboardId: string) => void;
  onError?: (error: SisenseErrorInfo) => void;
  onHealthChange?: (isHealthy: boolean) => void;
}

// Dashboard metadata return interface
interface UseDashboardMetadataReturn extends DashboardMetadataState {
  selectDashboard: (dashboardId: string) => void;
  refreshMetadata: () => Promise<void>;
  checkHealth: () => Promise<boolean>;
  validateConfig: () => { isValid: boolean; errors: string[] };
  getDashboardById: (id: string) => DashboardConfig | undefined;
  getDashboardBySlug: (slug: string) => DashboardConfig | undefined;
  getNextDashboard: () => DashboardConfig | undefined;
  getPreviousDashboard: () => DashboardConfig | undefined;
  retryLastOperation: () => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

// Local storage utilities
const loadSelectedDashboard = (): string | null => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.ACTIVE_DASHBOARD);
    return stored || null;
  } catch (error) {
    console.warn('Failed to load selected dashboard from localStorage:', error);
    return null;
  }
};

const saveSelectedDashboard = (dashboardId: string): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.ACTIVE_DASHBOARD, dashboardId);
  } catch (error) {
    console.warn('Failed to save selected dashboard to localStorage:', error);
  }
};

const loadDashboardMetadataCache = (): { dashboards: DashboardConfig[]; timestamp: number } | null => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.DASHBOARD_PREFERENCES);
    if (!stored) return null;
    
    const parsed = JSON.parse(stored);
    if (parsed.dashboards && parsed.timestamp) {
      return parsed;
    }
    return null;
  } catch (error) {
    console.warn('Failed to load dashboard metadata cache from localStorage:', error);
    return null;
  }
};

const saveDashboardMetadataCache = (dashboards: DashboardConfig[]): void => {
  try {
    const cacheData = {
      dashboards,
      timestamp: Date.now()
    };
    localStorage.setItem(STORAGE_KEYS.DASHBOARD_PREFERENCES, JSON.stringify(cacheData));
  } catch (error) {
    console.warn('Failed to save dashboard metadata cache to localStorage:', error);
  }
};

// Error creation utility
const createSisenseError = (
  type: keyof typeof SisenseErrorType,
  message: string,
  originalError?: Error
): SisenseErrorInfo => ({
  type: SisenseErrorType[type],
  message,
  originalError
});

export const useDashboardMetadata = (
  options: UseDashboardMetadataOptions = {}
): UseDashboardMetadataReturn => {
  const {
    autoSelect = true,
    persistSelection = true,
    enableHealthCheck = true,
    refreshInterval,
    maxRetries = DEFAULT_DASHBOARD_CONFIG.maxRetries,
    retryDelay = DEFAULT_DASHBOARD_CONFIG.retryDelay,
    onDashboardChange,
    onError,
    onHealthChange
  } = options;

  // State management
  const [state, setState] = useState<DashboardMetadataState>(() => {
    const savedDashboardId = persistSelection ? loadSelectedDashboard() : null;
    const defaultId = savedDashboardId || DEFAULT_DASHBOARD_CONFIG.defaultDashboardId;
    const cachedData = loadDashboardMetadataCache();
    const configValidation = validateSisenseConfig();
    
    return {
      dashboards: cachedData?.dashboards || IIJA_DASHBOARDS,
      selectedDashboardId: autoSelect ? defaultId : null,
      isLoading: false,
      isRefreshing: false,
      error: null,
      lastUpdated: cachedData?.timestamp || Date.now(),
      lastRefreshTime: null,
      healthStatus: 'unknown',
      configValidation
    };
  });

  // Refs for cleanup and retry logic
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef(0);
  const lastOperationRef = useRef<(() => Promise<void>) | null>(null);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
      refreshIntervalRef.current = null;
    }
  }, []);

  // Select dashboard function
  const selectDashboard = useCallback((dashboardId: string) => {
    const dashboard = state.dashboards.find(d => d.id === dashboardId);
    
    if (!dashboard) {
      const error = createSisenseError(
        'DASHBOARD_LOAD',
        `Dashboard with ID "${dashboardId}" not found`
      );
      setState(prev => ({ ...prev, error }));
      onError?.(error);
      return;
    }

    setState(prev => ({
      ...prev,
      selectedDashboardId: dashboardId,
      error: null
    }));

    if (persistSelection) {
      saveSelectedDashboard(dashboardId);
    }

    onDashboardChange?.(dashboardId);
  }, [state.dashboards, persistSelection, onDashboardChange, onError]);

  // Health check function
  const checkHealth = useCallback(async (): Promise<boolean> => {
    try {
      const isHealthy = await checkSisenseHealth();
      setState(prev => ({ ...prev, healthStatus: isHealthy ? 'healthy' : 'unhealthy' }));
      onHealthChange?.(isHealthy);
      return isHealthy;
    } catch (error) {
      setState(prev => ({ ...prev, healthStatus: 'unhealthy' }));
      onHealthChange?.(false);
      return false;
    }
  }, [onHealthChange]);

  // Config validation function
  const validateConfig = useCallback(() => {
    const validation = validateSisenseConfig();
    setState(prev => ({ ...prev, configValidation: validation }));
    return validation;
  }, []);

  // Refresh metadata function with retry logic
  const refreshMetadata = useCallback(async (): Promise<void> => {
    lastOperationRef.current = refreshMetadata;
    
    setState(prev => ({ 
      ...prev, 
      isRefreshing: true, 
      error: null 
    }));

    const attemptRefresh = async (attempt: number): Promise<void> => {
      try {
        // Validate configuration first
        const configValidation = validateSisenseConfig();
        if (!configValidation.isValid) {
          throw new Error(`Configuration invalid: ${configValidation.errors.join(', ')}`);
        }

        // Check health if enabled
        let healthStatus: 'healthy' | 'unhealthy' | 'unknown' = 'unknown';
        if (enableHealthCheck) {
          const isHealthy = await checkSisenseHealth();
          healthStatus = isHealthy ? 'healthy' : 'unhealthy';
          onHealthChange?.(isHealthy);
          
          if (!isHealthy) {
            throw new Error('Sisense service is not healthy');
          }
        }

        // Simulate metadata refresh (in real app, this would fetch from API)
        await new Promise(resolve => {
          timeoutRef.current = setTimeout(resolve, 500);
        });

        // Validate current selection against available dashboards
        const currentDashboard = state.selectedDashboardId 
          ? IIJA_DASHBOARDS.find(d => d.id === state.selectedDashboardId)
          : null;

        const refreshTime = Date.now();
        
        setState(prev => ({
          ...prev,
          dashboards: IIJA_DASHBOARDS,
          selectedDashboardId: currentDashboard ? prev.selectedDashboardId : IIJA_DASHBOARDS[0]?.id || null,
          isRefreshing: false,
          error: null,
          lastUpdated: refreshTime,
          lastRefreshTime: refreshTime,
          healthStatus,
          configValidation
        }));

        // Cache the updated metadata
        saveDashboardMetadataCache(IIJA_DASHBOARDS);
        retryCountRef.current = 0;

      } catch (error) {
        if (attempt < maxRetries) {
          retryCountRef.current = attempt;
          await new Promise(resolve => {
            timeoutRef.current = setTimeout(resolve, retryDelay * attempt);
          });
          return attemptRefresh(attempt + 1);
        }

        const sisenseError = createSisenseError(
          'DASHBOARD_LOAD',
          error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR,
          error instanceof Error ? error : undefined
        );

        setState(prev => ({
          ...prev,
          isRefreshing: false,
          error: sisenseError,
          healthStatus: 'unhealthy',
          configValidation: validateSisenseConfig()
        }));

        onError?.(sisenseError);
        throw sisenseError;
      }
    };

    await attemptRefresh(1);
  }, [state.selectedDashboardId, enableHealthCheck, maxRetries, retryDelay, onError, onHealthChange, checkHealth]);

  // Helper functions
  const getDashboardById = useCallback((id: string): DashboardConfig | undefined => {
    return state.dashboards.find(dashboard => dashboard.id === id);
  }, [state.dashboards]);

  const getDashboardBySlug = useCallback((slug: string): DashboardConfig | undefined => {
    return state.dashboards.find(dashboard => dashboard.slug === slug);
  }, [state.dashboards]);

  const getNextDashboard = useCallback((): DashboardConfig | undefined => {
    if (!state.selectedDashboardId) return state.dashboards[0];
    
    const currentIndex = state.dashboards.findIndex(d => d.id === state.selectedDashboardId);
    if (currentIndex === -1) return state.dashboards[0];
    
    const nextIndex = (currentIndex + 1) % state.dashboards.length;
    return state.dashboards[nextIndex];
  }, [state.dashboards, state.selectedDashboardId]);

  const getPreviousDashboard = useCallback((): DashboardConfig | undefined => {
    if (!state.selectedDashboardId) return state.dashboards[state.dashboards.length - 1];
    
    const currentIndex = state.dashboards.findIndex(d => d.id === state.selectedDashboardId);
    if (currentIndex === -1) return state.dashboards[state.dashboards.length - 1];
    
    const prevIndex = currentIndex === 0 ? state.dashboards.length - 1 : currentIndex - 1;
    return state.dashboards[prevIndex];
  }, [state.dashboards, state.selectedDashboardId]);

  // Retry last operation function
  const retryLastOperation = useCallback(async (): Promise<void> => {
    if (lastOperationRef.current) {
      await lastOperationRef.current();
    } else {
      await refreshMetadata();
    }
  }, [refreshMetadata]);

  // Clear error function
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Reset function
  const reset = useCallback(() => {
    cleanup();
    retryCountRef.current = 0;
    lastOperationRef.current = null;
    
    setState({
      dashboards: IIJA_DASHBOARDS,
      selectedDashboardId: autoSelect ? DEFAULT_DASHBOARD_CONFIG.defaultDashboardId : null,
      isLoading: false,
      isRefreshing: false,
      error: null,
      lastUpdated: Date.now(),
      lastRefreshTime: null,
      healthStatus: 'unknown',
      configValidation: validateSisenseConfig()
    });

    if (persistSelection) {
      try {
        localStorage.removeItem(STORAGE_KEYS.ACTIVE_DASHBOARD);
        localStorage.removeItem(STORAGE_KEYS.DASHBOARD_PREFERENCES);
      } catch (error) {
        console.warn('Failed to clear dashboard data from localStorage:', error);
      }
    }
  }, [autoSelect, persistSelection, cleanup]);

  // Effect for initial dashboard selection validation
  useEffect(() => {
    if (state.selectedDashboardId) {
      const dashboard = getDashboardById(state.selectedDashboardId);
      if (!dashboard) {
        // Selected dashboard doesn't exist, reset to default
        const defaultId = DEFAULT_DASHBOARD_CONFIG.defaultDashboardId;
        selectDashboard(defaultId);
      }
    }
  }, [state.selectedDashboardId]); // Removed function dependencies to prevent infinite loops

  // Effect for refresh interval
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(() => {
        refreshMetadata();
      }, refreshInterval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
          refreshIntervalRef.current = null;
        }
      };
    }
  }, [refreshInterval]); // Removed refreshMetadata to prevent infinite loops

  // Effect for initial health check
  useEffect(() => {
    if (enableHealthCheck && state.healthStatus === 'unknown') {
      checkHealth();
    }
  }, [enableHealthCheck, state.healthStatus]); // Removed checkHealth to prevent infinite loops

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, []); // Empty dependency array - cleanup only on unmount

  return {
    ...state,
    selectDashboard,
    refreshMetadata,
    checkHealth,
    validateConfig,
    getDashboardById,
    getDashboardBySlug,
    getNextDashboard,
    getPreviousDashboard,
    retryLastOperation,
    clearError,
    reset
  };
};

// Hook for managing dashboard cache
interface DashboardCacheEntry {
  metadata: DashboardConfig;
  widgets: any[];
  tabs: any[];
  timestamp: number;
}

interface UseDashboardCacheOptions {
  maxCacheSize?: number;
  cacheTimeout?: number;
}

export const useDashboardCache = (options: UseDashboardCacheOptions = {}) => {
  const { maxCacheSize = 10, cacheTimeout = 300000 } = options; // 5 minutes default

  const [cache, setCache] = useState<Map<string, DashboardCacheEntry>>(new Map());

  const getCachedDashboard = useCallback((dashboardId: string): DashboardCacheEntry | null => {
    const entry = cache.get(dashboardId);
    if (!entry) return null;

    // Check if cache entry is still valid
    if (Date.now() - entry.timestamp > cacheTimeout) {
      setCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(dashboardId);
        return newCache;
      });
      return null;
    }

    return entry;
  }, [cache, cacheTimeout]);

  const setCachedDashboard = useCallback((
    dashboardId: string, 
    data: Omit<DashboardCacheEntry, 'timestamp'>
  ) => {
    setCache(prev => {
      const newCache = new Map(prev);
      
      // Remove oldest entries if cache is full
      if (newCache.size >= maxCacheSize) {
        const oldestKey = Array.from(newCache.keys())[0];
        newCache.delete(oldestKey);
      }

      newCache.set(dashboardId, {
        ...data,
        timestamp: Date.now()
      });

      return newCache;
    });
  }, [maxCacheSize]);

  const clearCache = useCallback(() => {
    setCache(new Map());
  }, []);

  const getCacheStats = useCallback(() => {
    return {
      size: cache.size,
      maxSize: maxCacheSize,
      entries: Array.from(cache.entries()).map(([id, entry]) => ({
        dashboardId: id,
        timestamp: entry.timestamp,
        age: Date.now() - entry.timestamp,
        isExpired: Date.now() - entry.timestamp > cacheTimeout
      }))
    };
  }, [cache, maxCacheSize, cacheTimeout]);

  return {
    getCachedDashboard,
    setCachedDashboard,
    clearCache,
    getCacheStats,
    cacheSize: cache.size
  };
};