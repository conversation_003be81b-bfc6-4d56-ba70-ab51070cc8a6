import type { DashboardConfig } from '@/types/sisense';

// IIJA Dashboard Configuration
// Based on Source_sisense_exports/index.json and individual dashboard files
export const IIJA_DASHBOARDS: DashboardConfig[] = [
  {
    id: '6865541f099a11833ea60aef',
    title: '1. Summary Dashboard_SDK',
    slug: 'summary-dashboard',
    description: 'Overview of IIJA transportation construction analytics with key metrics and trends',
    oid: '6865541f099a11833ea60aef',
    hasWidgetsTabber: false,
    widgetCount: 21,
    mapCount: 1
  },
  {
    id: '68654950099a11833ea60935',
    title: '2. Contract Awards_SDK',
    slug: 'contract-awards',
    description: 'Transportation construction contract awards analysis and trends',
    oid: '68654950099a11833ea60935',
    hasWidgetsTabber: true,
    widgetCount: 41,
    mapCount: 4
  },
  {
    id: '68655384099a11833ea60aa4',
    title: '3. Value Put in Place_SDK',
    slug: 'value-put-in-place',
    description: 'Construction value put in place metrics and analysis',
    oid: '68655384099a11833ea60aa4',
    hasWidgetsTabber: true,
    widgetCount: 24,
    mapCount: 0
  },
  {
    id: '68655464099a11833ea60b57',
    title: '4. Federal-Aid Obligations_SDK',
    slug: 'federal-aid-obligations',
    description: 'Federal highway funding obligations and distributions',
    oid: '68655464099a11833ea60b57',
    hasWidgetsTabber: true,
    widgetCount: 17,
    mapCount: 1
  },
  {
    id: '6865547a099a11833ea60b8d',
    title: '5. IIJA State Funding_SDK',
    slug: 'iija-state-funding',
    description: 'Infrastructure Investment and Jobs Act state-level funding analysis',
    oid: '6865547a099a11833ea60b8d',
    hasWidgetsTabber: false,
    widgetCount: 13,
    mapCount: 1
  },
  {
    id: '68655309099a11833ea60a2a',
    title: '6. State Legislative Initiatives_SDK',
    slug: 'state-legislative-initiatives',
    description: 'State transportation funding legislative measures and initiatives',
    oid: '68655309099a11833ea60a2a',
    hasWidgetsTabber: false,
    widgetCount: 14,
    mapCount: 1
  },
  {
    id: '686554bc099a11833ea60be4',
    title: '7. How States Use Their Federal Aid Highway Funds_SDK',
    slug: 'federal-aid-highway-funds',
    description: 'Analysis of how states utilize federal highway funding',
    oid: '686554bc099a11833ea60be4',
    hasWidgetsTabber: false,
    widgetCount: 19,
    mapCount: 2
  },
  {
    id: '686554d1099a11833ea60c20',
    title: '8. State DOT Budgets_SDK',
    slug: 'state-dot-budgets',
    description: 'State Department of Transportation budget analysis and trends',
    oid: '686554d1099a11833ea60c20',
    hasWidgetsTabber: true,
    widgetCount: 16,
    mapCount: 1
  },
  {
    id: '686554e7099a11833ea60c53',
    title: '9. Material Prices_SDK',
    slug: 'material-prices',
    description: 'Construction material price trends and market analysis',
    oid: '686554e7099a11833ea60c53',
    hasWidgetsTabber: true,
    widgetCount: 22,
    mapCount: 0
  }
];

// Widget exclusion configuration
// Widgets to exclude from rendering (BloX widgets and other non-essential widgets)
export const EXCLUDED_WIDGET_IDS: string[] = [
  // Add specific widget IDs that should be excluded from rendering
  // These will be populated based on widget filtering requirements
];

// Widget type exclusion patterns
export const EXCLUDED_WIDGET_TYPES: string[] = [
  'BloX',
  'richtexteditor' // Rich text widgets are typically static content
];

// Widget types that should be treated as tabber widgets
export const TABBER_WIDGET_TYPES: string[] = [
  'WidgetsTabber'
];

// Map widget types for special handling
export const MAP_WIDGET_TYPES: string[] = [
  'map/area',
  'map/scatter'
];

// Default dashboard configuration
export const DEFAULT_DASHBOARD_CONFIG = {
  defaultDashboardId: IIJA_DASHBOARDS[0].id,
  refreshInterval: 300000, // 5 minutes
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  loadingTimeout: 30000 // 30 seconds
};

// Helper functions for dashboard configuration
export const getDashboardById = (id: string): DashboardConfig | undefined => {
  return IIJA_DASHBOARDS.find(dashboard => dashboard.id === id);
};

export const getDashboardBySlug = (slug: string): DashboardConfig | undefined => {
  return IIJA_DASHBOARDS.find(dashboard => dashboard.slug === slug);
};

export const getDashboardsWithTabs = (): DashboardConfig[] => {
  return IIJA_DASHBOARDS.filter(dashboard => dashboard.hasWidgetsTabber);
};

export const getDashboardsWithoutTabs = (): DashboardConfig[] => {
  return IIJA_DASHBOARDS.filter(dashboard => !dashboard.hasWidgetsTabber);
};

// Widget filtering helper functions
// Note: These are legacy functions. Use the comprehensive functions from @/lib/widgetFilters instead.
export const isExcludedWidget = (widget: { id: string; type: string; subtype?: string }): boolean => {
  // Check if widget ID is in exclusion list
  if (EXCLUDED_WIDGET_IDS.includes(widget.id)) {
    return true;
  }
  
  // Check if widget type is in exclusion list
  if (EXCLUDED_WIDGET_TYPES.includes(widget.type)) {
    return true;
  }
  
  // Check if widget subtype is in exclusion list
  if (widget.subtype && EXCLUDED_WIDGET_TYPES.includes(widget.subtype)) {
    return true;
  }
  
  return false;
};

export const isTabberWidget = (widget: { type: string; subtype?: string }): boolean => {
  return TABBER_WIDGET_TYPES.includes(widget.type) || 
         (widget.subtype ? TABBER_WIDGET_TYPES.includes(widget.subtype) : false);
};

export const isMapWidget = (widget: { type: string; isMap?: boolean }): boolean => {
  return widget.isMap === true || MAP_WIDGET_TYPES.some(type => widget.type.includes(type));
};

// Export alias for backward compatibility
export const DASHBOARDS = IIJA_DASHBOARDS;