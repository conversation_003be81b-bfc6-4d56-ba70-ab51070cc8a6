/**
 * Sisense Configuration
 * Central configuration for Sisense instance and dashboard IDs
 */

export const sisenseConfig = {
  // Base URL from environment variable
  url: import.meta.env.VITE_INSTANCE_URL || import.meta.env.VITE_SISENSE_URL,
  
  // API token from environment variable
  token: import.meta.env.VITE_TOKEN,
  
  // Dashboard IDs mapping - update these with your actual dashboard OIDs
  dashboards: {
    summary: '6308702510c4b001bdbcd9b_summary',
    contractAwards: '6308702510c4b001bdbcd9b_contract_awards',
    valuePutInPlace: '6308702510c4b001bdbcd9b_value_put_in_place',
    federalAidObligations: '6308702510c4b001bdbcd9b_federal_aid_obligations',
    iijaStateFunding: '6308702510c4b001bdbcd9b_iija_state_funding',
    stateLegislativeInitiatives: '6308702510c4b001bdbcd9b_state_legislative_initiatives',
    federalAidHighwayFund: '6308702510c4b001bdbcd9b_federal_aid_highway_fund',
    stateDotBudgets: '6308702510c4b001bdbcd9b_state_dot_budgets',
    materialPrices: '6308702510c4b001bdbcd9b_material_prices'
  }
};

// Validate configuration
if (!sisenseConfig.url) {
  console.error('Sisense URL is required. Please set VITE_INSTANCE_URL or VITE_SISENSE_URL environment variable.');
}

if (!sisenseConfig.token) {
  console.error('Sisense token is required. Please set VITE_TOKEN environment variable.');
}

export default sisenseConfig;