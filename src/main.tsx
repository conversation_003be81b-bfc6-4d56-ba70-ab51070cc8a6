import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { RouterProvider } from 'react-router-dom'
import { HelmetProvider } from 'react-helmet-async'
import { GlobalErrorBoundary } from '@/components/sisense/GlobalErrorBoundary'
import './index.css'
import { router } from './router'

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <GlobalErrorBoundary showReportButton={true}>
      <HelmetProvider>
        <RouterProvider router={router} />
      </HelmetProvider>
    </GlobalErrorBoundary>
  </StrictMode>,
)
