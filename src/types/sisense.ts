// Sisense SDK type extensions and custom types

export interface SisenseConfig {
  url: string;
  token: string;
}

export interface SisenseAppConfig {
  queryCacheConfig?: {
    enabled: boolean;
  };
  trackingConfig?: {
    enabled: boolean;
  };
}

export interface SisenseError extends Error {
  status?: number;
  statusText?: string;
  response?: any;
}

// Dashboard and Widget types
export interface DashboardConfig {
  id: string;
  title: string;
  slug: string;
  description: string;
  oid?: string;
  hasWidgetsTabber?: boolean;
  widgetCount?: number;
  mapCount?: number;
}

export interface ApiWidget {
  id: string;
  oid?: string;
  title?: string | null;
  type: string;
  subtype: string;
  isMap: boolean;
  script?: string;
  jaql?: {
    titles: string[];
    dimensions: string[];
    measures: string[];
  };
}

export interface TabDefinition {
  title: string;
  displayWidgetIds: string[];
  hideWidgetIds: string[];
  sourceTabberId?: string;
}

// Dashboard state management types
export interface DashboardState {
  activeDashboardId: string;
  dashboards: DashboardConfig[];
  widgetsByDashboard: Record<string, ApiWidget[]>;
  tabsByDashboard: Record<string, TabDefinition[]>;
  loadingStates: Record<string, boolean>;
  errors: Record<string, string | null>;
}

// Widget state management types
export interface WidgetState {
  widgetOid: string;
  dashboardOid: string;
  isLoading: boolean;
  isError: boolean;
  error?: Error;
  isVisible: boolean;
}

// Local storage preferences
export interface DashboardPreferences {
  activeDashboardId: string;
  collapsedTabs: string[];
  widgetSizes: Record<string, { width: number; height: number }>;
  lastRefreshTime: number;
}

// API response types
export interface DashboardWidgetsResponse {
  widgets: ApiWidget[];
  tabs: TabDefinition[];
  isLoading: boolean;
  error: string | null;
}

export interface SisenseApiError {
  status: number;
  message: string;
  details?: any;
}

// Error types for better error handling
export const SisenseErrorType = {
  AUTHENTICATION: 'authentication',
  NETWORK: 'network',
  WIDGET_LOAD: 'widget_load',
  DASHBOARD_LOAD: 'dashboard_load',
  PARSING: 'parsing',
  UNKNOWN: 'unknown'
} as const;

export type SisenseErrorType = typeof SisenseErrorType[keyof typeof SisenseErrorType];

export interface SisenseErrorInfo {
  type: SisenseErrorType;
  message: string;
  originalError?: Error;
  retry?: () => void;
}