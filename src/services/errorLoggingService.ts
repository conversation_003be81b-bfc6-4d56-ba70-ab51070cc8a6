import type { ErrorInfo } from 'react';

export interface ErrorReport {
  errorId: string;
  message: string;
  stack?: string;
  componentStack?: string;
  timestamp: string;
  userAgent: string;
  url: string;
  userId?: string;
  sessionId?: string;
  buildVersion?: string;
  environment: string;
  errorType: 'javascript' | 'react' | 'sisense' | 'network' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
}

export interface ErrorContext {
  dashboardId?: string;
  widgetId?: string;
  userId?: string;
  sessionId?: string;
  customData?: Record<string, any>;
}

class ErrorLoggingService {
  private sessionId: string;
  private userId?: string;
  private buildVersion?: string;
  private errorQueue: ErrorReport[] = [];
  private isOnline = navigator.onLine;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.buildVersion = import.meta.env.VITE_BUILD_VERSION || 'unknown';
    
    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushErrorQueue();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // Listen for unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError(event.reason, {
        errorType: 'javascript',
        severity: 'high',
        context: { type: 'unhandledrejection' }
      });
    });

    // Listen for global JavaScript errors
    window.addEventListener('error', (event) => {
      this.logError(event.error || new Error(event.message), {
        errorType: 'javascript',
        severity: 'medium',
        context: { 
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });
  }

  setUserId(userId: string) {
    this.userId = userId;
  }

  logError(
    error: Error, 
    options: {
      errorType?: ErrorReport['errorType'];
      severity?: ErrorReport['severity'];
      context?: ErrorContext;
      componentStack?: string;
    } = {}
  ): string {
    const errorId = this.generateErrorId();
    
    const errorReport: ErrorReport = {
      errorId,
      message: error.message,
      stack: error.stack,
      componentStack: options.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.userId,
      sessionId: this.sessionId,
      buildVersion: this.buildVersion,
      environment: import.meta.env.MODE || 'unknown',
      errorType: options.errorType || this.categorizeError(error),
      severity: options.severity || this.determineSeverity(error),
      context: options.context,
    };

    // Log to console in development
    if (import.meta.env.DEV) {
      console.error('Error logged:', errorReport);
    }

    // Store error report
    this.storeError(errorReport);

    // Send to external service if online
    if (this.isOnline) {
      this.sendErrorReport(errorReport);
    } else {
      this.queueError(errorReport);
    }

    return errorId;
  }

  logReactError(error: Error, errorInfo: ErrorInfo, context?: ErrorContext): string {
    return this.logError(error, {
      errorType: 'react',
      severity: 'high',
      context,
      componentStack: errorInfo.componentStack,
    });
  }

  logSisenseError(error: Error, context?: ErrorContext): string {
    return this.logError(error, {
      errorType: 'sisense',
      severity: 'medium',
      context,
    });
  }

  logNetworkError(error: Error, context?: ErrorContext): string {
    return this.logError(error, {
      errorType: 'network',
      severity: 'medium',
      context,
    });
  }

  private generateErrorId(): string {
    return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `SES_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private categorizeError(error: Error): ErrorReport['errorType'] {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('sisense') || stack.includes('sisense')) {
      return 'sisense';
    }
    
    if (
      message.includes('fetch') || 
      message.includes('network') || 
      message.includes('timeout') ||
      message.includes('cors')
    ) {
      return 'network';
    }

    if (
      message.includes('component') || 
      stack.includes('react') ||
      stack.includes('jsx')
    ) {
      return 'react';
    }

    return 'javascript';
  }

  private determineSeverity(error: Error): ErrorReport['severity'] {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    // Critical errors
    if (
      message.includes('chunk') ||
      message.includes('module') ||
      message.includes('loading') ||
      stack.includes('webpack') ||
      stack.includes('vite')
    ) {
      return 'critical';
    }

    // High severity errors
    if (
      message.includes('render') ||
      message.includes('component') ||
      message.includes('auth')
    ) {
      return 'high';
    }

    // Medium severity errors
    if (
      message.includes('network') ||
      message.includes('fetch') ||
      message.includes('sisense')
    ) {
      return 'medium';
    }

    return 'low';
  }

  private storeError(errorReport: ErrorReport) {
    try {
      const storedErrors = this.getStoredErrors();
      storedErrors.push(errorReport);
      
      // Keep only last 50 errors to prevent storage bloat
      const recentErrors = storedErrors.slice(-50);
      
      localStorage.setItem('error_reports', JSON.stringify(recentErrors));
    } catch (e) {
      console.warn('Failed to store error report:', e);
    }
  }

  private getStoredErrors(): ErrorReport[] {
    try {
      const stored = localStorage.getItem('error_reports');
      return stored ? JSON.parse(stored) : [];
    } catch (e) {
      console.warn('Failed to retrieve stored errors:', e);
      return [];
    }
  }

  private queueError(errorReport: ErrorReport) {
    this.errorQueue.push(errorReport);
    
    // Limit queue size
    if (this.errorQueue.length > 20) {
      this.errorQueue = this.errorQueue.slice(-20);
    }
  }

  private async flushErrorQueue() {
    if (this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    for (const error of errors) {
      await this.sendErrorReport(error);
    }
  }

  private async sendErrorReport(errorReport: ErrorReport) {
    try {
      // In a real application, send to your error tracking service
      // Examples: Sentry, LogRocket, Bugsnag, DataDog, etc.
      
      // Example implementation:
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport),
      // });

      // For development, just log
      if (import.meta.env.DEV) {
        console.log('Would send error report:', errorReport);
      }
    } catch (e) {
      console.warn('Failed to send error report:', e);
      // Re-queue the error for later retry
      this.queueError(errorReport);
    }
  }

  // Public methods for retrieving error information
  getErrorHistory(): ErrorReport[] {
    return this.getStoredErrors();
  }

  getSessionId(): string {
    return this.sessionId;
  }

  clearErrorHistory() {
    try {
      localStorage.removeItem('error_reports');
      this.errorQueue = [];
    } catch (e) {
      console.warn('Failed to clear error history:', e);
    }
  }

  // Method to create error report for external use (e.g., user feedback)
  createErrorReport(errorId: string): string {
    const errors = this.getStoredErrors();
    const error = errors.find(e => e.errorId === errorId);
    
    if (!error) {
      return 'Error report not found';
    }

    return `
Error Report
============

Error ID: ${error.errorId}
Timestamp: ${error.timestamp}
URL: ${error.url}
Message: ${error.message}
Type: ${error.errorType}
Severity: ${error.severity}
Environment: ${error.environment}
Build Version: ${error.buildVersion}
Session ID: ${error.sessionId}
User Agent: ${error.userAgent}

Stack Trace:
${error.stack || 'No stack trace available'}

Component Stack:
${error.componentStack || 'No component stack available'}

Context:
${error.context ? JSON.stringify(error.context, null, 2) : 'No additional context'}
    `.trim();
  }
}

// Export singleton instance
export const errorLoggingService = new ErrorLoggingService();
export default errorLoggingService;