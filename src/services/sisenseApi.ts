import type { 
  ApiWidget, 
  TabDefinition, 
  SisenseApiError,
  DashboardWidgetsResponse 
} from '@/types/sisense';
import { 
  SISENSE_CONFIG, 
  SISENSE_ENDPOINTS, 
  ERROR_MESSAGES,
  TAB_CONFIG 
} from '@/constants/sisense';

// API client configuration
const createApiClient = () => {
  const baseURL = SISENSE_CONFIG.URL;
  const token = SISENSE_CONFIG.TOKEN;

  if (!baseURL || !token) {
    throw new Error('Sisense URL and token must be configured');
  }

  return {
    baseURL,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    timeout: SISENSE_CONFIG.TIMEOUT,
  };
};

// Retry logic for failed requests
const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry on authentication errors
      if (error instanceof Error && error.message.includes('401')) {
        throw error;
      }

      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
  }

  throw lastError!;
};

// Create a SisenseApiError from a fetch response
const createSisenseError = async (response: Response): Promise<SisenseApiError> => {
  let details;
  try {
    details = await response.json();
  } catch {
    details = { message: response.statusText };
  }

  return {
    status: response.status,
    message: details.message || ERROR_MESSAGES.UNKNOWN_ERROR,
    details,
  };
};

// Fetch dashboard widgets from Sisense REST API
export const fetchDashboardWidgets = async (
  dashboardId: string
): Promise<DashboardWidgetsResponse> => {
  const client = createApiClient();
  
  const operation = async (): Promise<DashboardWidgetsResponse> => {
    const url = `${client.baseURL}${SISENSE_ENDPOINTS.WIDGETS.replace('{dashboardId}', dashboardId)}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), client.timeout);

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: client.headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const error = await createSisenseError(response);
        throw new Error(`API Error ${error.status}: ${error.message}`);
      }

      const data = await response.json();
      
      // Transform the response to match our ApiWidget interface
      const widgets: ApiWidget[] = (data.widgets || data || []).map((widget: any) => ({
        id: widget.id || widget.oid,
        oid: widget.oid || widget.id,
        title: widget.title,
        type: widget.type,
        subtype: widget.subtype,
        isMap: widget.isMap || false,
        script: widget.script,
        jaql: widget.jaql,
      }));

      // Parse tabs from tabber widgets
      const tabs = parseTabsFromWidgets(widgets);

      return {
        widgets,
        tabs,
        isLoading: false,
        error: null,
      };
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(ERROR_MESSAGES.TIMEOUT_ERROR);
        }
        if (error.message.includes('Failed to fetch')) {
          throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
        }
        if (error.message.includes('401')) {
          throw new Error(ERROR_MESSAGES.AUTHENTICATION_FAILED);
        }
      }
      
      throw error;
    }
  };

  try {
    return await withRetry(operation);
  } catch (error) {
    return {
      widgets: [],
      tabs: [],
      isLoading: false,
      error: error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR,
    };
  }
};

// Parse tab definitions from tabber widget scripts
export const parseTabScript = (script: string): TabDefinition[] => {
  if (!script) return [];

  const tabs: TabDefinition[] = [];
  const { TAB_SCRIPT_PATTERNS } = TAB_CONFIG;

  try {
    // Extract tab definitions using regex patterns
    const displayWidgetMatches = Array.from(script.matchAll(TAB_SCRIPT_PATTERNS.DISPLAY_WIDGETS));
    const hideWidgetMatches = Array.from(script.matchAll(TAB_SCRIPT_PATTERNS.HIDE_WIDGETS));
    const titleMatches = Array.from(script.matchAll(TAB_SCRIPT_PATTERNS.TAB_TITLE));

    // Create tab definitions from matches
    const maxTabs = Math.max(
      displayWidgetMatches.length,
      hideWidgetMatches.length,
      titleMatches.length
    );

    for (let i = 0; i < maxTabs; i++) {
      const displayWidgets = displayWidgetMatches[i]?.[1] || '';
      const hideWidgets = hideWidgetMatches[i]?.[1] || '';
      const title = titleMatches[i]?.[1] || `${TAB_CONFIG.DEFAULT_TAB_TITLE} ${i + 1}`;

      // Parse widget IDs from comma-separated strings
      const displayWidgetIds = displayWidgets
        .split(',')
        .map(id => id.trim().replace(/['"]/g, ''))
        .filter(id => id.length > 0);

      const hideWidgetIds = hideWidgets
        .split(',')
        .map(id => id.trim().replace(/['"]/g, ''))
        .filter(id => id.length > 0);

      if (displayWidgetIds.length > 0 || hideWidgetIds.length > 0) {
        tabs.push({
          title: title.substring(0, TAB_CONFIG.MAX_TAB_TITLE_LENGTH),
          displayWidgetIds,
          hideWidgetIds,
        });
      }
    }
  } catch (error) {
    console.warn('Error parsing tab script:', error);
  }

  return tabs;
};

// Parse tabs from widgets (looking for WidgetsTabber widgets)
const parseTabsFromWidgets = (widgets: ApiWidget[]): TabDefinition[] => {
  const tabberWidgets = widgets.filter(widget => 
    widget.type === 'WidgetsTabber' || widget.subtype === 'WidgetsTabber'
  );

  const allTabs: TabDefinition[] = [];

  for (const tabberWidget of tabberWidgets) {
    if (tabberWidget.script) {
      const tabs = parseTabScript(tabberWidget.script);
      tabs.forEach(tab => {
        tab.sourceTabberId = tabberWidget.id;
      });
      allTabs.push(...tabs);
    }
  }

  return allTabs;
};

// Fetch widget data (for future use)
export const fetchWidgetData = async (widgetId: string): Promise<any> => {
  const client = createApiClient();
  
  const operation = async () => {
    const url = `${client.baseURL}${SISENSE_ENDPOINTS.WIDGET_DATA.replace('{widgetId}', widgetId)}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), client.timeout);

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: client.headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const error = await createSisenseError(response);
        throw new Error(`API Error ${error.status}: ${error.message}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  };

  return await withRetry(operation);
};

// Health check endpoint
export const checkSisenseHealth = async (): Promise<boolean> => {
  try {
    const client = createApiClient();
    const url = `${client.baseURL}${SISENSE_ENDPOINTS.HEALTH}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: client.headers,
      signal: AbortSignal.timeout(5000), // 5 second timeout for health check
    });

    return response.ok;
  } catch {
    return false;
  }
};

// Utility function to validate API configuration
export const validateSisenseConfig = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!SISENSE_CONFIG.URL) {
    errors.push('Sisense URL is not configured');
  }

  if (!SISENSE_CONFIG.TOKEN) {
    errors.push('Sisense token is not configured');
  }

  try {
    new URL(SISENSE_CONFIG.URL);
  } catch {
    errors.push('Sisense URL is not a valid URL');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};