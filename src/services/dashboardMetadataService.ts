import type { 
  DashboardConfig, 
  SisenseErrorInfo,
  DashboardPreferences 
} from '@/types/sisense';
import { SisenseErrorType } from '@/types/sisense';
import { IIJA_DASHBOARDS, DEFAULT_DASHBOARD_CONFIG } from '@/config/dashboards';
import { STORAGE_KEYS, ERROR_MESSAGES } from '@/constants/sisense';
import { checkSisenseHealth, validateSisenseConfig } from './sisenseApi';

// Dashboard metadata service class
export class DashboardMetadataService {
  private static instance: DashboardMetadataService;
  private cache: Map<string, { data: DashboardConfig[]; timestamp: number }> = new Map();
  private readonly cacheTimeout = 300000; // 5 minutes

  private constructor() {}

  static getInstance(): DashboardMetadataService {
    if (!DashboardMetadataService.instance) {
      DashboardMetadataService.instance = new DashboardMetadataService();
    }
    return DashboardMetadataService.instance;
  }

  // Load dashboard metadata with caching
  async loadDashboardMetadata(forceRefresh = false): Promise<{
    dashboards: DashboardConfig[];
    error: SisenseErrorInfo | null;
  }> {
    const cacheKey = 'dashboard-metadata';
    
    // Check cache first unless force refresh
    if (!forceRefresh) {
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return { dashboards: cached.data, error: null };
      }
    }

    try {
      // Validate configuration
      const configValidation = validateSisenseConfig();
      if (!configValidation.isValid) {
        throw new Error(`Configuration invalid: ${configValidation.errors.join(', ')}`);
      }

      // Check health
      const isHealthy = await checkSisenseHealth();
      if (!isHealthy) {
        throw new Error('Sisense service is not healthy');
      }

      // In a real application, this would fetch from the Sisense API
      // For now, we use the static configuration with validation
      const dashboards = await this.validateDashboards(IIJA_DASHBOARDS);

      // Cache the result
      this.cache.set(cacheKey, {
        data: dashboards,
        timestamp: Date.now()
      });

      // Persist to localStorage
      this.saveDashboardsToStorage(dashboards);

      return { dashboards, error: null };
    } catch (error) {
      const sisenseError: SisenseErrorInfo = {
        type: SisenseErrorType.DASHBOARD_LOAD,
        message: error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR,
        originalError: error instanceof Error ? error : undefined
      };

      // Try to load from localStorage as fallback
      const fallbackDashboards = this.loadDashboardsFromStorage();
      if (fallbackDashboards.length > 0) {
        return { dashboards: fallbackDashboards, error: sisenseError };
      }

      return { dashboards: [], error: sisenseError };
    }
  }

  // Validate dashboard configurations
  private async validateDashboards(dashboards: DashboardConfig[]): Promise<DashboardConfig[]> {
    // In a real application, this would validate each dashboard exists in Sisense
    // For now, we just return the dashboards with basic validation
    return dashboards.filter(dashboard => 
      dashboard.id && 
      dashboard.title && 
      dashboard.slug
    );
  }

  // Get dashboard by ID
  getDashboardById(dashboards: DashboardConfig[], id: string): DashboardConfig | undefined {
    return dashboards.find(dashboard => dashboard.id === id);
  }

  // Get dashboard by slug
  getDashboardBySlug(dashboards: DashboardConfig[], slug: string): DashboardConfig | undefined {
    return dashboards.find(dashboard => dashboard.slug === slug);
  }

  // Get next dashboard in sequence
  getNextDashboard(dashboards: DashboardConfig[], currentId: string): DashboardConfig | undefined {
    const currentIndex = dashboards.findIndex(d => d.id === currentId);
    if (currentIndex === -1) return dashboards[0];
    
    const nextIndex = (currentIndex + 1) % dashboards.length;
    return dashboards[nextIndex];
  }

  // Get previous dashboard in sequence
  getPreviousDashboard(dashboards: DashboardConfig[], currentId: string): DashboardConfig | undefined {
    const currentIndex = dashboards.findIndex(d => d.id === currentId);
    if (currentIndex === -1) return dashboards[dashboards.length - 1];
    
    const prevIndex = currentIndex === 0 ? dashboards.length - 1 : currentIndex - 1;
    return dashboards[prevIndex];
  }

  // Dashboard selection management
  selectDashboard(dashboardId: string, dashboards: DashboardConfig[]): {
    success: boolean;
    dashboard?: DashboardConfig;
    error?: SisenseErrorInfo;
  } {
    const dashboard = this.getDashboardById(dashboards, dashboardId);
    
    if (!dashboard) {
      return {
        success: false,
        error: {
          type: SisenseErrorType.DASHBOARD_LOAD,
          message: `Dashboard with ID "${dashboardId}" not found`
        }
      };
    }

    // Save selection to localStorage
    this.saveSelectedDashboard(dashboardId);

    return { success: true, dashboard };
  }

  // Local storage operations
  private saveDashboardsToStorage(dashboards: DashboardConfig[]): void {
    try {
      const cacheData = {
        dashboards,
        timestamp: Date.now()
      };
      localStorage.setItem(STORAGE_KEYS.DASHBOARD_PREFERENCES, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to save dashboards to localStorage:', error);
    }
  }

  private loadDashboardsFromStorage(): DashboardConfig[] {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.DASHBOARD_PREFERENCES);
      if (!stored) return [];
      
      const parsed = JSON.parse(stored);
      if (parsed.dashboards && Array.isArray(parsed.dashboards)) {
        return parsed.dashboards;
      }
      return [];
    } catch (error) {
      console.warn('Failed to load dashboards from localStorage:', error);
      return [];
    }
  }

  saveSelectedDashboard(dashboardId: string): void {
    try {
      localStorage.setItem(STORAGE_KEYS.ACTIVE_DASHBOARD, dashboardId);
    } catch (error) {
      console.warn('Failed to save selected dashboard to localStorage:', error);
    }
  }

  loadSelectedDashboard(): string | null {
    try {
      return localStorage.getItem(STORAGE_KEYS.ACTIVE_DASHBOARD);
    } catch (error) {
      console.warn('Failed to load selected dashboard from localStorage:', error);
      return null;
    }
  }

  // Dashboard preferences management
  saveDashboardPreferences(preferences: DashboardPreferences): void {
    try {
      localStorage.setItem(STORAGE_KEYS.USER_SETTINGS, JSON.stringify(preferences));
    } catch (error) {
      console.warn('Failed to save dashboard preferences to localStorage:', error);
    }
  }

  loadDashboardPreferences(): DashboardPreferences | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.USER_SETTINGS);
      if (!stored) return null;
      
      const parsed = JSON.parse(stored);
      return parsed;
    } catch (error) {
      console.warn('Failed to load dashboard preferences from localStorage:', error);
      return null;
    }
  }

  // Health and configuration checks
  async checkSystemHealth(): Promise<{
    isHealthy: boolean;
    configValid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];
    
    // Check configuration
    const configValidation = validateSisenseConfig();
    if (!configValidation.isValid) {
      errors.push(...configValidation.errors);
    }

    // Check health
    let isHealthy = false;
    try {
      isHealthy = await checkSisenseHealth();
      if (!isHealthy) {
        errors.push('Sisense service is not responding');
      }
    } catch (error) {
      errors.push('Failed to check Sisense health');
      isHealthy = false;
    }

    return {
      isHealthy,
      configValid: configValidation.isValid,
      errors
    };
  }

  // Cache management
  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats(): {
    size: number;
    entries: Array<{
      key: string;
      timestamp: number;
      age: number;
      isExpired: boolean;
    }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, value]) => ({
      key,
      timestamp: value.timestamp,
      age: Date.now() - value.timestamp,
      isExpired: Date.now() - value.timestamp > this.cacheTimeout
    }));

    return {
      size: this.cache.size,
      entries
    };
  }

  // Cleanup expired cache entries
  cleanupExpiredCache(): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }

  // Reset all data
  reset(): void {
    this.clearCache();
    try {
      localStorage.removeItem(STORAGE_KEYS.ACTIVE_DASHBOARD);
      localStorage.removeItem(STORAGE_KEYS.DASHBOARD_PREFERENCES);
      localStorage.removeItem(STORAGE_KEYS.USER_SETTINGS);
    } catch (error) {
      console.warn('Failed to clear localStorage:', error);
    }
  }
}

// Export singleton instance
export const dashboardMetadataService = DashboardMetadataService.getInstance();

// Utility functions for common operations
export const dashboardUtils = {
  // Get default dashboard ID
  getDefaultDashboardId: (): string => {
    return DEFAULT_DASHBOARD_CONFIG.defaultDashboardId;
  },

  // Validate dashboard ID format
  isValidDashboardId: (id: string): boolean => {
    return typeof id === 'string' && id.length > 0;
  },

  // Create dashboard URL slug
  createSlug: (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  },

  // Format dashboard title for display
  formatTitle: (title: string): string => {
    return title.replace(/_SDK$/, '').replace(/^\d+\.\s*/, '');
  },

  // Check if dashboard has tabbed widgets
  hasTabberWidgets: (dashboard: DashboardConfig): boolean => {
    return dashboard.hasWidgetsTabber === true;
  },

  // Get dashboard statistics
  getDashboardStats: (dashboards: DashboardConfig[]) => {
    const totalWidgets = dashboards.reduce((sum, d) => sum + (d.widgetCount || 0), 0);
    const totalMaps = dashboards.reduce((sum, d) => sum + (d.mapCount || 0), 0);
    const withTabs = dashboards.filter(d => d.hasWidgetsTabber).length;
    
    return {
      totalDashboards: dashboards.length,
      totalWidgets,
      totalMaps,
      dashboardsWithTabs: withTabs,
      dashboardsWithoutTabs: dashboards.length - withTabs
    };
  }
};