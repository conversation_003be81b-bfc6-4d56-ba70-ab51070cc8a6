import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { fetchDashboardWidgets, parseTabScript } from '../sisenseApi';

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock error logging service
vi.mock('@/services/errorLoggingService', () => ({
  errorLoggingService: {
    logNetworkError: vi.fn(),
  },
}));

// Mock environment variables
vi.mock('@/constants/sisense', () => ({
  SISENSE_URL: 'https://test-sisense.com',
  SISENSE_TOKEN: 'test-token',
}));

describe('sisenseApi', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('fetchDashboardWidgets', () => {
    const mockDashboardId = 'test-dashboard-123';

    it('fetches dashboard widgets successfully', async () => {
      const mockResponse = {
        widgets: [
          {
            oid: 'widget-1',
            title: 'Test Widget 1',
            type: 'chart',
            subtype: 'column',
          },
          {
            oid: 'widget-2',
            title: 'Test Widget 2',
            type: 'table',
            subtype: 'pivot',
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockResponse),
      });

      const result = await fetchDashboardWidgets(mockDashboardId);

      expect(mockFetch).toHaveBeenCalledWith(
        `https://test-sisense.com/api/v1/dashboards/${mockDashboardId}/widgets`,
        {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json',
          },
        }
      );

      expect(result.widgets).toEqual(mockResponse.widgets);
      expect(result.tabs).toEqual([]);
    });

    it('handles network errors', async () => {
      const networkError = new Error('Network error');
      mockFetch.mockRejectedValueOnce(networkError);

      await expect(fetchDashboardWidgets(mockDashboardId)).rejects.toThrow('Network error');
    });

    it('handles HTTP error responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: () => Promise.resolve({ error: 'Dashboard not found' }),
      });

      await expect(fetchDashboardWidgets(mockDashboardId)).rejects.toThrow('HTTP error! status: 404');
    });

    it('handles authentication errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: () => Promise.resolve({ error: 'Invalid token' }),
      });

      await expect(fetchDashboardWidgets(mockDashboardId)).rejects.toThrow('HTTP error! status: 401');
    });

    it('handles malformed JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.reject(new Error('Invalid JSON')),
      });

      await expect(fetchDashboardWidgets(mockDashboardId)).rejects.toThrow('Invalid JSON');
    });

    it('filters out excluded widgets', async () => {
      const mockResponse = {
        widgets: [
          {
            oid: 'widget-1',
            title: 'Valid Widget',
            type: 'chart',
            subtype: 'column',
          },
          {
            oid: 'widget-2',
            title: 'BloX Widget',
            type: 'blox',
            subtype: 'container',
          },
          {
            oid: 'excluded-widget-id',
            title: 'Excluded Widget',
            type: 'chart',
            subtype: 'line',
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockResponse),
      });

      const result = await fetchDashboardWidgets(mockDashboardId);

      // Should only include the valid widget
      expect(result.widgets).toHaveLength(1);
      expect(result.widgets[0].oid).toBe('widget-1');
    });

    it('parses tabber widgets and creates tabs', async () => {
      const mockResponse = {
        widgets: [
          {
            oid: 'widget-1',
            title: 'Regular Widget',
            type: 'chart',
            subtype: 'column',
          },
          {
            oid: 'tabber-widget',
            title: 'Tabber Widget',
            type: 'tabber',
            subtype: 'tabber',
            script: `
              var config = {
                tabs: [
                  {
                    title: 'Tab 1',
                    displayWidgetIds: ['widget-2', 'widget-3'],
                    hideWidgetIds: []
                  },
                  {
                    title: 'Tab 2',
                    displayWidgetIds: ['widget-4'],
                    hideWidgetIds: ['widget-5']
                  }
                ]
              };
            `,
          },
          {
            oid: 'widget-2',
            title: 'Widget 2',
            type: 'chart',
            subtype: 'line',
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockResponse),
      });

      const result = await fetchDashboardWidgets(mockDashboardId);

      expect(result.tabs).toHaveLength(2);
      expect(result.tabs[0]).toEqual({
        title: 'Tab 1',
        displayWidgetIds: ['widget-2', 'widget-3'],
        hideWidgetIds: [],
        sourceTabberId: 'tabber-widget',
      });
      expect(result.tabs[1]).toEqual({
        title: 'Tab 2',
        displayWidgetIds: ['widget-4'],
        hideWidgetIds: ['widget-5'],
        sourceTabberId: 'tabber-widget',
      });
    });

    it('handles empty widget list', async () => {
      const mockResponse = { widgets: [] };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockResponse),
      });

      const result = await fetchDashboardWidgets(mockDashboardId);

      expect(result.widgets).toEqual([]);
      expect(result.tabs).toEqual([]);
    });

    it('handles missing dashboard ID', async () => {
      await expect(fetchDashboardWidgets('')).rejects.toThrow('Dashboard ID is required');
    });

    it('handles timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'AbortError';
      mockFetch.mockRejectedValueOnce(timeoutError);

      await expect(fetchDashboardWidgets(mockDashboardId)).rejects.toThrow('Request timeout');
    });

    it('retries failed requests', async () => {
      const networkError = new Error('Network error');
      
      // First two calls fail, third succeeds
      mockFetch
        .mockRejectedValueOnce(networkError)
        .mockRejectedValueOnce(networkError)
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: () => Promise.resolve({ widgets: [] }),
        });

      const result = await fetchDashboardWidgets(mockDashboardId, { retries: 2 });

      expect(mockFetch).toHaveBeenCalledTimes(3);
      expect(result.widgets).toEqual([]);
    });

    it('handles rate limiting', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        headers: new Map([['Retry-After', '60']]),
        json: () => Promise.resolve({ error: 'Rate limit exceeded' }),
      });

      await expect(fetchDashboardWidgets(mockDashboardId)).rejects.toThrow('HTTP error! status: 429');
    });
  });

  describe('parseTabScript', () => {
    it('parses valid tab script', () => {
      const script = `
        var config = {
          tabs: [
            {
              title: 'Revenue',
              displayWidgetIds: ['widget-1', 'widget-2'],
              hideWidgetIds: []
            },
            {
              title: 'Costs',
              displayWidgetIds: ['widget-3'],
              hideWidgetIds: ['widget-4']
            }
          ]
        };
      `;

      const result = parseTabScript(script, 'tabber-1');

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        title: 'Revenue',
        displayWidgetIds: ['widget-1', 'widget-2'],
        hideWidgetIds: [],
        sourceTabberId: 'tabber-1',
      });
      expect(result[1]).toEqual({
        title: 'Costs',
        displayWidgetIds: ['widget-3'],
        hideWidgetIds: ['widget-4'],
        sourceTabberId: 'tabber-1',
      });
    });

    it('handles malformed script gracefully', () => {
      const malformedScript = 'invalid javascript code {{{';

      const result = parseTabScript(malformedScript, 'tabber-1');

      expect(result).toEqual([]);
    });

    it('handles script without config object', () => {
      const script = 'var someOtherVariable = "test";';

      const result = parseTabScript(script, 'tabber-1');

      expect(result).toEqual([]);
    });

    it('handles config without tabs array', () => {
      const script = 'var config = { title: "Test" };';

      const result = parseTabScript(script, 'tabber-1');

      expect(result).toEqual([]);
    });

    it('handles empty tabs array', () => {
      const script = 'var config = { tabs: [] };';

      const result = parseTabScript(script, 'tabber-1');

      expect(result).toEqual([]);
    });

    it('handles tabs with missing properties', () => {
      const script = `
        var config = {
          tabs: [
            { title: 'Tab 1' },
            { displayWidgetIds: ['widget-1'] },
            { title: 'Tab 2', displayWidgetIds: ['widget-2'], hideWidgetIds: ['widget-3'] }
          ]
        };
      `;

      const result = parseTabScript(script, 'tabber-1');

      // Should only return valid tabs
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Tab 2',
        displayWidgetIds: ['widget-2'],
        hideWidgetIds: ['widget-3'],
        sourceTabberId: 'tabber-1',
      });
    });

    it('handles null or undefined script', () => {
      expect(parseTabScript(null as any, 'tabber-1')).toEqual([]);
      expect(parseTabScript(undefined as any, 'tabber-1')).toEqual([]);
      expect(parseTabScript('', 'tabber-1')).toEqual([]);
    });

    it('handles complex nested config structures', () => {
      const script = `
        var config = {
          metadata: { version: '1.0' },
          tabs: [
            {
              title: 'Overview',
              displayWidgetIds: ['widget-1', 'widget-2'],
              hideWidgetIds: [],
              settings: { color: 'blue' }
            }
          ],
          other: { data: 'test' }
        };
      `;

      const result = parseTabScript(script, 'tabber-1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Overview',
        displayWidgetIds: ['widget-1', 'widget-2'],
        hideWidgetIds: [],
        sourceTabberId: 'tabber-1',
      });
    });
  });
});