import React from 'react';
import { useRouteError, useNavigate, isRouteErrorResponse } from 'react-router-dom';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, Home, RefreshCw, ArrowLeft } from 'lucide-react';
import { Helmet } from 'react-helmet-async';

/**
 * Error boundary component for handling route-level errors
 */
const RouteErrorBoundary: React.FC = () => {
  const error = useRouteError();
  const navigate = useNavigate();

  // Determine error type and message
  const getErrorInfo = () => {
    if (isRouteErrorResponse(error)) {
      switch (error.status) {
        case 404:
          return {
            title: 'Page Not Found',
            message: 'The page you are looking for does not exist or has been moved.',
            showRetry: false,
          };
        case 403:
          return {
            title: 'Access Denied',
            message: 'You do not have permission to access this page.',
            showRetry: false,
          };
        case 500:
          return {
            title: 'Server Error',
            message: 'An internal server error occurred. Please try again later.',
            showRetry: true,
          };
        default:
          return {
            title: 'Error',
            message: error.statusText || 'An unexpected error occurred.',
            showRetry: true,
          };
      }
    }

    if (error instanceof Error) {
      return {
        title: 'Application Error',
        message: error.message || 'An unexpected error occurred.',
        showRetry: true,
      };
    }

    return {
      title: 'Unknown Error',
      message: 'An unknown error occurred.',
      showRetry: true,
    };
  };

  const { title, message, showRetry } = getErrorInfo();

  const handleRetry = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    navigate('/', { replace: true });
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      navigate('/', { replace: true });
    }
  };

  return (
    <>
      <Helmet>
        <title>{title} - IIJA Dashboard</title>
        <meta name="description" content={message} />
      </Helmet>

      <div className="min-h-screen flex items-center justify-center p-6 bg-background">
        <Card className="max-w-2xl w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              {title}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{message}</AlertDescription>
            </Alert>

            {/* Error details for development */}
            {process.env.NODE_ENV === 'development' && error instanceof Error && (
              <div className="bg-muted p-4 rounded-md">
                <h4 className="font-medium mb-2 text-sm">Error Details (Development):</h4>
                <pre className="text-xs text-muted-foreground overflow-auto">
                  {error.stack || error.message}
                </pre>
              </div>
            )}

            <div className="flex flex-wrap gap-2">
              <Button onClick={handleGoHome} className="flex items-center gap-2">
                <Home className="h-4 w-4" />
                Go Home
              </Button>
              
              <Button 
                variant="outline" 
                onClick={handleGoBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Go Back
              </Button>

              {showRetry && (
                <Button 
                  variant="outline" 
                  onClick={handleRetry}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Retry
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default RouteErrorBoundary;