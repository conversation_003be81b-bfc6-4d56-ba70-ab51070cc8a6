import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import RouteGuard from '../RouteGuard';
import RouteErrorBoundary from '../RouteErrorBoundary';

// Mock environment variables
const mockEnv = {
  VITE_SISENSE_URL: 'https://test-sisense.com',
  VITE_SISENSE_TOKEN: 'test-token',
};

// Mock import.meta.env
Object.defineProperty(import.meta, 'env', {
  value: mockEnv,
  writable: true,
});

describe('Router Components', () => {
  const TestComponent = () => <div>Test Content</div>;

  const renderWithRouter = (component: React.ReactElement, initialEntries = ['/']) => {
    return render(
      <HelmetProvider>
        <MemoryRouter initialEntries={initialEntries}>
          {component}
        </MemoryRouter>
      </HelmetProvider>
    );
  };

  describe('RouteGuard', () => {
    it('renders children when no guards are required', () => {
      renderWithRouter(
        <RouteGuard>
          <TestComponent />
        </RouteGuard>
      );

      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    it('renders children when Sisense is configured', () => {
      renderWithRouter(
        <RouteGuard requireSisenseConfig={true}>
          <TestComponent />
        </RouteGuard>
      );

      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    it('shows configuration error when Sisense is not configured', () => {
      // Mock missing environment variables
      const originalEnv = import.meta.env;
      Object.defineProperty(import.meta, 'env', {
        value: {},
        writable: true,
      });

      renderWithRouter(
        <RouteGuard requireSisenseConfig={true}>
          <TestComponent />
        </RouteGuard>
      );

      expect(screen.getByText('Sisense Configuration Required')).toBeInTheDocument();
      expect(screen.getByText(/VITE_SISENSE_URL/)).toBeInTheDocument();
      expect(screen.getByText(/VITE_SISENSE_TOKEN/)).toBeInTheDocument();

      // Restore original env
      Object.defineProperty(import.meta, 'env', {
        value: originalEnv,
        writable: true,
      });
    });

    it('shows authentication error when auth is required', () => {
      renderWithRouter(
        <RouteGuard requireAuth={true}>
          <TestComponent />
        </RouteGuard>
      );

      expect(screen.getByText('Authentication Required')).toBeInTheDocument();
      expect(screen.getByText(/log in to continue/)).toBeInTheDocument();
    });

    it('renders children when custom auth check passes', () => {
      renderWithRouter(
        <RouteGuard requireAuth={true} isAuthenticated={() => true}>
          <TestComponent />
        </RouteGuard>
      );

      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });
  });

  describe('RouteErrorBoundary', () => {
    it('renders error boundary component', () => {
      // Mock useRouteError to return a 404 error
      const mockError = {
        status: 404,
        statusText: 'Not Found',
        data: 'Page not found',
      };

      // We can't easily test the error boundary without triggering an actual error
      // This test just ensures the component can be imported and rendered
      expect(RouteErrorBoundary).toBeDefined();
    });
  });
});