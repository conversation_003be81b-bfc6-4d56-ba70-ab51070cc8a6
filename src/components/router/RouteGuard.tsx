import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, Settings, RefreshCw, Home, ArrowLeft } from 'lucide-react';

export interface RouteGuardProps {
  children: React.ReactNode;
  /** Whether authentication is required for this route */
  requireAuth?: boolean;
  /** Whether Sisense configuration is required */
  requireSisenseConfig?: boolean;

  /** Custom authentication check function */
  isAuthenticated?: () => boolean;
  /** Custom Sisense configuration check function */
  isSisenseConfigured?: () => boolean;
}

/**
 * Route guard component that handles authentication and configuration checks
 */
const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requireAuth = false,
  requireSisenseConfig = false,

  isAuthenticated = () => true, // Default to authenticated for now
  isSisenseConfigured = () => {
    // Check if Sisense environment variables are configured
    const sisenseUrl = import.meta.env.VITE_SISENSE_URL;
    const sisenseToken = import.meta.env.VITE_SISENSE_TOKEN;
    return !!(sisenseUrl && sisenseToken);
  },
}) => {
  const navigate = useNavigate();

  // Check authentication if required
  if (requireAuth && !isAuthenticated()) {
    // For now, show authentication required message instead of redirecting
    // since we don't have a login page implemented yet
    return (
      <div className="p-6">
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Authentication Required
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription>
                This page requires authentication to access. Please log in to continue.
              </AlertDescription>
            </Alert>
            
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => navigate('/')}
                className="flex items-center gap-2"
              >
                <Home className="h-4 w-4" />
                Go Home
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.history.back()}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check Sisense configuration if required
  if (requireSisenseConfig && !isSisenseConfigured()) {
    return (
      <div className="p-6">
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Sisense Configuration Required
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription>
                This page requires Sisense configuration to function properly. 
                Please ensure the following environment variables are set:
              </AlertDescription>
            </Alert>
            
            <div className="bg-muted p-4 rounded-md">
              <h4 className="font-medium mb-2">Required Environment Variables:</h4>
              <ul className="space-y-1 text-sm font-mono">
                <li>
                  <span className="text-muted-foreground">VITE_SISENSE_URL=</span>
                  <span className="text-foreground">your-sisense-instance-url</span>
                </li>
                <li>
                  <span className="text-muted-foreground">VITE_SISENSE_TOKEN=</span>
                  <span className="text-foreground">your-sisense-api-token</span>
                </li>
              </ul>
            </div>
            
            <div className="text-sm text-muted-foreground">
              <p>
                Add these variables to your <code className="bg-muted px-1 rounded">.env</code> file 
                in the project root, then restart the development server.
              </p>
            </div>
            
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => window.location.reload()}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Retry
              </Button>
              <Button 
                variant="outline" 
                onClick={() => navigate('/')}
                className="flex items-center gap-2"
              >
                <Home className="h-4 w-4" />
                Go Home
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.history.back()}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
};

export default RouteGuard;