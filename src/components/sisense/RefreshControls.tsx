import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { 
  RefreshCw, 
  Clock, 
  Settings, 
  Play, 
  Pause, 
  RotateCcw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface RefreshControlsProps {
  /** Whether refresh is currently in progress */
  isRefreshing?: boolean;
  /** Callback when manual refresh is triggered */
  onRefresh?: () => Promise<void> | void;
  /** Callback when auto-refresh is toggled */
  onAutoRefreshToggle?: (enabled: boolean) => void;
  /** Callback when refresh interval changes */
  onIntervalChange?: (interval: number) => void;
  /** Whether auto-refresh is enabled */
  autoRefreshEnabled?: boolean;
  /** Current refresh interval in milliseconds */
  refreshInterval?: number;
  /** Last refresh timestamp */
  lastRefreshTime?: number;
  /** Whether to show advanced controls */
  showAdvancedControls?: boolean;
  /** Custom className */
  className?: string;
  /** Button variant */
  variant?: 'default' | 'outline' | 'ghost';
  /** Button size */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  /** Whether to show the refresh status */
  showStatus?: boolean;
}

// Predefined refresh intervals (in milliseconds)
const REFRESH_INTERVALS = [
  { label: '30 seconds', value: 30000 },
  { label: '1 minute', value: 60000 },
  { label: '5 minutes', value: 300000 },
  { label: '10 minutes', value: 600000 },
  { label: '30 minutes', value: 1800000 },
  { label: '1 hour', value: 3600000 },
];

/**
 * Comprehensive refresh controls component
 * Provides manual refresh, auto-refresh, and refresh status functionality
 */
export const RefreshControls: React.FC<RefreshControlsProps> = ({
  isRefreshing = false,
  onRefresh,
  onAutoRefreshToggle,
  onIntervalChange,
  autoRefreshEnabled = false,
  refreshInterval = 300000, // 5 minutes default
  lastRefreshTime,
  showAdvancedControls = false,
  className,
  variant = 'outline',
  size = 'sm',
  showStatus = false,
}) => {
  const [refreshError, setRefreshError] = useState<string | null>(null);
  const [refreshSuccess, setRefreshSuccess] = useState(false);

  // Handle manual refresh
  const handleRefresh = useCallback(async () => {
    if (!onRefresh || isRefreshing) return;

    try {
      setRefreshError(null);
      setRefreshSuccess(false);
      
      await onRefresh();
      
      setRefreshSuccess(true);
      setTimeout(() => setRefreshSuccess(false), 2000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Refresh failed';
      setRefreshError(errorMessage);
      setTimeout(() => setRefreshError(null), 5000);
    }
  }, [onRefresh, isRefreshing]);

  // Handle auto-refresh toggle
  const handleAutoRefreshToggle = useCallback(() => {
    const newEnabled = !autoRefreshEnabled;
    onAutoRefreshToggle?.(newEnabled);
  }, [autoRefreshEnabled, onAutoRefreshToggle]);

  // Handle interval change
  const handleIntervalChange = useCallback((interval: number) => {
    onIntervalChange?.(interval);
  }, [onIntervalChange]);

  // Format last refresh time
  const formatLastRefresh = useCallback((timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) { // Less than 1 minute
      return 'Just now';
    } else if (diff < 3600000) { // Less than 1 hour
      const minutes = Math.floor(diff / 60000);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else {
      const hours = Math.floor(diff / 3600000);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    }
  }, []);

  // Get current interval label
  const getCurrentIntervalLabel = useCallback(() => {
    const interval = REFRESH_INTERVALS.find(i => i.value === refreshInterval);
    return interval?.label || 'Custom';
  }, [refreshInterval]);

  // Simple refresh button (default)
  if (!showAdvancedControls) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <Button
          variant={variant}
          size={size}
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="shrink-0"
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''} ${size === 'sm' ? 'mr-1' : 'mr-2'}`} />
          {size !== 'sm' && (
            <span className="hidden sm:inline">
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </span>
          )}
        </Button>

        {showStatus && (
          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
            {refreshSuccess && (
              <div className="flex items-center space-x-1 text-green-600">
                <CheckCircle className="h-3 w-3" />
                <span>Updated</span>
              </div>
            )}
            {refreshError && (
              <div className="flex items-center space-x-1 text-red-600">
                <AlertCircle className="h-3 w-3" />
                <span>Error</span>
              </div>
            )}
            {lastRefreshTime && !refreshSuccess && !refreshError && (
              <div className="flex items-center space-x-1">
                <Clock className="h-3 w-3" />
                <span>{formatLastRefresh(lastRefreshTime)}</span>
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  // Advanced refresh controls with dropdown
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Manual Refresh Button */}
      <Button
        variant={variant}
        size={size}
        onClick={handleRefresh}
        disabled={isRefreshing}
        className="shrink-0"
      >
        <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''} mr-2`} />
        {isRefreshing ? 'Refreshing...' : 'Refresh'}
      </Button>

      {/* Advanced Controls Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size={size} className="shrink-0">
            <Settings className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Refresh Settings</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {/* Auto-refresh toggle */}
          <DropdownMenuItem onClick={handleAutoRefreshToggle}>
            {autoRefreshEnabled ? (
              <Pause className="h-4 w-4 mr-2" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            {autoRefreshEnabled ? 'Disable' : 'Enable'} Auto-refresh
          </DropdownMenuItem>

          {/* Refresh interval options */}
          {autoRefreshEnabled && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Refresh Interval</DropdownMenuLabel>
              {REFRESH_INTERVALS.map((interval) => (
                <DropdownMenuItem
                  key={interval.value}
                  onClick={() => handleIntervalChange(interval.value)}
                  className={refreshInterval === interval.value ? 'bg-accent' : ''}
                >
                  <Clock className="h-4 w-4 mr-2" />
                  {interval.label}
                  {refreshInterval === interval.value && (
                    <CheckCircle className="h-3 w-3 ml-auto" />
                  )}
                </DropdownMenuItem>
              ))}
            </>
          )}

          <DropdownMenuSeparator />
          
          {/* Reset/Clear cache option */}
          <DropdownMenuItem onClick={() => window.location.reload()}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset Dashboard
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Status Display */}
      {showStatus && (
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          {autoRefreshEnabled && (
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span>Auto ({getCurrentIntervalLabel()})</span>
            </div>
          )}
          
          {refreshSuccess && (
            <div className="flex items-center space-x-1 text-green-600">
              <CheckCircle className="h-3 w-3" />
              <span>Updated</span>
            </div>
          )}
          
          {refreshError && (
            <div className="flex items-center space-x-1 text-red-600">
              <AlertCircle className="h-3 w-3" />
              <span title={refreshError}>Error</span>
            </div>
          )}
          
          {lastRefreshTime && !refreshSuccess && !refreshError && (
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{formatLastRefresh(lastRefreshTime)}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

interface RefreshStatusProps {
  /** Whether refresh is in progress */
  isRefreshing: boolean;
  /** Last refresh timestamp */
  lastRefreshTime?: number;
  /** Whether auto-refresh is enabled */
  autoRefreshEnabled?: boolean;
  /** Refresh interval in milliseconds */
  refreshInterval?: number;
  /** Custom className */
  className?: string;
}

/**
 * Standalone refresh status component
 * Shows current refresh state and timing information
 */
export const RefreshStatus: React.FC<RefreshStatusProps> = ({
  isRefreshing,
  lastRefreshTime,
  autoRefreshEnabled = false,
  refreshInterval: _refreshInterval = 300000, // Currently unused, reserved for future use
  className,
}) => {
  const [timeAgo, setTimeAgo] = useState<string>('');

  // Update time ago every minute
  useEffect(() => {
    if (!lastRefreshTime) return;

    const updateTimeAgo = () => {
      const now = Date.now();
      const diff = now - lastRefreshTime;
      
      if (diff < 60000) {
        setTimeAgo('Just now');
      } else if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        setTimeAgo(`${minutes}m ago`);
      } else {
        const hours = Math.floor(diff / 3600000);
        setTimeAgo(`${hours}h ago`);
      }
    };

    updateTimeAgo();
    const interval = setInterval(updateTimeAgo, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [lastRefreshTime]);

  return (
    <Card className={`border-dashed ${className}`}>
      <CardContent className="p-3">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-2">
            {isRefreshing ? (
              <>
                <RefreshCw className="h-3 w-3 animate-spin" />
                <span>Refreshing...</span>
              </>
            ) : (
              <>
                <Clock className="h-3 w-3" />
                <span>Last updated: {timeAgo || 'Never'}</span>
              </>
            )}
          </div>
          
          {autoRefreshEnabled && (
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span>Auto-refresh</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RefreshControls;