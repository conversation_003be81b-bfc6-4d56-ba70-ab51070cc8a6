import React from 'react';
import { WidgetGrid } from './WidgetGrid';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useResponsive } from '@/lib/responsive';
import type { ApiWidget } from '@/types/sisense';

// Mock widgets for demonstration
const mockWidgets: ApiWidget[] = [
  { id: 'demo-1', oid: 'demo-1', title: 'Revenue Chart', type: 'chart', subtype: 'column', isMap: false },
  { id: 'demo-2', oid: 'demo-2', title: 'Sales Table', type: 'table', subtype: 'pivot', isMap: false },
  { id: 'demo-3', oid: 'demo-3', title: 'Performance Line', type: 'chart', subtype: 'line', isMap: false },
  { id: 'demo-4', oid: 'demo-4', title: 'Geographic Map', type: 'map', subtype: 'area', isMap: true },
  { id: 'demo-5', oid: 'demo-5', title: 'KPI Indicators', type: 'indicator', subtype: 'numeric', isMap: false },
  { id: 'demo-6', oid: 'demo-6', title: 'Trend Analysis', type: 'chart', subtype: 'area', isMap: false }
];

/**
 * Demo component to showcase responsive grid layouts
 * This component demonstrates the responsive features implemented in task 6.1
 */
export const ResponsiveDemo: React.FC = () => {
  const { deviceType, isMobile, isTablet, screenSize } = useResponsive();
  const [currentConfig, setCurrentConfig] = React.useState<'compact' | 'standard' | 'spacious' | 'dense'>('standard');
  const [widgetSize, setWidgetSize] = React.useState<'small' | 'medium' | 'large' | 'auto'>('medium');

  return (
    <div className="p-4 space-y-6">
      {/* Responsive Information Panel */}
      <Card>
        <CardHeader>
          <CardTitle>Responsive Layout Demo</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">Device Type:</span>
              <br />
              <span className="capitalize">{deviceType}</span>
            </div>
            <div>
              <span className="font-medium">Screen Size:</span>
              <br />
              {screenSize.width} × {screenSize.height}
            </div>
            <div>
              <span className="font-medium">Mobile:</span>
              <br />
              {isMobile ? 'Yes' : 'No'}
            </div>
            <div>
              <span className="font-medium">Tablet:</span>
              <br />
              {isTablet ? 'Yes' : 'No'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Layout Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Grid Configuration:</label>
              <div className="flex flex-wrap gap-2">
                {(['compact', 'standard', 'spacious', 'dense'] as const).map((config) => (
                  <Button
                    key={config}
                    variant={currentConfig === config ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setCurrentConfig(config)}
                    className="capitalize"
                  >
                    {config}
                  </Button>
                ))}
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Widget Size:</label>
              <div className="flex flex-wrap gap-2">
                {(['small', 'medium', 'large', 'auto'] as const).map((size) => (
                  <Button
                    key={size}
                    variant={widgetSize === size ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setWidgetSize(size)}
                    className="capitalize"
                  >
                    {size}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Responsive Widget Grid Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Responsive Widget Grid</CardTitle>
          <p className="text-sm text-muted-foreground">
            This grid adapts to different screen sizes and touch interactions.
            Try resizing your browser window or changing the configuration above.
          </p>
        </CardHeader>
        <CardContent>
          <WidgetGrid
            widgets={mockWidgets}
            dashboardOid="demo-dashboard"
            responsiveConfig={currentConfig}
            widgetSize={widgetSize}
            touchFriendly={true}
            adaptiveLayout={true}
            enableScrolling={true}
            showWidgetTitles={true}
            showWidgetIds={false}
          />
        </CardContent>
      </Card>

      {/* Feature Highlights */}
      <Card>
        <CardHeader>
          <CardTitle>Responsive Features Implemented</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">✅ Responsive Breakpoints</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Mobile: &lt; 768px (1 column)</li>
                <li>• Tablet: 768px - 1024px (2-3 columns)</li>
                <li>• Desktop: 1024px - 1280px (3-4 columns)</li>
                <li>• Large Desktop: &gt; 1280px (4-6 columns)</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">✅ Touch-Friendly Navigation</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Larger touch targets on mobile</li>
                <li>• Touch feedback animations</li>
                <li>• Momentum scrolling support</li>
                <li>• Optimized tap highlights</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">✅ Scrollable Containers</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Responsive max heights</li>
                <li>• Smooth scrolling behavior</li>
                <li>• Overflow content handling</li>
                <li>• Custom scrollbar styling</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">✅ Adaptive Widget Sizing</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Device-specific widget sizes</li>
                <li>• Responsive padding and margins</li>
                <li>• Flexible grid configurations</li>
                <li>• Auto-adjusting column counts</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ResponsiveDemo;