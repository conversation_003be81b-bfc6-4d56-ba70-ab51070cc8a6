import { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { errorLoggingService } from '@/services/errorLoggingService';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  dashboardId?: string;
  onRetry?: () => void;
  onReset?: () => void;
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

/**
 * Error boundary specifically for dashboard-level errors
 * Provides contextual error messages and recovery options for dashboard failures
 */
export class DashboardErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Dashboard Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });

    // Log error with error logging service
    errorLoggingService.logReactError(error, errorInfo, {
      dashboardId: this.props.dashboardId,
      customData: {
        boundaryType: 'dashboard',
        retryCount: this.state.retryCount,
      }
    });
  }

  handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1,
      }));
      
      // Call external retry handler if provided
      this.props.onRetry?.();
    }
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    });
    
    // Call external reset handler if provided
    this.props.onReset?.();
  };

  private getErrorType(): 'dashboard' | 'widget' | 'network' | 'auth' | 'unknown' {
    const errorMessage = this.state.error?.message?.toLowerCase() || '';
    
    if (errorMessage.includes('dashboard') || errorMessage.includes('metadata')) {
      return 'dashboard';
    }
    if (errorMessage.includes('widget')) {
      return 'widget';
    }
    if (errorMessage.includes('network') || errorMessage.includes('fetch') || errorMessage.includes('timeout')) {
      return 'network';
    }
    if (errorMessage.includes('auth') || errorMessage.includes('token') || errorMessage.includes('unauthorized')) {
      return 'auth';
    }
    return 'unknown';
  }

  private getErrorMessage(): { title: string; description: string; suggestion: string } {
    const errorType = this.getErrorType();
    const dashboardName = this.props.dashboardId ? `Dashboard ${this.props.dashboardId}` : 'Dashboard';
    
    switch (errorType) {
      case 'dashboard':
        return {
          title: 'Dashboard Load Error',
          description: `Failed to load ${dashboardName}. The dashboard metadata could not be retrieved.`,
          suggestion: 'Try refreshing the dashboard or check if the dashboard exists in Sisense.',
        };
      case 'widget':
        return {
          title: 'Widget Error',
          description: `Some widgets in ${dashboardName} failed to load properly.`,
          suggestion: 'Try refreshing the dashboard or check widget permissions.',
        };
      case 'network':
        return {
          title: 'Connection Error',
          description: 'Unable to connect to the Sisense server.',
          suggestion: 'Check your internet connection and try again.',
        };
      case 'auth':
        return {
          title: 'Authentication Error',
          description: 'Your Sisense session has expired or is invalid.',
          suggestion: 'Please check your credentials and try again.',
        };
      default:
        return {
          title: 'Dashboard Error',
          description: `An unexpected error occurred while loading ${dashboardName}.`,
          suggestion: 'Try refreshing the page or contact support if the problem persists.',
        };
    }
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { title, description, suggestion } = this.getErrorMessage();
      const canRetry = this.state.retryCount < this.maxRetries;
      const showDetails = this.props.showDetails ?? process.env.NODE_ENV === 'development';

      return (
        <div className="flex items-center justify-center min-h-[300px] p-4">
          <Card className="w-full max-w-lg">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <CardTitle className="text-red-900 dark:text-red-100">
                {title}
              </CardTitle>
              <CardDescription className="text-center">
                {description}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {suggestion}
                </AlertDescription>
              </Alert>

              {showDetails && this.state.error && (
                <details className="text-sm">
                  <summary className="cursor-pointer font-medium text-gray-700 dark:text-gray-300 flex items-center">
                    <Bug className="h-4 w-4 mr-2" />
                    Technical Details
                  </summary>
                  <div className="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs">
                    <div className="mb-2">
                      <span className="font-medium">Error:</span>
                      <pre className="mt-1 whitespace-pre-wrap break-words text-red-600 dark:text-red-400">
                        {this.state.error.message}
                      </pre>
                    </div>
                    {this.state.error.stack && (
                      <div>
                        <span className="font-medium">Stack Trace:</span>
                        <pre className="mt-1 whitespace-pre-wrap break-words text-gray-600 dark:text-gray-400 max-h-32 overflow-y-auto">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}

              <div className="flex flex-col gap-2">
                {canRetry && (
                  <Button onClick={this.handleRetry} className="w-full">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Try Again ({this.maxRetries - this.state.retryCount} attempts left)
                  </Button>
                )}
                
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    onClick={this.handleReset} 
                    className="flex-1"
                  >
                    <Home className="mr-2 h-4 w-4" />
                    Reset Dashboard
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => window.location.reload()} 
                    className="flex-1"
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reload Page
                  </Button>
                </div>
              </div>

              {this.state.retryCount >= this.maxRetries && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Maximum retry attempts reached. Please reload the page or contact support.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default DashboardErrorBoundary;