
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { CheckCircle, AlertCircle } from 'lucide-react';

/**
 * Simple test component to verify Sisense context is working
 * This will be replaced by actual dashboard components in later tasks
 */
export function SisenseTestComponent() {
  const sisenseUrl = import.meta.env.VITE_INSTANCE_URL;
  const hasToken = !!import.meta.env.VITE_TOKEN;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          Sisense SDK Integration
        </CardTitle>
        <CardDescription>
          Sisense Compose SDK is configured and ready
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <span>Sisense URL:</span>
          <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
            {sisenseUrl || 'Not configured'}
          </span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span>Authentication:</span>
          <div className="flex items-center gap-2">
            {hasToken ? (
              <>
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-green-600">Token configured</span>
              </>
            ) : (
              <>
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-red-600">No token</span>
              </>
            )}
          </div>
        </div>
        <div className="text-xs text-muted-foreground mt-4 p-3 bg-muted rounded">
          <strong>Next steps:</strong> This component will be replaced with actual Sisense widgets 
          in the next implementation tasks. The SDK is now ready for dashboard and widget integration.
        </div>
      </CardContent>
    </Card>
  );
}

export default SisenseTestComponent;