import React, { useCallback, useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { 
  useResponsive,
  RESPONSIVE_CONTAINER_CLASSES,
  TOUCH_FRIENDLY_CLASSES,
  type ResponsiveGridConfig,
  RESPONSIVE_GRID_CONFIGS
} from '@/lib/responsive';

export interface ResponsiveLayoutProps {
  children: React.ReactNode;
  className?: string;
  enableScrolling?: boolean;
  maxHeight?: string;
  gridConfig?: keyof typeof RESPONSIVE_GRID_CONFIGS | ResponsiveGridConfig;
  onScroll?: (scrollTop: number, scrollLeft: number) => void;
  maintainScrollPosition?: boolean;
}

/**
 * ResponsiveLayout component provides enhanced mobile-first responsive layout
 * with optimized scrolling, touch interactions, and adaptive grid behavior
 */
export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  className,
  enableScrolling = true,
  maxHeight,
  gridConfig = 'standard',
  onScroll,
  maintainScrollPosition = false
}) => {
  const { 
    isMobile, 
    isTablet, 
    isTouch, 
    orientation
  } = useResponsive();
  
  const scrollRef = useRef<HTMLDivElement>(null);
  const [scrollPosition, setScrollPosition] = useState({ x: 0, y: 0 });

  // Get the appropriate grid configuration
  const effectiveGridConfig = typeof gridConfig === 'string' 
    ? RESPONSIVE_GRID_CONFIGS[gridConfig] || RESPONSIVE_GRID_CONFIGS.standard
    : gridConfig;

  // Handle scroll events with throttling
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const target = event.currentTarget;
    const newPosition = {
      x: target.scrollLeft,
      y: target.scrollTop
    };
    
    setScrollPosition(newPosition);
    onScroll?.(newPosition.y, newPosition.x);
  }, [onScroll]);

  // Restore scroll position when needed
  useEffect(() => {
    if (maintainScrollPosition && scrollRef.current) {
      scrollRef.current.scrollTo(scrollPosition.x, scrollPosition.y);
    }
  }, [maintainScrollPosition, scrollPosition]);

  // Determine effective max height based on device and configuration
  const effectiveMaxHeight = maxHeight || (() => {
    if (!effectiveGridConfig.maxHeight) return undefined;
    
    if (isMobile) return effectiveGridConfig.maxHeight.mobile;
    if (isTablet) return effectiveGridConfig.maxHeight.tablet;
    return effectiveGridConfig.maxHeight.desktop;
  })();

  // Container classes based on device type and configuration
  const containerClasses = cn(
    // Base container classes
    RESPONSIVE_CONTAINER_CLASSES.scrollableContent,
    
    // Device-specific optimizations
    isMobile && [
      RESPONSIVE_CONTAINER_CLASSES.mobileContent,
      'overscroll-y-contain', // Prevent bounce scrolling on iOS
      'scroll-smooth',
      '-webkit-overflow-scrolling-touch'
    ],
    
    // Touch-specific optimizations
    isTouch && TOUCH_FRIENDLY_CLASSES.scrollable,
    
    // Scrolling configuration
    enableScrolling && [
      'overflow-auto',
      'will-change-scroll'
    ],
    
    // Max height configuration
    effectiveMaxHeight && `max-h-[${effectiveMaxHeight}]`,
    
    // Orientation-specific adjustments
    orientation === 'landscape' && isMobile && [
      'max-h-[85vh]', // Reduce height in landscape mode
      'py-1' // Reduce vertical padding
    ],
    
    className
  );

  return (
    <div
      ref={scrollRef}
      className={containerClasses}
      onScroll={enableScrolling ? handleScroll : undefined}
      style={{
        // CSS custom properties for dynamic values
        '--scroll-padding-top': isMobile ? '1rem' : '1.5rem',
        '--scroll-padding-bottom': isMobile ? '1rem' : '1.5rem',
      } as React.CSSProperties}
    >
      {children}
    </div>
  );
};

export default ResponsiveLayout;