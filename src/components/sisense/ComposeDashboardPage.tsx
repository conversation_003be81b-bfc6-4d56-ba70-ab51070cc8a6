import React, { useEffect, useMemo } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import ComposeDashboard from './ComposeDashboard';

import { 
  getDashboardByIdOrSlug, 
  getDashboardPath, 
  getFirstDashboard,
  generateBreadcrumbs,
  urlUtils 
} from '@/lib/navigation';
import { DASHBOARDS } from '@/config/dashboards';
import type { ComposeDashboardProps } from './ComposeDashboard';

export interface ComposeDashboardPageProps extends Omit<ComposeDashboardProps, 'defaultDashboardId' | 'onDashboardChange'> {
  /** Whether to update the URL when dashboard changes */
  updateUrl?: boolean;
  /** Whether to show breadcrumb navigation */
  showBreadcrumbs?: boolean;
}

/**
 * Route-aware wrapper for ComposeDashboard that handles URL parameters
 * and navigation integration
 */
const ComposeDashboardPage: React.FC<ComposeDashboardPageProps> = ({
  updateUrl = true,
  showBreadcrumbs = true,
  ...dashboardProps
}) => {
  const { dashboardId } = useParams<{ dashboardId?: string }>();
  const navigate = useNavigate();
  const location = useLocation();

  // Find the current dashboard configuration
  const currentDashboard = useMemo(() => {
    if (!dashboardId) return null;
    return getDashboardByIdOrSlug(dashboardId);
  }, [dashboardId]);

  // Handle dashboard changes by updating the URL
  const handleDashboardChange = React.useCallback((newDashboardId: string) => {
    if (!updateUrl) return;
    
    try {
      const dashboardPath = getDashboardPath(newDashboardId);
      navigate(dashboardPath, { replace: false });
    } catch (error) {
      console.error('Failed to navigate to dashboard:', error);
    }
  }, [navigate, updateUrl]);

  // Redirect to first dashboard if no dashboard is specified - DISABLED
  // This redirect is handled by the router now
  // useEffect(() => {
  //   if (!dashboardId) {
  //     const firstDashboard = getFirstDashboard();
  //     if (firstDashboard) {
  //       const dashboardPath = getDashboardPath(firstDashboard.id);
  //       navigate(dashboardPath, { replace: true });
  //     } else {
  //       // No dashboards available, redirect to home
  //       navigate('/', { replace: true });
  //     }
  //   }
  // }, [dashboardId, navigate]);

  // Handle invalid dashboard IDs
  useEffect(() => {
    if (dashboardId && !currentDashboard) {
      console.warn('Dashboard not found:', dashboardId, 'Available dashboards:', DASHBOARDS.map(d => ({ id: d.id, slug: d.slug })));
      // Dashboard ID is provided but doesn't exist, redirect to first dashboard
      const firstDashboard = getFirstDashboard();
      if (firstDashboard) {
        const dashboardPath = getDashboardPath(firstDashboard.id);
        navigate(dashboardPath, { replace: true });
      } else {
        // No dashboards available, redirect to home
        navigate('/', { replace: true });
      }
    }
  }, [dashboardId, currentDashboard, navigate]);

  // Generate page title and meta information
  const pageTitle = useMemo(() => {
    return urlUtils.getPageTitle(location.pathname);
  }, [location.pathname]);

  const pageDescription = useMemo(() => {
    if (currentDashboard) {
      return currentDashboard.description || `View ${currentDashboard.title} analytics and insights`;
    }
    return 'Interactive dashboard for Infrastructure Investment and Jobs Act (IIJA) data visualization';
  }, [currentDashboard]);

  // Generate breadcrumb data
  const breadcrumbs = useMemo(() => {
    return generateBreadcrumbs(location.pathname);
  }, [location.pathname]);

  return (
    <>
      {/* SEO and page metadata */}
      <Helmet>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:title" content={pageTitle} />
        <meta name="twitter:description" content={pageDescription} />
        
        {/* Structured data for better SEO */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": pageTitle,
            "description": pageDescription,
            "url": `${window.location.origin}${location.pathname}`,
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Web Browser"
          })}
        </script>
      </Helmet>

      {/* Dashboard page header with consistent spacing */}
      <div className="dashboard-page-header">
        {/* Page title and description */}
        <div className="page-header-consistent">
          <h1 className="page-title-consistent">
            {currentDashboard?.title || 'IIJA Dashboard'}
          </h1>
          <p className="page-description-consistent">
            {currentDashboard?.description || 'Infrastructure Investment and Jobs Act analytics and insights'}
          </p>
        </div>

        {/* Breadcrumb navigation */}
        {showBreadcrumbs && breadcrumbs.length > 2 && (
          <nav className="mb-4" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm text-muted-foreground">
              {breadcrumbs.map((crumb, index) => (
                <li key={crumb.href} className="flex items-center">
                  {index > 0 && (
                    <span className="mx-2 text-muted-foreground/50">/</span>
                  )}
                  {index === breadcrumbs.length - 1 ? (
                    <span className="font-medium text-foreground" aria-current="page">
                      {crumb.label}
                    </span>
                  ) : (
                    <a
                      href={crumb.href}
                      className="hover:text-foreground transition-colors"
                      onClick={(e) => {
                        e.preventDefault();
                        navigate(crumb.href);
                      }}
                    >
                      {crumb.label}
                    </a>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        )}
      </div>

      {/* Main dashboard component with consistent layout */}
      <div className="dashboard-container">
        <ComposeDashboard
          {...dashboardProps}
          defaultDashboardId={dashboardId}
          onDashboardChange={handleDashboardChange}
          className="w-full"
        />
      </div>
    </>
  );
};

export default ComposeDashboardPage;