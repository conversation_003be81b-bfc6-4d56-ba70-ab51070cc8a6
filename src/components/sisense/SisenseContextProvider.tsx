import type { ReactNode } from 'react';
import { SisenseContextProvider as SisenseProvider } from '@sisense/sdk-ui';

interface SisenseContextProviderProps {
  children: ReactNode;
  url?: string;
  token?: string;
}

/**
 * Wrapper component for Sisense Context Provider with authentication configuration
 * Uses environment variables for URL and token if not provided as props
 */
export function SisenseContextProvider({ 
  children, 
  url = import.meta.env.VITE_INSTANCE_URL,
  token = import.meta.env.VITE_TOKEN 
}: SisenseContextProviderProps) {
  // Validate required configuration
  if (!url) {
    throw new Error('Sisense URL is required. Please set VITE_INSTANCE_URL environment variable or pass url prop.');
  }

  if (!token) {
    throw new Error('Sisense token is required. Please set VITE_TOKEN environment variable or pass token prop.');
  }

  return (
    <SisenseProvider
      url={url}
      token={token}
      appConfig={{
        // Enable query caching for better performance
        queryCacheConfig: {
          enabled: true,
        },
        // Configure tracking for analytics (optional)
        trackingConfig: {
          enabled: false,
        },
      }}
    >
      {children}
    </SisenseProvider>
  );
}

export default SisenseContextProvider;