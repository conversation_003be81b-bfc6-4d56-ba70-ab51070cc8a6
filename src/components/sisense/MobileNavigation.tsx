import React, { useCallback, useRef, useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { 
  useResponsive,
  RESPONSIVE_TAB_CLASSES,
  TOUCH_FRIENDLY_CLASSES
} from '@/lib/responsive';

export interface MobileNavigationItem {
  id: string;
  title: string;
  isActive?: boolean;
  disabled?: boolean;
}

export interface MobileNavigationProps {
  items: MobileNavigationItem[];
  activeId?: string;
  onItemSelect: (id: string) => void;
  className?: string;
  showScrollIndicators?: boolean;
  enableSwipeGestures?: boolean;
}

/**
 * MobileNavigation component provides touch-optimized horizontal navigation
 * with swipe gestures, scroll indicators, and enhanced mobile UX
 */
export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  items,
  activeId,
  onItemSelect,
  className,
  showScrollIndicators = true,
  enableSwipeGestures = true
}) => {
  const { isMobile, isTouch } = useResponsive();
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  // Touch gesture state
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);
  const [touchEnd, setTouchEnd] = useState<{ x: number; y: number } | null>(null);

  // Update scroll indicators
  const updateScrollIndicators = useCallback(() => {
    if (!scrollRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
    setCanScrollLeft(scrollLeft > 0);
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
  }, []);

  // Handle scroll events
  const handleScroll = useCallback(() => {
    updateScrollIndicators();
    setIsScrolling(true);
    
    // Clear scrolling state after a delay
    const timeoutId = setTimeout(() => setIsScrolling(false), 150);
    return () => clearTimeout(timeoutId);
  }, [updateScrollIndicators]);

  // Scroll to specific item
  const scrollToItem = useCallback((itemId: string) => {
    if (!scrollRef.current) return;

    const itemElement = scrollRef.current.querySelector(`[data-item-id="${itemId}"]`);
    if (itemElement) {
      itemElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center'
      });
    }
  }, []);

  // Handle manual scroll buttons
  const scrollLeft = useCallback(() => {
    if (!scrollRef.current) return;
    
    const scrollAmount = scrollRef.current.clientWidth * 0.8;
    scrollRef.current.scrollBy({
      left: -scrollAmount,
      behavior: 'smooth'
    });
  }, []);

  const scrollRight = useCallback(() => {
    if (!scrollRef.current) return;
    
    const scrollAmount = scrollRef.current.clientWidth * 0.8;
    scrollRef.current.scrollBy({
      left: scrollAmount,
      behavior: 'smooth'
    });
  }, []);

  // Touch gesture handlers
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!enableSwipeGestures) return;
    
    const touch = e.touches[0];
    setTouchStart({ x: touch.clientX, y: touch.clientY });
    setTouchEnd(null);
  }, [enableSwipeGestures]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!enableSwipeGestures || !touchStart) return;
    
    const touch = e.touches[0];
    setTouchEnd({ x: touch.clientX, y: touch.clientY });
  }, [enableSwipeGestures, touchStart]);

  const handleTouchEnd = useCallback(() => {
    if (!enableSwipeGestures || !touchStart || !touchEnd) return;

    const deltaX = touchStart.x - touchEnd.x;
    const deltaY = touchStart.y - touchEnd.y;
    const minSwipeDistance = 50;

    // Only handle horizontal swipes
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
      if (deltaX > 0 && canScrollRight) {
        scrollRight();
      } else if (deltaX < 0 && canScrollLeft) {
        scrollLeft();
      }
    }

    setTouchStart(null);
    setTouchEnd(null);
  }, [enableSwipeGestures, touchStart, touchEnd, canScrollLeft, canScrollRight, scrollLeft, scrollRight]);

  // Handle item selection
  const handleItemSelect = useCallback((itemId: string) => {
    onItemSelect(itemId);
    scrollToItem(itemId);
  }, [onItemSelect, scrollToItem]);

  // Update scroll indicators on mount and when items change
  useEffect(() => {
    updateScrollIndicators();
    
    // Set up resize observer to update indicators when container size changes
    if (!scrollRef.current) return;

    const resizeObserver = new ResizeObserver(updateScrollIndicators);
    resizeObserver.observe(scrollRef.current);

    return () => resizeObserver.disconnect();
  }, [items, updateScrollIndicators]);

  // Scroll to active item when it changes
  useEffect(() => {
    if (activeId) {
      scrollToItem(activeId);
    }
  }, [activeId, scrollToItem]);

  if (!isMobile && !isTouch) {
    // Render standard navigation for non-mobile devices
    return (
      <div className={cn('flex flex-wrap gap-2', className)}>
        {items.map((item) => (
          <Button
            key={item.id}
            variant={item.id === activeId ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleItemSelect(item.id)}
            disabled={item.disabled}
            className="flex-shrink-0"
          >
            {item.title}
          </Button>
        ))}
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      {/* Scroll indicators */}
      {showScrollIndicators && (
        <>
          {canScrollLeft && (
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                'absolute left-0 top-1/2 -translate-y-1/2 z-10',
                'bg-background/80 backdrop-blur-sm border border-border/50',
                'shadow-lg rounded-full w-8 h-8 p-0',
                TOUCH_FRIENDLY_CLASSES.interactive
              )}
              onClick={scrollLeft}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          )}
          
          {canScrollRight && (
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                'absolute right-0 top-1/2 -translate-y-1/2 z-10',
                'bg-background/80 backdrop-blur-sm border border-border/50',
                'shadow-lg rounded-full w-8 h-8 p-0',
                TOUCH_FRIENDLY_CLASSES.interactive
              )}
              onClick={scrollRight}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}
        </>
      )}

      {/* Navigation items */}
      <div
        ref={scrollRef}
        className={cn(
          RESPONSIVE_TAB_CLASSES.mobileTabsList,
          'relative',
          // Add padding for scroll indicators
          showScrollIndicators && canScrollLeft && 'pl-10',
          showScrollIndicators && canScrollRight && 'pr-10'
        )}
        onScroll={handleScroll}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {items.map((item) => (
          <button
            key={item.id}
            data-item-id={item.id}
            onClick={() => handleItemSelect(item.id)}
            disabled={item.disabled}
            className={cn(
              RESPONSIVE_TAB_CLASSES.mobileTabTrigger,
              // Active state styling
              item.id === activeId && [
                'bg-primary',
                'text-primary-foreground',
                'border-primary',
                'shadow-sm'
              ],
              // Disabled state styling
              item.disabled && [
                'opacity-50',
                'cursor-not-allowed',
                'pointer-events-none'
              ],
              // Scrolling state styling
              isScrolling && 'pointer-events-none'
            )}
          >
            <span className="truncate font-medium">
              {item.title}
            </span>
          </button>
        ))}
      </div>

      {/* Gradient overlays for visual scroll indication */}
      {showScrollIndicators && (
        <>
          {canScrollLeft && (
            <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-background to-transparent pointer-events-none z-5" />
          )}
          {canScrollRight && (
            <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-background to-transparent pointer-events-none z-5" />
          )}
        </>
      )}
    </div>
  );
};

export default MobileNavigation;