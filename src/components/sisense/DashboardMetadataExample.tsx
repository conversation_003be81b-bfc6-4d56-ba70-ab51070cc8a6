import React from 'react';
import { useDashboardManager } from '@/hooks/useDashboardManager';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { RefreshCw, AlertCircle, CheckCircle, XCircle, Info } from 'lucide-react';

/**
 * Example component demonstrating dashboard metadata management functionality
 * This component showcases the enhanced dashboard metadata management features
 * including loading, caching, error handling, and refresh functionality.
 */
export const DashboardMetadataExample: React.FC = () => {
  const {
    // State
    dashboards,
    selectedDashboardId,
    selectedDashboard,
    
    // Loading states
    isLoadingMetadata,
    isRefreshing,
    
    // Error states
    metadataError,
    
    // Health and config
    healthStatus,
    configValidation,
    
    // Actions
    selectDashboard,
    refreshAll,
    refreshMetadata,
    retryLastOperation,
    clearErrors,
    reset,
    
    // Utilities
    getNextDashboard,
    getPreviousDashboard,
    
    // Cache management
    cacheStats,
    clearCache
  } = useDashboardManager({
    autoLoadWidgets: false, // Don't load widgets for this example
    enableCaching: true,
    enableHealthCheck: true,
    onDashboardChange: (dashboardId) => {
      console.log('Dashboard changed to:', dashboardId);
    },
    onError: (error) => {
      console.error('Dashboard error:', error);
    },
    onLoadingChange: (isLoading) => {
      console.log('Loading state changed:', isLoading);
    }
  });

  const getHealthStatusIcon = () => {
    switch (healthStatus) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'unhealthy':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getHealthStatusText = () => {
    switch (healthStatus) {
      case 'healthy':
        return 'Healthy';
      case 'unhealthy':
        return 'Unhealthy';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Dashboard Metadata Management</h1>
        <div className="flex items-center gap-2">
          <Button
            onClick={refreshAll}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh All'}
          </Button>
          <Button
            onClick={reset}
            variant="outline"
            size="sm"
          >
            Reset
          </Button>
        </div>
      </div>

      {/* System Status */}
      <Card>
        <CardContent className="p-4">
          <h2 className="text-lg font-semibold mb-3">System Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              {getHealthStatusIcon()}
              <span className="text-sm">
                Health: <strong>{getHealthStatusText()}</strong>
              </span>
            </div>
            <div className="flex items-center gap-2">
              {configValidation.isValid ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm">
                Config: <strong>{configValidation.isValid ? 'Valid' : 'Invalid'}</strong>
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-blue-500" />
              <span className="text-sm">
                Cache: <strong>{cacheStats.size} entries</strong>
              </span>
            </div>
          </div>
          
          {!configValidation.isValid && (
            <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-700 font-medium">Configuration Errors:</p>
              <ul className="text-sm text-red-600 mt-1 list-disc list-inside">
                {configValidation.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Error Display */}
      {metadataError && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
              <div className="flex-1">
                <h3 className="text-sm font-medium text-red-800">
                  Error ({metadataError.type})
                </h3>
                <p className="text-sm text-red-700 mt-1">
                  {metadataError.message}
                </p>
                <div className="flex gap-2 mt-3">
                  <Button
                    onClick={retryLastOperation}
                    size="sm"
                    variant="outline"
                    className="text-red-700 border-red-300 hover:bg-red-100"
                  >
                    Retry
                  </Button>
                  <Button
                    onClick={clearErrors}
                    size="sm"
                    variant="ghost"
                    className="text-red-700 hover:bg-red-100"
                  >
                    Dismiss
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dashboard Selection */}
      <Card>
        <CardContent className="p-4">
          <h2 className="text-lg font-semibold mb-3">Dashboard Selection</h2>
          
          {selectedDashboard && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="font-medium text-blue-900">{selectedDashboard.title}</h3>
              <p className="text-sm text-blue-700 mt-1">{selectedDashboard.description}</p>
              <div className="flex gap-4 mt-2 text-xs text-blue-600">
                <span>ID: {selectedDashboard.id}</span>
                <span>Slug: {selectedDashboard.slug}</span>
                {selectedDashboard.widgetCount && (
                  <span>Widgets: {selectedDashboard.widgetCount}</span>
                )}
                {selectedDashboard.hasWidgetsTabber && (
                  <span className="bg-blue-200 px-2 py-0.5 rounded">Tabbed</span>
                )}
              </div>
            </div>
          )}

          <div className="flex gap-2 mb-4">
            <Button
              onClick={() => {
                const prev = getPreviousDashboard();
                if (prev) selectDashboard(prev.id);
              }}
              size="sm"
              variant="outline"
              disabled={!selectedDashboardId || dashboards.length <= 1}
            >
              Previous
            </Button>
            <Button
              onClick={() => {
                const next = getNextDashboard();
                if (next) selectDashboard(next.id);
              }}
              size="sm"
              variant="outline"
              disabled={!selectedDashboardId || dashboards.length <= 1}
            >
              Next
            </Button>
            <Separator orientation="vertical" className="h-8" />
            <Button
              onClick={refreshMetadata}
              size="sm"
              variant="outline"
              disabled={isLoadingMetadata}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingMetadata ? 'animate-spin' : ''}`} />
              Refresh Metadata
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
            {dashboards.map((dashboard) => (
              <button
                key={dashboard.id}
                onClick={() => selectDashboard(dashboard.id)}
                className={`p-3 text-left border rounded-md transition-colors ${
                  selectedDashboardId === dashboard.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="font-medium text-sm">
                  {dashboard.title.replace(/_SDK$/, '').replace(/^\d+\.\s*/, '')}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {dashboard.widgetCount} widgets
                  {dashboard.hasWidgetsTabber && (
                    <span className="ml-2 bg-gray-200 px-1 py-0.5 rounded text-xs">
                      Tabbed
                    </span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Cache Management */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold">Cache Management</h2>
            <Button
              onClick={clearCache}
              size="sm"
              variant="outline"
              disabled={cacheStats.size === 0}
            >
              Clear Cache
            </Button>
          </div>
          
          <div className="text-sm text-gray-600 mb-3">
            Cache contains {cacheStats.size} entries
          </div>

          {cacheStats.entries.length > 0 && (
            <div className="space-y-2">
              {cacheStats.entries.map((entry, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
                >
                  <span className="font-mono">{entry.dashboardId}</span>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <span>Age: {Math.round(entry.age / 1000)}s</span>
                    {entry.isExpired && (
                      <span className="bg-red-100 text-red-700 px-2 py-0.5 rounded">
                        Expired
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Loading State */}
      {(isLoadingMetadata || isRefreshing) && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
              <span className="text-blue-700">
                {isLoadingMetadata ? 'Loading metadata...' : 'Refreshing...'}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};