import React, { useCallback, useMemo } from 'react';
import { ComposeWidgetById } from './ComposeWidgetById';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import type { ApiWidget } from '@/types/sisense';
import { cn, filterWidgets, isValidWidget } from '@/lib/utils';
import { useOptimizedIntersectionObserver } from '@/lib/performanceOptimizations';

export interface LazyWidgetGridProps {
  widgets: ApiWidget[];
  dashboardOid: string;
  className?: string;
  columns?: number;
  showWidgetTitles?: boolean;
  showWidgetIds?: boolean;
  widgetHeight?: number | 'auto';
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  enableScrolling?: boolean;
  maxHeight?: string;
  autoColumns?: boolean;
  // Lazy loading specific props
  rootMargin?: string;
  threshold?: number;
  loadingPlaceholderHeight?: number;
}

interface LazyWidgetItemProps {
  widget: ApiWidget;
  widgetOid: string;
  dashboardOid: string;
  showWidgetTitles: boolean;
  showWidgetIds: boolean;
  widgetHeight: number | 'auto';
  onError: (widgetOid: string, error: Error) => void;
  onLoad: (widgetOid: string) => void;
  rootMargin?: string;
  threshold?: number;
  loadingPlaceholderHeight?: number;
}

// Use the optimized intersection observer from performance utilities

// Memoized lazy widget item component
const LazyWidgetItem = React.memo<LazyWidgetItemProps>(({
  widget,
  widgetOid,
  dashboardOid,
  showWidgetTitles,
  showWidgetIds,
  widgetHeight,
  onError,
  onLoad,
  rootMargin = '100px',
  threshold = 0.1,
  loadingPlaceholderHeight = 300
}) => {
  const { ref: containerRef, isIntersecting: isVisible } = useOptimizedIntersectionObserver({
    rootMargin,
    threshold,
    freezeOnceVisible: true
  });

  const handleError = useCallback((error: Error) => {
    onError(widgetOid, error);
  }, [widgetOid, onError]);

  const handleLoad = useCallback(() => {
    onLoad(widgetOid);
  }, [widgetOid, onLoad]);

  // Render placeholder while not visible
  if (!isVisible) {
    return (
      <Card
        ref={containerRef}
        className={cn(
          "min-h-0 flex flex-col",
          widgetHeight === 'auto' ? 'h-auto' : 'h-full',
          "border border-border/30"
        )}
      >
        <CardContent className="flex-1 p-2 sm:p-3 md:p-4">
          <div className="space-y-3">
            {showWidgetTitles && widget.title && (
              <Skeleton className="h-5 w-3/4" />
            )}
            <Skeleton 
              className="w-full rounded-lg"
              style={{ 
                height: widgetHeight === 'auto' 
                  ? `${loadingPlaceholderHeight}px` 
                  : `${widgetHeight}px` 
              }}
            />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render actual widget when visible
  return (
    <Card
      ref={containerRef}
      className={cn(
        "min-h-0 flex flex-col",
        widgetHeight === 'auto' ? 'h-auto' : 'h-full',
        // Enhanced interaction feedback with touch support
        "transition-all duration-200 ease-in-out",
        "hover:shadow-md hover:scale-[1.02]",
        // Touch-friendly interactions
        "touch-manipulation",
        "active:scale-[0.98]",
        // Mobile-specific styling
        "border border-border/50 hover:border-border",
        // Accessibility improvements
        "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
      )}
    >
      <CardContent className={cn(
        "flex-1",
        "p-2 sm:p-3 md:p-4",
        widgetHeight === 'auto' ? 'h-auto' : 'h-full'
      )}>
        <ComposeWidgetById
          widgetOid={widgetOid}
          dashboardOid={dashboardOid}
          title={widget.title || undefined}
          showTitle={showWidgetTitles}
          showWidgetId={showWidgetIds}
          height={widgetHeight === 'auto' ? undefined : widgetHeight}
          className="h-full"
          onError={handleError}
          onLoad={handleLoad}
        />
      </CardContent>
    </Card>
  );
});

LazyWidgetItem.displayName = 'LazyWidgetItem';

// Grid configuration function (same as WidgetGrid)
const getGridConfig = (
  columns?: number, 
  gap: 'sm' | 'md' | 'lg' | 'xl' = 'md',
  widgetCount: number = 0,
  autoColumns: boolean = true
) => {
  let columnClasses: string;
  
  if (columns) {
    const maxCols = Math.min(columns, 6);
    columnClasses = [
      'grid-cols-1',
      maxCols >= 2 ? 'sm:grid-cols-2' : '',
      maxCols >= 3 ? 'md:grid-cols-3' : '',
      maxCols >= 4 ? 'lg:grid-cols-4' : '',
      maxCols >= 5 ? 'xl:grid-cols-5' : '',
      maxCols >= 6 ? '2xl:grid-cols-6' : ''
    ].filter(Boolean).join(' ');
  } else if (autoColumns && widgetCount > 0) {
    if (widgetCount === 1) {
      columnClasses = 'grid-cols-1';
    } else if (widgetCount === 2) {
      columnClasses = 'grid-cols-1 md:grid-cols-2';
    } else if (widgetCount === 3) {
      columnClasses = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
    } else if (widgetCount === 4) {
      columnClasses = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4';
    } else if (widgetCount <= 6) {
      columnClasses = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
    } else {
      columnClasses = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
    }
  } else {
    columnClasses = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
  }
  
  const gapClasses = {
    sm: 'gap-2 sm:gap-3',
    md: 'gap-3 sm:gap-4',
    lg: 'gap-4 sm:gap-6',
    xl: 'gap-6 sm:gap-8'
  };

  return {
    columns: columnClasses,
    gap: gapClasses[gap],
    containerClasses: 'w-full min-h-0'
  };
};

export const LazyWidgetGrid: React.FC<LazyWidgetGridProps> = React.memo(({
  widgets,
  dashboardOid,
  className,
  columns,
  showWidgetTitles = true,
  showWidgetIds = false,
  widgetHeight = 400,
  gap = 'md',
  enableScrolling = false,
  maxHeight,
  autoColumns = true,
  rootMargin = '100px',
  threshold = 0.1,
  loadingPlaceholderHeight = 300
}) => {
  // Memoize filtered widgets to prevent unnecessary re-filtering
  const filteredWidgets = useMemo(() => {
    return filterWidgets(widgets, {
      excludeBlox: true,
      excludeTabbers: true,
      customFilter: isValidWidget
    });
  }, [widgets]);

  // Memoize grid configuration to prevent unnecessary recalculations
  const gridConfig = useMemo(() => {
    return getGridConfig(columns, gap, filteredWidgets.length, autoColumns);
  }, [columns, gap, filteredWidgets.length, autoColumns]);

  // Memoize error handler to prevent unnecessary re-renders
  const handleWidgetError = useCallback((widgetOid: string, error: Error) => {
    console.error(`Widget ${widgetOid} failed to load:`, error);
  }, []);

  // Memoize load handler to prevent unnecessary re-renders
  const handleWidgetLoad = useCallback((widgetOid: string) => {
    console.debug(`Widget ${widgetOid} loaded successfully`);
  }, []);

  if (!filteredWidgets || filteredWidgets.length === 0) {
    return (
      <div className={cn(
        "flex items-center justify-center p-8",
        enableScrolling && maxHeight && `max-h-[${maxHeight}]`,
        className
      )}>
        <Card className="max-w-md">
          <CardContent className="p-8 text-center">
            <div className="text-muted-foreground">
              <div className="text-lg font-medium mb-2">No widgets available</div>
              <div className="text-sm">
                {widgets.length > 0 
                  ? `${widgets.length} widgets were filtered out (excluded or invalid)`
                  : "This dashboard doesn't have any widgets to display."
                }
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Container classes with scrolling support optimized for Card components
  const containerClasses = cn(
    "grid",
    gridConfig.containerClasses,
    gridConfig.columns,
    gridConfig.gap,
    // Height and scrolling configuration
    widgetHeight === 'auto' ? 'auto-rows-max' : 'auto-rows-fr',
    // Proper alignment for Card components
    "items-start",
    // Scrolling configuration
    enableScrolling && "overflow-auto",
    enableScrolling && maxHeight && `max-h-[${maxHeight}]`,
    // Performance optimizations
    enableScrolling && "will-change-scroll",
    // Touch-friendly scrolling
    enableScrolling && "overscroll-contain",
    enableScrolling && "scroll-smooth",
    // Mobile-specific optimizations
    "touch-pan-y touch-pan-x",
    // Padding for Card shadows with responsive adjustments
    "p-1 sm:p-2",
    className
  );

  return (
    <div className={containerClasses}>
      {filteredWidgets.map((widget) => {
        const widgetOid = widget.oid || widget.id;
        
        if (!widgetOid) {
          console.warn('Widget missing OID/ID:', widget);
          return null;
        }

        return (
          <LazyWidgetItem
            key={widgetOid}
            widget={widget}
            widgetOid={widgetOid}
            dashboardOid={dashboardOid}
            showWidgetTitles={showWidgetTitles}
            showWidgetIds={showWidgetIds}
            widgetHeight={widgetHeight}
            onError={handleWidgetError}
            onLoad={handleWidgetLoad}
            rootMargin={rootMargin}
            threshold={threshold}
            loadingPlaceholderHeight={loadingPlaceholderHeight}
          />
        );
      })}
    </div>
  );
});

LazyWidgetGrid.displayName = 'LazyWidgetGrid';

export default LazyWidgetGrid;