import { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, Home, Bug, Mail } from 'lucide-react';
import { errorLoggingService } from '@/services/errorLoggingService';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showReportButton?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

/**
 * Global error boundary for unhandled application errors
 * Provides comprehensive error reporting and recovery options
 */
export class GlobalErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Generate unique error ID for tracking
    const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Global Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Log error with error logging service
    const errorId = errorLoggingService.logReactError(error, errorInfo, {
      customData: {
        boundaryType: 'global',
        category: this.getErrorCategory(),
      }
    });

    // Update state with the generated error ID
    this.setState({ errorId });

    // Call external error handler if provided
    this.props.onError?.(error, errorInfo);
  }



  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  handleReportIssue = () => {
    if (!this.state.errorId) return;

    // Get detailed error report from error logging service
    const errorReport = errorLoggingService.createErrorReport(this.state.errorId);

    // Create mailto link with error details
    const subject = encodeURIComponent(`Application Error Report - ${this.state.errorId}`);
    const body = encodeURIComponent(`
${errorReport}

Please describe what you were doing when this error occurred:
[Your description here]
    `.trim());

    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  private getErrorCategory(): 'critical' | 'recoverable' | 'unknown' {
    const errorMessage = this.state.error?.message?.toLowerCase() || '';
    const errorStack = this.state.error?.stack?.toLowerCase() || '';
    
    // Critical errors that likely require page reload
    if (
      errorMessage.includes('chunk') ||
      errorMessage.includes('loading') ||
      errorMessage.includes('module') ||
      errorStack.includes('webpack') ||
      errorStack.includes('vite')
    ) {
      return 'critical';
    }
    
    // Recoverable errors that might be fixed with retry
    if (
      errorMessage.includes('network') ||
      errorMessage.includes('fetch') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('sisense')
    ) {
      return 'recoverable';
    }
    
    return 'unknown';
  }

  private getErrorMessage(): { title: string; description: string; suggestion: string } {
    const category = this.getErrorCategory();
    
    switch (category) {
      case 'critical':
        return {
          title: 'Application Error',
          description: 'A critical error occurred that requires reloading the application.',
          suggestion: 'Please reload the page. If the problem persists, try clearing your browser cache.',
        };
      case 'recoverable':
        return {
          title: 'Connection Error',
          description: 'A network or service error occurred.',
          suggestion: 'This might be a temporary issue. Try again in a moment.',
        };
      default:
        return {
          title: 'Unexpected Error',
          description: 'An unexpected error occurred in the application.',
          suggestion: 'Try refreshing the page or contact support if the problem continues.',
        };
    }
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { title, description, suggestion } = this.getErrorMessage();
      const category = this.getErrorCategory();
      const showDetails = process.env.NODE_ENV === 'development';
      const showReportButton = this.props.showReportButton ?? true;

      return (
        <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
          <Card className="w-full max-w-2xl">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
              <CardTitle className="text-2xl text-red-900 dark:text-red-100">
                {title}
              </CardTitle>
              <CardDescription className="text-center text-base">
                {description}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {suggestion}
                </AlertDescription>
              </Alert>

              {this.state.errorId && (
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">
                    Error ID: <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-xs">{this.state.errorId}</code>
                  </p>
                </div>
              )}

              {showDetails && this.state.error && (
                <details className="text-sm">
                  <summary className="cursor-pointer font-medium text-gray-700 dark:text-gray-300 flex items-center">
                    <Bug className="h-4 w-4 mr-2" />
                    Technical Details
                  </summary>
                  <div className="mt-3 p-4 bg-gray-100 dark:bg-gray-800 rounded">
                    <div className="mb-3">
                      <span className="font-medium">Error Message:</span>
                      <pre className="mt-1 whitespace-pre-wrap break-words text-red-600 dark:text-red-400 text-xs">
                        {this.state.error.message}
                      </pre>
                    </div>
                    {this.state.error.stack && (
                      <div className="mb-3">
                        <span className="font-medium">Stack Trace:</span>
                        <pre className="mt-1 whitespace-pre-wrap break-words text-gray-600 dark:text-gray-400 text-xs max-h-40 overflow-y-auto">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                    {this.state.errorInfo?.componentStack && (
                      <div>
                        <span className="font-medium">Component Stack:</span>
                        <pre className="mt-1 whitespace-pre-wrap break-words text-gray-600 dark:text-gray-400 text-xs max-h-32 overflow-y-auto">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}

              <div className="flex flex-col gap-3">
                {category === 'recoverable' && (
                  <Button onClick={this.handleRetry} className="w-full">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Try Again
                  </Button>
                )}
                
                <div className="flex gap-3">
                  <Button 
                    variant="outline" 
                    onClick={() => window.location.reload()} 
                    className="flex-1"
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reload Page
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => window.location.href = '/'} 
                    className="flex-1"
                  >
                    <Home className="mr-2 h-4 w-4" />
                    Go Home
                  </Button>
                </div>

                {showReportButton && (
                  <Button 
                    variant="secondary" 
                    onClick={this.handleReportIssue}
                    className="w-full"
                  >
                    <Mail className="mr-2 h-4 w-4" />
                    Report Issue
                  </Button>
                )}
              </div>

              <div className="text-center text-xs text-muted-foreground">
                <p>If this problem persists, please contact support with the error ID above.</p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default GlobalErrorBoundary;