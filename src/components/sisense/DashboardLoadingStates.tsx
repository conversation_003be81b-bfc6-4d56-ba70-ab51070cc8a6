import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Loader2, AlertTriangle, RefreshCw, Database, Wifi, WifiOff } from 'lucide-react';

interface DashboardSkeletonProps {
  /** Number of dashboard tabs to show in skeleton */
  tabCount?: number;
  /** Number of widget skeletons to show */
  widgetCount?: number;
  /** Whether to show the dashboard header skeleton */
  showHeader?: boolean;
  /** Custom className */
  className?: string;
}

/**
 * Dashboard loading skeleton component
 * Shows placeholder content while dashboard metadata and widgets are loading
 */
export const DashboardSkeleton: React.FC<DashboardSkeletonProps> = ({
  tabCount = 5,
  widgetCount = 6,
  showHeader = true,
  className,
}) => {
  return (
    <div className={className}>
      {/* Dashboard Header Skeleton */}
      {showHeader && (
        <div className="mb-4">
          <Skeleton className="h-8 w-1/3 mb-2" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      )}

      {/* Tab Navigation Skeleton */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex space-x-2">
          {Array.from({ length: tabCount }).map((_, index) => (
            <Skeleton key={index} className="h-10 w-24" />
          ))}
        </div>
        <Skeleton className="h-10 w-20" />
      </div>

      {/* Dashboard Content Skeleton */}
      <Card>
        <CardHeader className="pb-4">
          <Skeleton className="h-6 w-1/4 mb-2" />
          <Skeleton className="h-4 w-3/4" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: widgetCount }).map((_, index) => (
              <WidgetSkeleton key={index} />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

interface WidgetSkeletonProps {
  /** Height of the widget skeleton */
  height?: string;
  /** Whether to show the widget title skeleton */
  showTitle?: boolean;
  /** Custom className */
  className?: string;
}

/**
 * Individual widget loading skeleton
 * Shows placeholder content while a widget is loading
 */
export const WidgetSkeleton: React.FC<WidgetSkeletonProps> = ({
  height = 'h-48',
  showTitle = true,
  className,
}) => {
  return (
    <Card className={className}>
      <CardContent className="p-4">
        {showTitle && (
          <div className="mb-3">
            <Skeleton className="h-4 w-3/4 mb-1" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        )}
        <Skeleton className={`w-full ${height}`} />
      </CardContent>
    </Card>
  );
};

interface TabsSkeletonProps {
  /** Number of tabs to show in skeleton */
  tabCount?: number;
  /** Number of widgets per tab */
  widgetCount?: number;
  /** Custom className */
  className?: string;
}

/**
 * Tabbed content loading skeleton
 * Shows placeholder content for tabbed dashboard layouts
 */
export const TabsSkeleton: React.FC<TabsSkeletonProps> = ({
  tabCount = 3,
  widgetCount = 4,
  className,
}) => {
  return (
    <div className={className}>
      {/* Tab Headers Skeleton */}
      <div className="flex space-x-1 mb-4">
        {Array.from({ length: tabCount }).map((_, index) => (
          <Skeleton key={index} className="h-10 w-20" />
        ))}
      </div>

      {/* Tab Content Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Array.from({ length: widgetCount }).map((_, index) => (
          <WidgetSkeleton key={index} />
        ))}
      </div>
    </div>
  );
};

interface LoadingSpinnerProps {
  /** Size of the spinner */
  size?: 'sm' | 'md' | 'lg';
  /** Loading message to display */
  message?: string;
  /** Whether to show the message */
  showMessage?: boolean;
  /** Custom className */
  className?: string;
}

/**
 * Loading spinner component with optional message
 * Used for inline loading states and refresh operations
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  message = 'Loading...',
  showMessage = true,
  className,
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  return (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      <Loader2 className={`animate-spin ${sizeClasses[size]}`} />
      {showMessage && (
        <span className={`text-muted-foreground ${textSizeClasses[size]}`}>
          {message}
        </span>
      )}
    </div>
  );
};

interface RefreshIndicatorProps {
  /** Whether the refresh is in progress */
  isRefreshing: boolean;
  /** Custom message during refresh */
  message?: string;
  /** Custom className */
  className?: string;
}

/**
 * Refresh indicator component
 * Shows loading state during dashboard refresh operations
 */
export const RefreshIndicator: React.FC<RefreshIndicatorProps> = ({
  isRefreshing,
  message = 'Refreshing dashboard...',
  className,
}) => {
  if (!isRefreshing) return null;

  return (
    <div className={`fixed top-4 right-4 z-50 ${className}`}>
      <Card className="shadow-lg">
        <CardContent className="p-3">
          <LoadingSpinner size="sm" message={message} />
        </CardContent>
      </Card>
    </div>
  );
};

interface EmptyStateProps {
  /** Title for the empty state */
  title?: string;
  /** Description for the empty state */
  description?: string;
  /** Icon to display */
  icon?: React.ReactNode;
  /** Action button */
  action?: React.ReactNode;
  /** Custom className */
  className?: string;
}

/**
 * Empty state component
 * Shows when no data is available or widgets are empty
 */
export const EmptyState: React.FC<EmptyStateProps> = ({
  title = 'No data available',
  description = 'There are no widgets to display for this dashboard.',
  icon,
  action,
  className,
}) => {
  return (
    <Card className={className}>
      <CardContent className="p-8 text-center">
        {icon && (
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
            {icon}
          </div>
        )}
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          {title}
        </h3>
        <p className="text-muted-foreground mb-4">
          {description}
        </p>
        {action}
      </CardContent>
    </Card>
  );
};

interface LoadingStateProps {
  /** Type of loading operation */
  type?: 'dashboard' | 'widgets' | 'data' | 'connection';
  /** Loading message */
  message?: string;
  /** Progress percentage (0-100) */
  progress?: number;
  /** Whether to show progress bar */
  showProgress?: boolean;
  /** Custom className */
  className?: string;
}

/**
 * Enhanced loading state component with progress indication
 * Shows different loading states based on operation type
 */
export const LoadingState: React.FC<LoadingStateProps> = ({
  type = 'dashboard',
  message,
  progress,
  showProgress = false,
  className,
}) => {
  const getLoadingConfig = () => {
    switch (type) {
      case 'dashboard':
        return {
          icon: <Database className="h-6 w-6" />,
          defaultMessage: 'Loading dashboard metadata...',
          color: 'text-blue-600',
        };
      case 'widgets':
        return {
          icon: <Loader2 className="h-6 w-6 animate-spin" />,
          defaultMessage: 'Loading widgets...',
          color: 'text-green-600',
        };
      case 'data':
        return {
          icon: <RefreshCw className="h-6 w-6 animate-spin" />,
          defaultMessage: 'Fetching data...',
          color: 'text-purple-600',
        };
      case 'connection':
        return {
          icon: <Wifi className="h-6 w-6" />,
          defaultMessage: 'Connecting to Sisense...',
          color: 'text-orange-600',
        };
      default:
        return {
          icon: <Loader2 className="h-6 w-6 animate-spin" />,
          defaultMessage: 'Loading...',
          color: 'text-gray-600',
        };
    }
  };

  const config = getLoadingConfig();

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex flex-col items-center text-center space-y-4">
          <div className={`${config.color}`}>
            {config.icon}
          </div>
          <div className="space-y-2">
            <p className="text-sm font-medium">
              {message || config.defaultMessage}
            </p>
            {showProgress && typeof progress === 'number' && (
              <div className="w-full max-w-xs">
                <div className="flex justify-between text-xs text-muted-foreground mb-1">
                  <span>Progress</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface ErrorStateProps {
  /** Type of error */
  type?: 'network' | 'auth' | 'data' | 'permission' | 'unknown';
  /** Error title */
  title?: string;
  /** Error message */
  message?: string;
  /** Retry callback */
  onRetry?: () => void;
  /** Reset callback */
  onReset?: () => void;
  /** Whether retry is in progress */
  isRetrying?: boolean;
  /** Show technical details */
  showDetails?: boolean;
  /** Technical error details */
  details?: string;
  /** Custom className */
  className?: string;
}

/**
 * Enhanced error state component with contextual messaging
 * Shows different error states based on error type
 */
export const ErrorState: React.FC<ErrorStateProps> = ({
  type = 'unknown',
  title,
  message,
  onRetry,
  onReset,
  isRetrying = false,
  showDetails = false,
  details,
  className,
}) => {
  const getErrorConfig = () => {
    switch (type) {
      case 'network':
        return {
          icon: <WifiOff className="h-8 w-8 text-red-500" />,
          defaultTitle: 'Connection Error',
          defaultMessage: 'Unable to connect to Sisense server. Please check your internet connection.',
          variant: 'destructive' as const,
        };
      case 'auth':
        return {
          icon: <AlertTriangle className="h-8 w-8 text-yellow-500" />,
          defaultTitle: 'Authentication Error',
          defaultMessage: 'Your session has expired or credentials are invalid.',
          variant: 'destructive' as const,
        };
      case 'data':
        return {
          icon: <Database className="h-8 w-8 text-blue-500" />,
          defaultTitle: 'Data Error',
          defaultMessage: 'Failed to load dashboard data. The data source may be unavailable.',
          variant: 'destructive' as const,
        };
      case 'permission':
        return {
          icon: <AlertTriangle className="h-8 w-8 text-orange-500" />,
          defaultTitle: 'Permission Error',
          defaultMessage: 'You do not have permission to access this dashboard.',
          variant: 'destructive' as const,
        };
      default:
        return {
          icon: <AlertTriangle className="h-8 w-8 text-red-500" />,
          defaultTitle: 'Error',
          defaultMessage: 'An unexpected error occurred.',
          variant: 'destructive' as const,
        };
    }
  };

  const config = getErrorConfig();

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex flex-col items-center text-center space-y-4">
          {config.icon}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">
              {title || config.defaultTitle}
            </h3>
            <p className="text-muted-foreground">
              {message || config.defaultMessage}
            </p>
          </div>

          {showDetails && details && (
            <Alert variant={config.variant} className="text-left max-w-md">
              <AlertDescription>
                <details className="text-xs">
                  <summary className="cursor-pointer font-medium mb-2">
                    Technical Details
                  </summary>
                  <pre className="whitespace-pre-wrap break-words">
                    {details}
                  </pre>
                </details>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col sm:flex-row gap-2">
            {onRetry && (
              <Button 
                onClick={onRetry} 
                disabled={isRetrying}
                className="min-w-[120px]"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
                {isRetrying ? 'Retrying...' : 'Try Again'}
              </Button>
            )}
            {onReset && (
              <Button variant="outline" onClick={onReset}>
                Reset
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface ConnectionStatusProps {
  /** Connection status */
  status: 'connected' | 'connecting' | 'disconnected' | 'error';
  /** Server URL */
  serverUrl?: string;
  /** Last connection attempt timestamp */
  lastAttempt?: number;
  /** Custom className */
  className?: string;
}

/**
 * Connection status indicator component
 * Shows current connection state to Sisense server
 */
export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  status,
  serverUrl,
  lastAttempt,
  className,
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          icon: <Wifi className="h-4 w-4 text-green-500" />,
          message: 'Connected to Sisense',
          bgColor: 'bg-green-50 dark:bg-green-950/20',
          borderColor: 'border-green-200 dark:border-green-800',
        };
      case 'connecting':
        return {
          icon: <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />,
          message: 'Connecting to Sisense...',
          bgColor: 'bg-blue-50 dark:bg-blue-950/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
        };
      case 'disconnected':
        return {
          icon: <WifiOff className="h-4 w-4 text-gray-500" />,
          message: 'Disconnected',
          bgColor: 'bg-gray-50 dark:bg-gray-950/20',
          borderColor: 'border-gray-200 dark:border-gray-800',
        };
      case 'error':
        return {
          icon: <AlertTriangle className="h-4 w-4 text-red-500" />,
          message: 'Connection failed',
          bgColor: 'bg-red-50 dark:bg-red-950/20',
          borderColor: 'border-red-200 dark:border-red-800',
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg border ${config.bgColor} ${config.borderColor} ${className}`}>
      {config.icon}
      <div className="text-sm">
        <span className="font-medium">{config.message}</span>
        {serverUrl && (
          <div className="text-xs text-muted-foreground truncate max-w-[200px]">
            {serverUrl}
          </div>
        )}
        {lastAttempt && status === 'error' && (
          <div className="text-xs text-muted-foreground">
            Last attempt: {new Date(lastAttempt).toLocaleTimeString()}
          </div>
        )}
      </div>
    </div>
  );
};

// Export all components
export default {
  DashboardSkeleton,
  WidgetSkeleton,
  TabsSkeleton,
  LoadingSpinner,
  RefreshIndicator,
  EmptyState,
  LoadingState,
  ErrorState,
  ConnectionStatus,
};