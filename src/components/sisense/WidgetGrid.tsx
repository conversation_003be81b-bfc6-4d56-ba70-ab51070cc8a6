import React, { useMemo, useCallback } from 'react';
import { ComposeWidgetById } from './ComposeWidgetById';
import { Card, CardContent } from '@/components/ui/card';
import type { ApiWidget } from '@/types/sisense';
import { cn, filterWidgets, isValidWidget } from '@/lib/utils';
import { 
  RESPONSIVE_GRID_CONFIGS,
  TOUCH_FRIENDLY_CLASSES,
  RESPONSIVE_CONTAINER_CLASSES,
  generateResponsiveGridClasses,
  generateResponsiveWidgetClasses,
  useResponsive,
  type ResponsiveGridConfig
} from '@/lib/responsive';

export interface WidgetGridProps {
  widgets: ApiWidget[];
  dashboardOid: string;
  className?: string;
  columns?: number;
  showWidgetTitles?: boolean;
  showWidgetIds?: boolean;
  widgetHeight?: number | 'auto';
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  enableScrolling?: boolean;
  maxHeight?: string;
  autoColumns?: boolean;
  /** Responsive layout configuration */
  responsiveConfig?: keyof typeof RESPONSIVE_GRID_CONFIGS | ResponsiveGridConfig;
  /** Widget size configuration for responsive design */
  widgetSize?: 'small' | 'medium' | 'large' | 'auto';
  /** Enable touch-friendly interactions */
  touchFriendly?: boolean;
  /** Enable adaptive layout based on widget count */
  adaptiveLayout?: boolean;
}

interface GridConfig {
  columns: string;
  gap: string;
  containerClasses: string;
  responsiveClasses: string;
  touchClasses: string;
}

const getGridConfig = (
  columns?: number, 
  gap: 'sm' | 'md' | 'lg' | 'xl' = 'md',
  widgetCount: number = 0,
  autoColumns: boolean = true,
  responsiveConfig?: keyof typeof RESPONSIVE_GRID_CONFIGS | ResponsiveGridConfig,
  touchFriendly: boolean = true,
  adaptiveLayout: boolean = true
): GridConfig => {
  // Use responsive configuration if provided
  if (responsiveConfig) {
    const config = typeof responsiveConfig === 'string' 
      ? RESPONSIVE_GRID_CONFIGS[responsiveConfig]
      : responsiveConfig;
    
    if (config) {
      const responsiveClasses = generateResponsiveGridClasses(config, widgetCount);
      return {
        columns: '',
        gap: '',
        containerClasses: RESPONSIVE_CONTAINER_CLASSES.widgetGrid,
        responsiveClasses,
        touchClasses: touchFriendly ? TOUCH_FRIENDLY_CLASSES.base : ''
      };
    }
  }

  // Enhanced responsive grid configuration optimized for Card components
  let columnClasses: string;
  
  if (columns) {
    // Custom column configuration with responsive fallbacks
    const maxCols = Math.min(columns, 6);
    columnClasses = [
      'grid-cols-1',
      maxCols >= 2 ? 'sm:grid-cols-2' : '',
      maxCols >= 3 ? 'md:grid-cols-3' : '',
      maxCols >= 4 ? 'lg:grid-cols-4' : '',
      maxCols >= 5 ? 'xl:grid-cols-5' : '',
      maxCols >= 6 ? '2xl:grid-cols-6' : ''
    ].filter(Boolean).join(' ');
  } else if (autoColumns && widgetCount > 0 && adaptiveLayout) {
    // Adaptive layout based on widget count and device type
    if (widgetCount === 1) {
      columnClasses = 'grid-cols-1';
    } else if (widgetCount === 2) {
      columnClasses = 'grid-cols-1 md:grid-cols-2';
    } else if (widgetCount === 3) {
      columnClasses = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
    } else if (widgetCount === 4) {
      columnClasses = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4';
    } else if (widgetCount <= 6) {
      columnClasses = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
    } else {
      // For many widgets, use a balanced responsive layout
      columnClasses = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
    }
  } else {
    // Default responsive configuration optimized for Card components
    columnClasses = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
  }
  
  // Enhanced gap configuration with responsive spacing for Card components
  const gapClasses = {
    sm: 'gap-2 sm:gap-3', // Smaller gaps on mobile, larger on desktop
    md: 'gap-3 sm:gap-4', // Standard responsive spacing
    lg: 'gap-4 sm:gap-6', // More generous responsive spacing
    xl: 'gap-6 sm:gap-8'  // Maximum responsive spacing
  };

  // Container classes optimized for responsive Card grid layout
  const containerClasses = RESPONSIVE_CONTAINER_CLASSES.widgetGrid;
  const touchClasses = touchFriendly ? TOUCH_FRIENDLY_CLASSES.base : '';

  return {
    columns: columnClasses,
    gap: gapClasses[gap],
    containerClasses,
    responsiveClasses: '',
    touchClasses
  };
};

export const WidgetGrid: React.FC<WidgetGridProps> = React.memo(({
  widgets,
  dashboardOid,
  className,
  columns,
  showWidgetTitles = true,
  showWidgetIds = false,
  widgetHeight = 400,
  gap = 'md',
  enableScrolling = false,
  maxHeight,
  autoColumns = true,
  responsiveConfig = 'standard',
  widgetSize = 'medium',
  touchFriendly = true,
  adaptiveLayout = true
}) => {
  // Get enhanced responsive information
  const { 
    deviceType, 
    isMobile, 
    isMobileSmall,
    isTablet, 
    isTouch,
    orientation,
    getRecommendedGridConfig
  } = useResponsive();
  // Memoize filtered widgets to prevent unnecessary re-filtering
  const filteredWidgets = useMemo(() => {
    return filterWidgets(widgets, {
      excludeBlox: true,
      excludeTabbers: true, // Don't render tabber widgets in the grid
      customFilter: isValidWidget
    });
  }, [widgets]);

  // Memoize grid configuration with enhanced responsive logic
  const gridConfig = useMemo(() => {
    // Use recommended configuration if auto-columns is enabled
    const effectiveResponsiveConfig = autoColumns && adaptiveLayout 
      ? getRecommendedGridConfig(filteredWidgets.length)
      : responsiveConfig;
    
    return getGridConfig(
      columns, 
      gap, 
      filteredWidgets.length, 
      autoColumns,
      effectiveResponsiveConfig,
      touchFriendly,
      adaptiveLayout
    );
  }, [columns, gap, filteredWidgets.length, autoColumns, responsiveConfig, touchFriendly, adaptiveLayout, deviceType, getRecommendedGridConfig]);

  // Memoize responsive widget classes
  const responsiveWidgetClasses = useMemo(() => {
    return generateResponsiveWidgetClasses(widgetSize);
  }, [widgetSize]);

  // Determine effective max height with enhanced device detection
  const effectiveMaxHeight = useMemo(() => {
    if (maxHeight) return maxHeight;
    
    // Use responsive max heights based on device type
    const config = typeof responsiveConfig === 'string' 
      ? RESPONSIVE_GRID_CONFIGS[responsiveConfig]
      : responsiveConfig;
    
    if (config?.maxHeight) {
      if (isMobileSmall) return config.maxHeight.mobileSmall;
      if (isMobile) return config.maxHeight.mobile;
      if (isTablet) return config.maxHeight.tablet;
      return config.maxHeight.desktop;
    }
    
    // Enhanced fallback based on device type and orientation
    if (isMobileSmall) return orientation === 'landscape' ? '60vh' : '65vh';
    if (isMobile) return orientation === 'landscape' ? '65vh' : '70vh';
    if (isTablet) return orientation === 'landscape' ? '70vh' : '75vh';
    return '80vh';
  }, [maxHeight, responsiveConfig, isMobileSmall, isMobile, isTablet, orientation]);

  // Memoize error handler to prevent unnecessary re-renders
  const handleWidgetError = useCallback((widgetOid: string, error: Error) => {
    console.error(`Widget ${widgetOid} failed to load:`, error);
  }, []);

  // Memoize load handler to prevent unnecessary re-renders
  const handleWidgetLoad = useCallback((widgetOid: string) => {
    console.debug(`Widget ${widgetOid} loaded successfully`);
  }, []);

  if (!filteredWidgets || filteredWidgets.length === 0) {
    return (
      <div className={cn(
        "flex items-center justify-center p-8",
        enableScrolling && maxHeight && `max-h-[${maxHeight}]`,
        className
      )}>
        <Card className="max-w-md">
          <CardContent className="p-8 text-center">
            <div className="text-muted-foreground">
              <div className="text-lg font-medium mb-2">No widgets available</div>
              <div className="text-sm">
                {widgets.length > 0 
                  ? `${widgets.length} widgets were filtered out (excluded or invalid)`
                  : "This dashboard doesn't have any widgets to display."
                }
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Container classes with enhanced responsive and touch support
  const containerClasses = cn(
    "grid",
    // Use responsive classes if available, otherwise fall back to legacy grid config
    gridConfig.responsiveClasses || [
      gridConfig.containerClasses,
      gridConfig.columns,
      gridConfig.gap
    ].join(' '),
    // Touch-friendly classes
    gridConfig.touchClasses,
    // Height and scrolling configuration
    widgetHeight === 'auto' ? 'auto-rows-max' : 'auto-rows-fr',
    // Proper alignment for Card components
    "items-start", // Align cards to top of grid cells
    // Scrolling configuration with responsive support
    enableScrolling && [
      "overflow-auto",
      RESPONSIVE_CONTAINER_CLASSES.scrollableContent,
      effectiveMaxHeight && `max-h-[${effectiveMaxHeight}]`
    ].filter(Boolean).join(' '),
    // Performance optimizations
    enableScrolling && "will-change-scroll",
    // Enhanced mobile support with device-specific optimizations
    (isMobileSmall || isMobile) && [
      "overscroll-y-contain", // Prevent bounce scrolling on iOS
      "scroll-smooth",
      "-webkit-overflow-scrolling-touch", // iOS momentum scrolling
      "scroll-mobile" // Custom mobile scroll utility
    ].join(' '),
    // Enhanced responsive padding with device-specific support
    isMobileSmall ? "p-1" : isMobile ? "p-2" : "p-1 sm:p-2 md:p-3",
    // Touch-specific optimizations
    isTouch && "touch-manipulation",
    className
  );

  return (
    <div className={containerClasses}>
      {filteredWidgets.map((widget) => {
        // Use widget.oid if available, fallback to widget.id
        const widgetOid = widget.oid || widget.id;
        
        if (!widgetOid) {
          console.warn('Widget missing OID/ID:', widget);
          return null;
        }

        return (
          <WidgetGridItem
            key={widgetOid}
            widget={widget}
            widgetOid={widgetOid}
            dashboardOid={dashboardOid}
            showWidgetTitles={showWidgetTitles}
            showWidgetIds={showWidgetIds}
            widgetHeight={widgetHeight}
            responsiveWidgetClasses={responsiveWidgetClasses}
            touchFriendly={touchFriendly}
            isMobile={isMobile}
            isMobileSmall={isMobileSmall}
            isTouch={isTouch}
            onError={handleWidgetError}
            onLoad={handleWidgetLoad}
          />
        );
      })}
    </div>
  );
});

// Memoized widget grid item component for performance optimization
interface WidgetGridItemProps {
  widget: ApiWidget;
  widgetOid: string;
  dashboardOid: string;
  showWidgetTitles: boolean;
  showWidgetIds: boolean;
  widgetHeight: number | 'auto';
  responsiveWidgetClasses: string;
  touchFriendly: boolean;
  isMobile: boolean;
  isMobileSmall: boolean;
  isTouch: boolean;
  onError: (widgetOid: string, error: Error) => void;
  onLoad: (widgetOid: string) => void;
}

const WidgetGridItem = React.memo<WidgetGridItemProps>(({
  widget,
  widgetOid,
  dashboardOid,
  showWidgetTitles,
  showWidgetIds,
  widgetHeight,
  responsiveWidgetClasses,
  touchFriendly,
  isMobile,
  isMobileSmall,
  isTouch,
  onError,
  onLoad
}) => {
  const handleError = useCallback((error: Error) => {
    onError(widgetOid, error);
  }, [widgetOid, onError]);

  const handleLoad = useCallback(() => {
    onLoad(widgetOid);
  }, [widgetOid, onLoad]);

  return (
    <Card
      className={cn(
        "min-h-0 flex flex-col", // Allows grid item to shrink and provides flex container
        responsiveWidgetClasses, // Apply responsive sizing
        widgetHeight === 'auto' ? 'h-auto' : 'h-full',
        // Enhanced interaction feedback with touch support
        touchFriendly && TOUCH_FRIENDLY_CLASSES.card,
        // Enhanced mobile-specific optimizations
        (isMobileSmall || isMobile) && [
          "border-border/30", // Lighter borders on mobile
          "shadow-sm", // Reduced shadow on mobile
          "active:shadow-none", // Remove shadow on touch
          "animate-mobile-scale" // Mobile-optimized scale animation
        ].join(' '),
        // Touch device optimizations
        isTouch && [
          "active:scale-[0.98]",
          "transition-transform duration-150 ease-out"
        ].join(' '),
        // Desktop hover effects (only for non-touch devices)
        !isTouch && [
          "hover:shadow-md",
          "hover:scale-[1.02]",
          "border-border/50",
          "hover:border-border"
        ].join(' '),
        // Accessibility improvements
        "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
      )}
    >
      <CardContent className={cn(
        "flex-1", // Take available space
        // Enhanced responsive padding with device-specific support
        isMobileSmall ? "p-1" : isMobile ? "p-2" : "p-2 sm:p-3 md:p-4",
        widgetHeight === 'auto' ? 'h-auto' : 'h-full',
        // Enhanced touch-friendly content area
        touchFriendly && "touch-manipulation",
        // Container query support for widget responsiveness
        "widget-compact widget-spacious"
      )}>
        <ComposeWidgetById
          widgetOid={widgetOid}
          dashboardOid={dashboardOid}
          title={widget.title || undefined}
          showTitle={showWidgetTitles}
          showWidgetId={showWidgetIds}
          height={widgetHeight === 'auto' ? undefined : widgetHeight}
          className="h-full"
          onError={handleError}
          onLoad={handleLoad}
        />
      </CardContent>
    </Card>
  );
});

WidgetGridItem.displayName = 'WidgetGridItem';

// Responsive grid variants for different use cases with Card component support
const WidgetGridVariants = {
  // Single column for mobile-first approach
  single: {
    columns: 1,
    gap: 'md' as const,
    autoColumns: false
  },
  
  // Two columns for tablets
  double: {
    columns: 2,
    gap: 'md' as const,
    autoColumns: false
  },
  
  // Three columns for desktop
  triple: {
    columns: 3,
    gap: 'md' as const,
    autoColumns: false
  },
  
  // Four columns for large screens
  quad: {
    columns: 4,
    gap: 'md' as const,
    autoColumns: false
  },
  
  // Six columns for extra large screens
  hex: {
    columns: 6,
    gap: 'sm' as const, // Smaller gap for more columns
    autoColumns: false
  },
  
  // Adaptive grid that adjusts based on widget count
  adaptive: (widgetCount: number) => {
    if (widgetCount === 1) return WidgetGridVariants.single;
    if (widgetCount === 2) return WidgetGridVariants.double;
    if (widgetCount <= 4) return WidgetGridVariants.triple;
    if (widgetCount <= 8) return WidgetGridVariants.quad;
    return WidgetGridVariants.hex;
  },
  
  // Auto-sizing grid (default behavior)
  auto: {
    autoColumns: true,
    gap: 'md' as const
  }
};

// Attach variants to the main component for easy access
(WidgetGrid as any).variants = WidgetGridVariants;

// Compact version for smaller widgets with enhanced scrolling and Card components
export const CompactWidgetGrid: React.FC<WidgetGridProps> = (props) => (
  <WidgetGrid
    {...props}
    widgetHeight={300}
    gap="sm"
    columns={props.columns || 4}
    autoColumns={props.autoColumns ?? true}
    enableScrolling={props.enableScrolling ?? true}
    maxHeight={props.maxHeight || "600px"}
    className={cn("compact-widget-grid", props.className)}
  />
);

// Large version for detailed widgets with better spacing and Card components
export const LargeWidgetGrid: React.FC<WidgetGridProps> = (props) => (
  <WidgetGrid
    {...props}
    widgetHeight={500}
    gap="lg"
    columns={props.columns || 2}
    autoColumns={props.autoColumns ?? false}
    enableScrolling={props.enableScrolling ?? true}
    maxHeight={props.maxHeight || "800px"}
    className={cn("large-widget-grid", props.className)}
  />
);

// Auto-height version for dynamic content with Card components
export const AutoHeightWidgetGrid: React.FC<WidgetGridProps> = (props) => (
  <WidgetGrid
    {...props}
    widgetHeight="auto"
    gap={props.gap || "md"}
    autoColumns={props.autoColumns ?? true}
    enableScrolling={props.enableScrolling ?? false}
    className={cn("auto-height-widget-grid", props.className)}
  />
);

// Scrollable version for constrained spaces with Card components
export const ScrollableWidgetGrid: React.FC<WidgetGridProps> = (props) => (
  <WidgetGrid
    {...props}
    enableScrolling={true}
    maxHeight={props.maxHeight || "70vh"}
    gap={props.gap || "md"}
    autoColumns={props.autoColumns ?? true}
    className={cn("scrollable-widget-grid", props.className)}
  />
);

export default WidgetGrid;