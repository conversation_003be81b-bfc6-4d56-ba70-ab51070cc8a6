import React, { useState } from 'react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { WidgetGrid } from './WidgetGrid';
import type { ApiWidget } from '@/types/sisense';
import { 
  filterWidgetsForTab, 
  getUntabbedWidgets
} from '@/lib/widgetFilters';
import { 
  parseAllTabberWidgets,
  validateTabDefinitions 
} from '@/lib/tabParser';
import { cn } from '@/lib/utils';
import { 
  RESPONSIVE_TAB_CLASSES,
  RESPONSIVE_CONTAINER_CLASSES,
  useResponsive
} from '@/lib/responsive';

export interface TabsContainerProps {
  widgets: ApiWidget[];
  dashboardOid: string;
  className?: string;
  showWidgetTitles?: boolean;
  showWidgetIds?: boolean;
  widgetHeight?: number;
  defaultTab?: string;
  onTabChange?: (tabId: string) => void;
}

export const TabsContainer: React.FC<TabsContainerProps> = ({
  widgets,
  dashboardOid,
  className,
  showWidgetTitles = true,
  showWidgetIds = false,
  widgetHeight = 400,
  defaultTab,
  onTabChange
}) => {
  // Get enhanced responsive information for adaptive layout
  const { isMobile, isMobileSmall, isTablet } = useResponsive();
  // Parse tab definitions from tabber widgets
  const allTabs = parseAllTabberWidgets(widgets);
  const { valid: validTabs, warnings } = validateTabDefinitions(allTabs, widgets);
  
  // Get widgets that aren't assigned to any tab
  const untabbedWidgets = getUntabbedWidgets(widgets, validTabs);
  
  // State for active tab
  const [activeTab, setActiveTab] = useState(() => {
    if (defaultTab) return defaultTab;
    if (validTabs.length > 0) return `tab-0`;
    return 'untabbed';
  });

  // Log warnings in development
  if (process.env.NODE_ENV === 'development' && warnings.length > 0) {
    console.warn('Tab validation warnings:', warnings);
  }

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    onTabChange?.(value);
  };

  // If no tabs and no untabbed widgets, show empty state
  if (validTabs.length === 0 && untabbedWidgets.length === 0) {
    return (
      <div className={cn("flex items-center justify-center p-8 text-center", className)}>
        <div className="text-muted-foreground">
          <div className="text-lg font-medium mb-2">No organized widgets found</div>
          <div className="text-sm">
            This dashboard doesn't have any tab organization or renderable widgets.
          </div>
        </div>
      </div>
    );
  }

  // If only untabbed widgets exist, render them directly
  if (validTabs.length === 0 && untabbedWidgets.length > 0) {
    return (
      <div className={className}>
        <WidgetGrid
          widgets={untabbedWidgets}
          dashboardOid={dashboardOid}
          showWidgetTitles={showWidgetTitles}
          showWidgetIds={showWidgetIds}
          widgetHeight={widgetHeight}
        />
      </div>
    );
  }

  return (
    <div className={cn(RESPONSIVE_CONTAINER_CLASSES.tabContainer, className)}>
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        {/* Enhanced responsive tab list */}
        {isMobile ? (
          // Mobile: Horizontal scrolling tab list
          <div className="relative mb-6">
            <TabsList className={cn(
              RESPONSIVE_TAB_CLASSES.tabsList,
              "justify-start w-auto min-w-full"
            )}>
              {validTabs.map((tab, index) => (
                <TabsTrigger 
                  key={`tab-${index}`} 
                  value={`tab-${index}`}
                  className={cn(
                    RESPONSIVE_TAB_CLASSES.tabTrigger,
                    "text-xs px-4 py-2 min-w-[100px]"
                  )}
                  title={tab.title}
                >
                  <span className="truncate font-medium">
                    {tab.title}
                  </span>
                </TabsTrigger>
              ))}
              {untabbedWidgets.length > 0 && (
                <TabsTrigger 
                  value="untabbed" 
                  className={cn(
                    RESPONSIVE_TAB_CLASSES.tabTrigger,
                    "text-xs px-4 py-2 min-w-[120px]"
                  )}
                  title={`Other Widgets (${untabbedWidgets.length})`}
                >
                  <span className="truncate font-medium">
                    Other ({untabbedWidgets.length})
                  </span>
                </TabsTrigger>
              )}
            </TabsList>
            {/* Scroll indicator */}
            <div className="absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-background to-transparent pointer-events-none" />
          </div>
        ) : (
          // Desktop/Tablet: Flexible tab list
          <TabsList className={cn(
            "flex w-full mb-6 p-1",
            // Responsive spacing
            isTablet ? "gap-1" : "gap-2"
          )}>
            {validTabs.map((tab, index) => (
              <TabsTrigger 
                key={`tab-${index}`} 
                value={`tab-${index}`}
                className={cn(
                  RESPONSIVE_TAB_CLASSES.tabTrigger,
                  "text-sm px-3 py-2 flex-1 min-w-0"
                )}
                title={tab.title}
              >
                <span className="truncate">
                  {tab.title}
                </span>
              </TabsTrigger>
            ))}
            {untabbedWidgets.length > 0 && (
              <TabsTrigger 
                value="untabbed" 
                className={cn(
                  RESPONSIVE_TAB_CLASSES.tabTrigger,
                  "text-sm px-3 py-2 flex-1 min-w-0"
                )}
                title={`Other Widgets (${untabbedWidgets.length})`}
              >
                <span className="truncate">
                  Other Widgets ({untabbedWidgets.length})
                </span>
              </TabsTrigger>
            )}
          </TabsList>
        )}

        {validTabs.map((tab, index) => {
          const tabWidgets = filterWidgetsForTab(widgets, tab);
          
          return (
            <TabsContent 
              key={`tab-${index}`} 
              value={`tab-${index}`}
              className={RESPONSIVE_TAB_CLASSES.tabContent}
            >
              {/* Enhanced tab header */}
              <div className={cn(
                "mb-4",
                // Responsive header spacing
                isMobile ? "mb-3" : "mb-4"
              )}>
                <h3 className={cn(
                  "font-semibold text-foreground mb-2",
                  // Responsive title sizing
                  isMobile ? "text-base" : "text-lg"
                )}>
                  {tab.title}
                </h3>
                {showWidgetIds && tab.sourceTabberId && (
                  <p className="text-xs text-muted-foreground">
                    Source Tabber: {tab.sourceTabberId}
                  </p>
                )}
              </div>
              
              {tabWidgets.length > 0 ? (
                <WidgetGrid
                  widgets={tabWidgets}
                  dashboardOid={dashboardOid}
                  showWidgetTitles={showWidgetTitles}
                  showWidgetIds={showWidgetIds}
                  widgetHeight={widgetHeight}
                  responsiveConfig={isMobileSmall ? 'mobile' : isMobile ? 'compact' : 'standard'}
                  widgetSize={isMobileSmall ? 'small' : isMobile ? 'small' : 'medium'}
                  touchFriendly={true}
                  adaptiveLayout={true}
                  enableScrolling={true}
                />
              ) : (
                <div className="flex items-center justify-center p-8 text-center">
                  <div className="text-muted-foreground">
                    <div className={cn(
                      isMobile ? "text-xs" : "text-sm"
                    )}>
                      No widgets available for this tab
                    </div>
                    {showWidgetIds && (
                      <div className="text-xs mt-2">
                        Display IDs: {tab.displayWidgetIds.join(', ') || 'None'}
                        <br />
                        Hide IDs: {tab.hideWidgetIds.join(', ') || 'None'}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </TabsContent>
          );
        })}

        {untabbedWidgets.length > 0 && (
          <TabsContent value="untabbed" className={RESPONSIVE_TAB_CLASSES.tabContent}>
            {validTabs.length > 0 && (
              <div className={cn(
                "mb-6",
                isMobile && "mb-4"
              )}>
                <Separator className="mb-4" />
                <div className="mb-4">
                  <h3 className={cn(
                    "font-semibold text-foreground mb-2",
                    isMobile ? "text-base" : "text-lg"
                  )}>
                    Other Widgets
                  </h3>
                  <p className={cn(
                    "text-muted-foreground",
                    isMobile ? "text-xs" : "text-sm"
                  )}>
                    Widgets not assigned to any specific tab
                  </p>
                </div>
              </div>
            )}
            
            {validTabs.length === 0 && (
              <div className="mb-4">
                <h3 className={cn(
                  "font-semibold text-foreground mb-2",
                  isMobile ? "text-base" : "text-lg"
                )}>
                  Other Widgets
                </h3>
                <p className={cn(
                  "text-muted-foreground",
                  isMobile ? "text-xs" : "text-sm"
                )}>
                  Widgets not assigned to any specific tab
                </p>
              </div>
            )}
            
            <WidgetGrid
              widgets={untabbedWidgets}
              dashboardOid={dashboardOid}
              showWidgetTitles={showWidgetTitles}
              showWidgetIds={showWidgetIds}
              widgetHeight={widgetHeight}
              responsiveConfig={isMobileSmall ? 'mobile' : isMobile ? 'compact' : 'standard'}
              widgetSize={isMobileSmall ? 'small' : isMobile ? 'small' : 'medium'}
              touchFriendly={true}
              adaptiveLayout={true}
              enableScrolling={true}
            />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default TabsContainer;