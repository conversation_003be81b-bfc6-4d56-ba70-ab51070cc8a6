/**
 * DashboardById Component
 * Fetches dashboard metadata via REST API and renders widgets
 */

import { useState, useEffect } from 'react';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';
import CleanWidget from './clean-widget';
import { filterWidgets, isTabber, parseTabScript } from '@/utils/sisense-constants';
import sisenseConfig from '@/config/sisense';
import type { Filter } from '@sisense/sdk-data';

interface DashboardByIdProps {
  dashboardId: string;
  filters?: Filter[];
  className?: string;
}

interface Widget {
  oid: string;
  title: string;
  type: string;
  subtype?: string;
}

interface TabGroup {
  name: string;
  widgets: Widget[];
}

interface DashboardData {
  title: string;
  widgets: Widget[];
  indicators: Widget[];
  tabs: TabGroup[];
}

export default function DashboardById({
  dashboardId,
  filters = [],
  className = ''
}: DashboardByIdProps) {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboard();
  }, [dashboardId]);

  const fetchDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `${sisenseConfig.url}/api/v1/dashboards/${dashboardId}`,
        {
          headers: {
            'Authorization': `Bearer ${sisenseConfig.token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch dashboard: ${response.statusText}`);
      }

      const data = await response.json();
      const processedData = processDashboardData(data);
      setDashboardData(processedData);
    } catch (err) {
      console.error('Error fetching dashboard:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const processDashboardData = (data: any): DashboardData => {
    const allWidgets = data.widgets || [];
    
    // Filter out excluded widgets
    const validWidgets = filterWidgets(allWidgets);
    
    // Separate indicators (small KPI widgets) from regular widgets
    const indicators = validWidgets.filter((widget: Widget) => 
      widget.subtype === 'indicator' || widget.type === 'indicator'
    );
    
    // Process tab groups from WidgetsTabber widgets
    const tabberWidgets = allWidgets.filter(isTabber);
    const tabs: TabGroup[] = [];
    
    tabberWidgets.forEach((tabber: any) => {
      if (tabber.script) {
        const parsedTabs = parseTabScript(tabber.script);
        tabs.push(...parsedTabs);
      }
    });
    
    // Regular widgets (excluding indicators and tabber widgets)
    const regularWidgets = validWidgets.filter((widget: Widget) => 
      widget.subtype !== 'indicator' && 
      widget.type !== 'indicator' &&
      !isTabber(widget)
    );

    return {
      title: data.title || 'Dashboard',
      widgets: regularWidgets,
      indicators,
      tabs
    };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Error loading dashboard: {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!dashboardData) {
    return (
      <Alert>
        <AlertDescription>
          No dashboard data available.
        </AlertDescription>
