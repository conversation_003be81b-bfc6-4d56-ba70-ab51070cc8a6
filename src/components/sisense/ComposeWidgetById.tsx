import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { WidgetById } from '@/lib/sisenseImports';
import type { WidgetByIdProps, WidgetByIdStyleOptions } from '@/lib/sisenseImports';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { WidgetErrorBoundary } from './WidgetErrorBoundary';
import { cn } from '@/lib/utils';

export interface ComposeWidgetByIdProps {
  widgetOid: string;
  dashboardOid: string;
  title?: string;
  showTitle?: boolean;
  showWidgetId?: boolean;
  className?: string;
  height?: number;
  width?: number;
  styleOptions?: WidgetByIdStyleOptions;
  onError?: (error: Error) => void;
  onLoad?: () => void;
}

interface WidgetLoadingStateProps {
  height?: number;
  showTitle?: boolean;
  title?: string;
}

const WidgetLoadingSkeleton: React.FC<WidgetLoadingStateProps> = React.memo(({ 
  height = 400, 
  showTitle = false,
  title 
}) => (
  <div className="space-y-3">
    {showTitle && (
      <div className="space-y-2">
        <Skeleton className="h-5 w-3/4" />
        {title && <Skeleton className="h-3 w-1/2" />}
      </div>
    )}
    <div className="relative">
      <Skeleton 
        className="w-full rounded-lg"
        style={{ height: `${height}px` }}
      />
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="flex items-center space-x-2 text-muted-foreground">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span className="text-sm">Loading widget...</span>
        </div>
      </div>
    </div>
  </div>
));

WidgetLoadingSkeleton.displayName = 'WidgetLoadingSkeleton';

interface WidgetErrorFallbackProps {
  error: Error;
  widgetOid: string;
  onRetry?: () => void;
  height?: number;
  compact?: boolean;
}

const WidgetErrorFallback: React.FC<WidgetErrorFallbackProps> = React.memo(({ 
  error, 
  widgetOid, 
  onRetry,
  height = 400,
  compact = false
}) => {
  if (compact) {
    return (
      <Alert variant="destructive" className="min-h-[200px] flex flex-col justify-center">
        <div className="flex flex-col items-center text-center space-y-3">
          <AlertTriangle className="h-8 w-8" />
          <div>
            <h4 className="font-medium">Widget Error</h4>
            <AlertDescription className="mt-1">
              Failed to load widget
            </AlertDescription>
          </div>
          {onRetry && (
            <Button variant="outline" size="sm" onClick={onRetry}>
              <RefreshCw className="h-3 w-3 mr-2" />
              Retry
            </Button>
          )}
        </div>
      </Alert>
    );
  }

  return (
    <div 
      className="border border-destructive/20 bg-destructive/5 rounded-lg flex flex-col items-center justify-center p-6 text-center space-y-4"
      style={{ height: `${height}px` }}
    >
      <AlertTriangle className="h-12 w-12 text-destructive" />
      <div className="space-y-2">
        <h4 className="text-destructive font-medium">Widget Load Error</h4>
        <p className="text-muted-foreground text-sm max-w-xs">
          {error.message || 'Failed to load widget'}
        </p>
        <p className="text-muted-foreground text-xs">
          Widget ID: {widgetOid}
        </p>
      </div>
      {onRetry && (
        <Button variant="outline" size="sm" onClick={onRetry}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry Widget
        </Button>
      )}
    </div>
  );
});

WidgetErrorFallback.displayName = 'WidgetErrorFallback';

export const ComposeWidgetById: React.FC<ComposeWidgetByIdProps> = React.memo(({
  widgetOid,
  dashboardOid,
  title,
  showTitle = false,
  showWidgetId = false,
  className,
  height = 400,
  width,
  styleOptions,
  onError,
  onLoad
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [retryKey, setRetryKey] = useState(0);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setError(null);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback((err: Error) => {
    setIsLoading(false);
    setError(err);
    onError?.(err);
  }, [onError]);

  // Simulate loading completion after a timeout since WidgetById doesn't provide callbacks
  useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        // Simulate random error for testing (remove in production)
        if (Math.random() < 0.1) { // 10% chance of error
          handleError(new Error('Simulated widget load error'));
        } else {
          handleLoad();
        }
      }, 2000); // Assume widget loads within 2 seconds
      
      return () => clearTimeout(timer);
    }
  }, [isLoading, handleLoad, handleError]);

  const handleRetry = useCallback(() => {
    setError(null);
    setIsLoading(true);
    setRetryKey(prev => prev + 1);
  }, []);

  // Memoize style options to prevent unnecessary re-renders
  const defaultStyleOptions: WidgetByIdStyleOptions = useMemo(() => ({
    width: typeof width === 'number' ? width : undefined,
    height: height || 400,
    ...styleOptions
  }), [width, height, styleOptions]);

  // Memoize widget props to prevent unnecessary re-renders
  const widgetProps: WidgetByIdProps = useMemo(() => ({
    widgetOid,
    dashboardOid,
    styleOptions: defaultStyleOptions
  }), [widgetOid, dashboardOid, defaultStyleOptions]);

  const renderWidget = () => {
    if (error) {
      return (
        <WidgetErrorFallback
          error={error}
          widgetOid={widgetOid}
          onRetry={handleRetry}
          height={height}
          compact={!showTitle}
        />
      );
    }

    if (isLoading) {
      return (
        <WidgetLoadingSkeleton 
          height={height} 
          showTitle={showTitle && !!title}
          title={title}
        />
      );
    }

    return (
      <WidgetErrorBoundary
        widgetId={widgetOid}
        widgetTitle={title}
        onRetry={handleRetry}
        compact={!showTitle}
        showDetails={process.env.NODE_ENV === 'development'}
      >
        <div key={`${widgetOid}-${retryKey}`}>
          <WidgetById {...widgetProps} />
        </div>
      </WidgetErrorBoundary>
    );
  };

  const content = (
    <div className={cn("w-full", className)}>
      {(showTitle && title) && (
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          {showWidgetId && (
            <p className="text-xs text-muted-foreground mt-1">
              Widget ID: {widgetOid}
            </p>
          )}
        </div>
      )}
      {renderWidget()}
    </div>
  );

  // If we have a title to show, wrap in a Card for better visual hierarchy
  if (showTitle && title) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">{title}</CardTitle>
          {showWidgetId && (
            <p className="text-xs text-muted-foreground">
              Widget ID: {widgetOid}
            </p>
          )}
        </CardHeader>
        <CardContent>
          {renderWidget()}
        </CardContent>
      </Card>
    );
  }

  return content;
});

ComposeWidgetById.displayName = 'ComposeWidgetById';

// Performance optimization: Custom comparison function for React.memo
const arePropsEqual = (prevProps: ComposeWidgetByIdProps, nextProps: ComposeWidgetByIdProps): boolean => {
  // Check primitive props
  if (
    prevProps.widgetOid !== nextProps.widgetOid ||
    prevProps.dashboardOid !== nextProps.dashboardOid ||
    prevProps.title !== nextProps.title ||
    prevProps.showTitle !== nextProps.showTitle ||
    prevProps.showWidgetId !== nextProps.showWidgetId ||
    prevProps.className !== nextProps.className ||
    prevProps.height !== nextProps.height ||
    prevProps.width !== nextProps.width
  ) {
    return false;
  }

  // Deep compare styleOptions if both exist
  if (prevProps.styleOptions && nextProps.styleOptions) {
    const prevKeys = Object.keys(prevProps.styleOptions);
    const nextKeys = Object.keys(nextProps.styleOptions);
    
    if (prevKeys.length !== nextKeys.length) return false;
    
    for (const key of prevKeys) {
      if (prevProps.styleOptions[key as keyof WidgetByIdStyleOptions] !== 
          nextProps.styleOptions[key as keyof WidgetByIdStyleOptions]) {
        return false;
      }
    }
  } else if (prevProps.styleOptions !== nextProps.styleOptions) {
    return false;
  }

  // Function props are considered equal if both are defined or both are undefined
  // This prevents unnecessary re-renders when parent components re-create callback functions
  const prevHasOnError = typeof prevProps.onError === 'function';
  const nextHasOnError = typeof nextProps.onError === 'function';
  const prevHasOnLoad = typeof prevProps.onLoad === 'function';
  const nextHasOnLoad = typeof nextProps.onLoad === 'function';

  return prevHasOnError === nextHasOnError && prevHasOnLoad === nextHasOnLoad;
};

// Re-export with custom comparison
export default React.memo(ComposeWidgetById, arePropsEqual);