import React, { useCallback, useMemo } from 'react';
import { RefreshCw } from 'lucide-react';
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useDashboardPreferences } from '@/hooks/useLocalStorageState';
import { IIJA_DASHBOARDS, DEFAULT_DASHBOARD_CONFIG } from '@/config/dashboards';
import type { DashboardConfig } from '@/types/sisense';
import { 
  RESPONSIVE_TAB_CLASSES,
  RESPONSIVE_CONTAINER_CLASSES,
  TOUCH_FRIENDLY_CLASSES,
  useResponsive
} from '@/lib/responsive';
import MobileNavigation from './MobileNavigation';
import { cn } from '@/lib/utils';

export interface DashboardTabsContainerProps {
  /** Array of dashboard configurations to display as tabs */
  dashboards?: DashboardConfig[];
  /** Currently active dashboard ID */
  activeDashboardId?: string;
  /** Callback when dashboard tab changes */
  onDashboardChange?: (dashboardId: string) => void;
  /** Callback when refresh button is clicked */
  onRefresh?: (dashboardId: string) => void;
  /** Whether refresh is currently in progress */
  isRefreshing?: boolean;
  /** Custom className for the container */
  className?: string;
  /** Whether to show refresh controls */
  showRefreshControls?: boolean;
  /** Whether to show dashboard descriptions */
  showDescriptions?: boolean;
  /** Children to render within each dashboard tab */
  children?: React.ReactNode | ((dashboardId: string) => React.ReactNode);
}

const DashboardTabsContainer: React.FC<DashboardTabsContainerProps> = ({
  dashboards = IIJA_DASHBOARDS,
  activeDashboardId,
  onDashboardChange,
  onRefresh,
  isRefreshing = false,
  className,
  showRefreshControls = true,
  showDescriptions = true,
  children,
}) => {
  // Get enhanced responsive information for adaptive layout
  const { isMobile, isMobileSmall, isTablet, isTouch } = useResponsive();
  // Local storage state management for dashboard preferences
  const [preferences, setPreferences] = useDashboardPreferences(
    activeDashboardId || DEFAULT_DASHBOARD_CONFIG.defaultDashboardId
  );

  // Determine the current active dashboard ID
  const currentDashboardId = useMemo(() => {
    return activeDashboardId || preferences.activeDashboardId;
  }, [activeDashboardId, preferences.activeDashboardId]);

  // Find the current dashboard config
  const currentDashboard = useMemo(() => {
    return dashboards.find(d => d.id === currentDashboardId) || dashboards[0];
  }, [dashboards, currentDashboardId]);

  // Handle tab change
  const handleTabChange = useCallback((dashboardId: string) => {
    // Update local storage preferences
    setPreferences(prev => ({
      ...prev,
      activeDashboardId: dashboardId,
    }));

    // Call external callback if provided
    onDashboardChange?.(dashboardId);
  }, [setPreferences, onDashboardChange]);

  // Handle refresh button click
  const handleRefresh = useCallback(() => {
    if (currentDashboard) {
      // Update last refresh time in preferences
      setPreferences(prev => ({
        ...prev,
        lastRefreshTime: Date.now(),
      }));

      // Call external refresh callback
      onRefresh?.(currentDashboard.id);
    }
  }, [currentDashboard, setPreferences, onRefresh]);

  // Generate tab content
  const renderTabContent = useCallback((dashboard: DashboardConfig) => {
    if (typeof children === 'function') {
      return children(dashboard.id);
    }
    return children;
  }, [children]);

  // Keyboard navigation handler
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
      const currentIndex = dashboards.findIndex(d => d.id === currentDashboardId);
      if (currentIndex === -1) return;

      let nextIndex: number;
      if (event.key === 'ArrowLeft') {
        nextIndex = currentIndex > 0 ? currentIndex - 1 : dashboards.length - 1;
      } else {
        nextIndex = currentIndex < dashboards.length - 1 ? currentIndex + 1 : 0;
      }

      const nextDashboard = dashboards[nextIndex];
      if (nextDashboard) {
        handleTabChange(nextDashboard.id);
      }
    }
  }, [dashboards, currentDashboardId, handleTabChange]);

  if (dashboards.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <p className="text-muted-foreground text-center">No dashboards available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn(RESPONSIVE_CONTAINER_CLASSES.dashboard, className)}>
      <Tabs
        value={currentDashboardId}
        onValueChange={handleTabChange}
        onKeyDown={handleKeyDown}
        className={RESPONSIVE_CONTAINER_CLASSES.tabContainer}
      >
        {/* Enhanced Dashboard Tab Navigation with Mobile Optimization */}
        <div className={cn(
          "flex flex-col gap-4 mb-4",
          // Enhanced responsive layout adjustments
          isMobile ? "space-y-3" : "sm:flex-row sm:items-center sm:justify-between sm:gap-6"
        )}>
          {/* Enhanced responsive tab navigation */}
          <div className="flex-1 min-w-0 overflow-hidden">
            {isMobile || isTouch ? (
              // Mobile/Touch: Use enhanced MobileNavigation component
              <MobileNavigation
                items={dashboards.map(dashboard => ({
                  id: dashboard.id,
                  title: dashboard.title.replace('_SDK', '').replace(/^\d+\.\s*/, ''),
                  isActive: dashboard.id === currentDashboardId
                }))}
                activeId={currentDashboardId}
                onItemSelect={handleTabChange}
                showScrollIndicators={true}
                enableSwipeGestures={true}
                className="mb-2"
              />
            ) : (
              // Desktop/Tablet: Enhanced grid layout
              <TabsList className={cn(
                "grid w-full gap-1 p-1",
                // Enhanced responsive grid columns based on dashboard count
                dashboards.length <= 3 && "grid-cols-3",
                dashboards.length === 4 && "grid-cols-2 sm:grid-cols-4",
                dashboards.length === 5 && "grid-cols-2 sm:grid-cols-3 lg:grid-cols-5",
                dashboards.length === 6 && "grid-cols-2 sm:grid-cols-3 lg:grid-cols-6",
                dashboards.length === 7 && "grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7",
                dashboards.length === 8 && "grid-cols-2 sm:grid-cols-4 lg:grid-cols-4 xl:grid-cols-8",
                dashboards.length >= 9 && "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-9"
              )}>
                {dashboards.map((dashboard) => (
                  <TabsTrigger
                    key={dashboard.id}
                    value={dashboard.id}
                    className={cn(
                      RESPONSIVE_TAB_CLASSES.tabTrigger,
                      // Enhanced responsive text sizing
                      "text-xs sm:text-sm px-2 py-1.5",
                      "min-w-0", // Allow shrinking
                      // Touch-friendly sizing for tablets
                      isTablet && "min-h-[44px] px-3 py-2"
                    )}
                    title={dashboard.title}
                  >
                    <span className="truncate">
                      {dashboard.title.replace('_SDK', '').replace(/^\d+\.\s*/, '')}
                    </span>
                  </TabsTrigger>
                ))}
              </TabsList>
            )}
          </div>

          {/* Enhanced Refresh Controls with Better Mobile Support */}
          {showRefreshControls && (
            <div className={cn(
              "flex shrink-0",
              isMobile ? "justify-center" : "justify-end sm:justify-start"
            )}>
              <Button
                variant="outline"
                size={isMobileSmall ? "sm" : isMobile ? "default" : "sm"}
                onClick={handleRefresh}
                disabled={isRefreshing}
                className={cn(
                  TOUCH_FRIENDLY_CLASSES.interactive,
                  "shrink-0",
                  // Enhanced mobile styling with device-specific optimizations
                  isMobileSmall && [
                    "px-4 py-2", // Smaller touch target for small screens
                    "text-xs font-medium",
                    "min-w-[100px]"
                  ].join(' '),
                  isMobile && !isMobileSmall && [
                    "px-6 py-3", // Larger touch target
                    "text-sm font-medium",
                    "min-w-[120px]" // Consistent width
                  ].join(' '),
                  // Touch-specific optimizations
                  isTouch && "min-touch-target"
                )}
                title={`Refresh ${currentDashboard?.title || 'dashboard'}`}
              >
                <RefreshCw className={cn(
                  isRefreshing ? 'animate-spin' : '',
                  isMobileSmall ? "h-4 w-4" : isMobile ? "h-5 w-5" : "h-4 w-4"
                )} />
                <span className={cn(
                  "ml-2",
                  isMobileSmall ? "hidden" : isMobile ? "inline" : "hidden sm:inline"
                )}>
                  Refresh
                </span>
              </Button>
            </div>
          )}
        </div>

        {/* Enhanced Dashboard Tab Content */}
        {dashboards.map((dashboard) => (
          <TabsContent
            key={dashboard.id}
            value={dashboard.id}
            className={cn(
              RESPONSIVE_TAB_CLASSES.tabContent,
              "mt-0"
            )}
          >
            <Card className={cn(
              // Enhanced mobile card styling
              isMobile && [
                "border-0", // Remove border on mobile
                "shadow-none", // Remove shadow on mobile
                "bg-transparent" // Transparent background on mobile
              ].join(' ')
            )}>
              {/* Enhanced Dashboard Header */}
              <CardHeader className={cn(
                "pb-4",
                // Responsive header padding
                isMobile ? "px-2 pt-2" : "px-6 pt-6"
              )}>
                <div className={cn(
                  "flex items-start justify-between",
                  // Stack on mobile for better readability
                  isMobile && "flex-col space-y-3"
                )}>
                  <div className="space-y-1 flex-1">
                    <CardTitle className={cn(
                      "font-semibold",
                      // Responsive title sizing
                      isMobile ? "text-lg" : "text-xl"
                    )}>
                      {dashboard.title.replace('_SDK', '')}
                    </CardTitle>
                    {showDescriptions && dashboard.description && (
                      <p className={cn(
                        "text-muted-foreground",
                        isMobile ? "text-xs" : "text-sm"
                      )}>
                        {dashboard.description}
                      </p>
                    )}
                  </div>
                  
                  {/* Enhanced Dashboard Metadata */}
                  <div className={cn(
                    "flex text-xs text-muted-foreground space-y-1",
                    // Responsive metadata layout
                    isMobile ? [
                      "flex-row space-x-4 space-y-0",
                      "justify-start w-full"
                    ].join(' ') : [
                      "flex-col items-end"
                    ].join(' ')
                  )}>
                    {dashboard.widgetCount && (
                      <span className={cn(
                        isMobile && "bg-muted px-2 py-1 rounded-md"
                      )}>
                        {dashboard.widgetCount} widgets
                      </span>
                    )}
                    {dashboard.mapCount && dashboard.mapCount > 0 && (
                      <span className={cn(
                        isMobile && "bg-muted px-2 py-1 rounded-md"
                      )}>
                        {dashboard.mapCount} maps
                      </span>
                    )}
                    {dashboard.hasWidgetsTabber && (
                      <span className={cn(
                        "text-blue-600 dark:text-blue-400",
                        isMobile && "bg-blue-50 dark:bg-blue-950 px-2 py-1 rounded-md"
                      )}>
                        Tabbed
                      </span>
                    )}
                  </div>
                </div>
              </CardHeader>

              {/* Enhanced Dashboard Content */}
              <CardContent className={cn(
                "pt-0",
                // Responsive content padding
                isMobile ? "px-2 pb-2" : "px-6 pb-6",
                // Scrollable content area
                RESPONSIVE_CONTAINER_CLASSES.scrollableContent
              )}>
                {renderTabContent(dashboard)}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default DashboardTabsContainer;