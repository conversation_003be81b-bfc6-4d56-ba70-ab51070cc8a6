/**
 * Tests for performance optimizations
 */

import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import React from 'react';
import { ComposeWidgetById } from '../ComposeWidgetById';
import { OptimizedWidgetGrid } from '../OptimizedWidgetGrid';
import type { ApiWidget } from '@/types/sisense';

// Mock the Sisense imports
vi.mock('@/lib/sisenseImports', () => ({
  WidgetById: ({ widgetOid }: { widgetOid: string }) => (
    <div data-testid={`widget-${widgetOid}`}>Mock Widget {widgetOid}</div>
  ),
}));

// Mock the performance optimizations
vi.mock('@/lib/performanceOptimizations', () => ({
  useOptimizedIntersectionObserver: () => ({
    ref: { current: null },
    isIntersecting: true,
    hasBeenVisible: true,
  }),
  useWidgetPerformance: () => ({
    startRender: vi.fn(),
    endRender: vi.fn(),
    optimizedProps: {},
  }),
  PERFORMANCE_CONFIG: {
    INTERSECTION_OBSERVER: {
      ROOT_MARGIN: '150px',
      THRESHOLD: 0.1,
      FREEZE_ON_VISIBLE: true,
    },
    LAZY_LOADING: {
      CHUNK_SIZE: 10,
      PRELOAD_DISTANCE: 2,
    },
  },
}));

describe('Performance Optimizations', () => {
  describe('ComposeWidgetById', () => {
    it('should render without unnecessary re-renders', () => {
      const onError = vi.fn();
      const onLoad = vi.fn();

      const { rerender } = render(
        <ComposeWidgetById
          widgetOid="test-widget-1"
          dashboardOid="test-dashboard"
          onError={onError}
          onLoad={onLoad}
        />
      );

      expect(screen.getByTestId('widget-test-widget-1')).toBeInTheDocument();

      // Re-render with same props should not cause re-render due to React.memo
      rerender(
        <ComposeWidgetById
          widgetOid="test-widget-1"
          dashboardOid="test-dashboard"
          onError={onError}
          onLoad={onLoad}
        />
      );

      expect(screen.getByTestId('widget-test-widget-1')).toBeInTheDocument();
    });

    it('should handle style options memoization', () => {
      const styleOptions = { width: 400, height: 300 };
      
      render(
        <ComposeWidgetById
          widgetOid="test-widget-2"
          dashboardOid="test-dashboard"
          styleOptions={styleOptions}
        />
      );

      expect(screen.getByTestId('widget-test-widget-2')).toBeInTheDocument();
    });
  });

  describe('OptimizedWidgetGrid', () => {
    const mockWidgets: ApiWidget[] = [
      {
        oid: 'widget-1',
        id: 'widget-1',
        title: 'Test Widget 1',
        type: 'chart',
      },
      {
        oid: 'widget-2',
        id: 'widget-2',
        title: 'Test Widget 2',
        type: 'table',
      },
    ];

    it('should render optimized widget grid', () => {
      render(
        <OptimizedWidgetGrid
          widgets={mockWidgets}
          dashboardOid="test-dashboard"
          enableLazyLoading={true}
        />
      );

      expect(screen.getByTestId('widget-widget-1')).toBeInTheDocument();
      expect(screen.getByTestId('widget-widget-2')).toBeInTheDocument();
    });

    it('should handle empty widget list', () => {
      render(
        <OptimizedWidgetGrid
          widgets={[]}
          dashboardOid="test-dashboard"
        />
      );

      expect(screen.getByText('No widgets available')).toBeInTheDocument();
    });

    it('should apply performance optimizations', () => {
      const { rerender } = render(
        <OptimizedWidgetGrid
          widgets={mockWidgets}
          dashboardOid="test-dashboard"
          enableLazyLoading={true}
          enableVirtualization={false}
        />
      );

      // Re-render with same props should be optimized
      rerender(
        <OptimizedWidgetGrid
          widgets={mockWidgets}
          dashboardOid="test-dashboard"
          enableLazyLoading={true}
          enableVirtualization={false}
        />
      );

      expect(screen.getByTestId('widget-widget-1')).toBeInTheDocument();
      expect(screen.getByTestId('widget-widget-2')).toBeInTheDocument();
    });
  });
});