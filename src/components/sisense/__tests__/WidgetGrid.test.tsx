import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { WidgetGrid, CompactWidgetGrid, LargeWidgetGrid, AutoHeightWidgetGrid, ScrollableWidgetGrid } from '../WidgetGrid';
import type { ApiWidget } from '@/types/sisense';

// Mock the ComposeWidgetById component
vi.mock('../ComposeWidgetById', () => ({
  ComposeWidgetById: vi.fn(),
}));

// Mock the shadcn/ui Card components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div className={className} data-testid="card">{children}</div>
  ),
  CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div className={className} data-testid="card-content">{children}</div>
  )
}));

// Mock the utility functions
vi.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
  filterWidgets: (widgets: ApiWidget[]) => widgets.filter(w => w.oid && !w.type?.includes('blox')),
  isValidWidget: (widget: ApiWidget) => Boolean(widget.oid)
}));

// Mock responsive utilities
vi.mock('@/lib/responsive', () => ({
  RESPONSIVE_GRID_CONFIGS: {
    standard: {
      mobile: { columns: 1, gap: 'sm' },
      tablet: { columns: 2, gap: 'md' },
      desktop: { columns: 3, gap: 'md' }
    }
  },
  TOUCH_FRIENDLY_CLASSES: {
    base: 'touch-manipulation',
    card: 'active:scale-95'
  },
  RESPONSIVE_CONTAINER_CLASSES: {
    widgetGrid: 'responsive-grid',
    scrollableContent: 'scrollable-content'
  },
  generateResponsiveGridClasses: vi.fn(() => 'responsive-grid-classes'),
  generateResponsiveWidgetClasses: vi.fn(() => 'responsive-widget-classes'),
  useResponsive: () => ({
    deviceType: 'desktop',
    isMobile: false,
    isMobileSmall: false,
    isTablet: false,
    isTouch: false,
    orientation: 'landscape',
    getRecommendedGridConfig: vi.fn(() => 'standard')
  })
}));

const mockWidgets: ApiWidget[] = [
  { id: 'widget-1', oid: 'widget-1', title: 'Widget 1', type: 'chart', subtype: 'column', isMap: false },
  { id: 'widget-2', oid: 'widget-2', title: 'Widget 2', type: 'table', subtype: 'pivot', isMap: false },
  { id: 'widget-3', oid: 'widget-3', title: 'Widget 3', type: 'chart', subtype: 'line', isMap: false },
  { id: 'widget-4', oid: 'widget-4', title: 'Widget 4', type: 'map', subtype: 'area', isMap: true }
];

describe('WidgetGrid', () => {
  const defaultProps = {
    widgets: mockWidgets,
    dashboardOid: 'dashboard-123'
  };

  let mockComposeWidgetById: any;

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'debug').mockImplementation(() => {});
    
    // Get the mocked component
    mockComposeWidgetById = vi.mocked(require('../ComposeWidgetById').ComposeWidgetById);
    
    // Setup default mock implementation
    mockComposeWidgetById.mockImplementation(({ widgetOid, title }: { widgetOid: string; title?: string }) => (
      <div data-testid={`widget-${widgetOid}`}>
        {title && <h3>{title}</h3>}
        Mock Widget {widgetOid}
      </div>
    ));
  });

  it('renders widgets in a grid layout with Card components', () => {
    render(<WidgetGrid {...defaultProps} />);
    
    expect(screen.getByTestId('widget-widget-1')).toBeInTheDocument();
    expect(screen.getByTestId('widget-widget-2')).toBeInTheDocument();
    expect(screen.getByTestId('widget-widget-3')).toBeInTheDocument();
    expect(screen.getByTestId('widget-widget-4')).toBeInTheDocument();
    
    // Should render Card components for each widget
    const cards = screen.getAllByTestId('card');
    expect(cards).toHaveLength(4);
    
    // Should render CardContent components for each widget
    const cardContents = screen.getAllByTestId('card-content');
    expect(cardContents).toHaveLength(4);
  });

  it('shows empty state when no widgets are available with Card component', () => {
    render(<WidgetGrid {...defaultProps} widgets={[]} />);
    
    expect(screen.getByText('No widgets available')).toBeInTheDocument();
    expect(screen.getByText("This dashboard doesn't have any widgets to display.")).toBeInTheDocument();
    
    // Should render empty state in a Card
    expect(screen.getByTestId('card')).toBeInTheDocument();
    expect(screen.getByTestId('card-content')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<WidgetGrid {...defaultProps} className="custom-class" />);
    
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('supports auto columns configuration', () => {
    const { container } = render(<WidgetGrid {...defaultProps} autoColumns={true} />);
    
    // Should have responsive grid classes
    expect(container.firstChild).toHaveClass('grid');
  });

  it('supports scrolling configuration', () => {
    const { container } = render(
      <WidgetGrid 
        {...defaultProps} 
        enableScrolling={true} 
        maxHeight="500px" 
      />
    );
    
    expect(container.firstChild).toHaveClass('overflow-auto');
  });
});

describe('WidgetGrid Variants', () => {
  const defaultProps = {
    widgets: mockWidgets,
    dashboardOid: 'dashboard-123'
  };

  it('renders CompactWidgetGrid with correct props', () => {
    const { container } = render(<CompactWidgetGrid {...defaultProps} />);
    
    expect(container.firstChild).toHaveClass('compact-widget-grid');
  });

  it('renders LargeWidgetGrid with correct props', () => {
    const { container } = render(<LargeWidgetGrid {...defaultProps} />);
    
    expect(container.firstChild).toHaveClass('large-widget-grid');
  });

  it('renders AutoHeightWidgetGrid with correct props', () => {
    const { container } = render(<AutoHeightWidgetGrid {...defaultProps} />);
    
    expect(container.firstChild).toHaveClass('auto-height-widget-grid');
  });

  it('renders ScrollableWidgetGrid with correct props', () => {
    const { container } = render(<ScrollableWidgetGrid {...defaultProps} />);
    
    expect(container.firstChild).toHaveClass('scrollable-widget-grid');
    expect(container.firstChild).toHaveClass('overflow-auto');
  });
});

describe('WidgetGrid Responsive Behavior', () => {
  const defaultProps = {
    widgets: mockWidgets,
    dashboardOid: 'dashboard-123'
  };

  it('applies responsive grid classes', () => {
    const { container } = render(
      <WidgetGrid 
        {...defaultProps} 
        responsiveConfig="standard"
      />
    );
    
    expect(container.firstChild).toHaveClass('responsive-grid-classes');
  });

  it('handles touch-friendly interactions', () => {
    const { container } = render(
      <WidgetGrid 
        {...defaultProps} 
        touchFriendly={true}
      />
    );
    
    expect(container.firstChild).toHaveClass('touch-manipulation');
  });

  it('adapts layout based on widget count', () => {
    const singleWidget = [mockWidgets[0]];
    const { container } = render(
      <WidgetGrid 
        widgets={singleWidget}
        dashboardOid="dashboard-123"
        adaptiveLayout={true}
      />
    );
    
    expect(container.firstChild).toHaveClass('grid');
  });

  it('applies custom max height for scrolling', () => {
    const { container } = render(
      <WidgetGrid 
        {...defaultProps}
        enableScrolling={true}
        maxHeight="500px"
      />
    );
    
    expect(container.firstChild).toHaveClass('overflow-auto');
  });

  it('handles widget error callbacks', () => {
    const onError = vi.fn();
    
    // Mock ComposeWidgetById to trigger error
    mockComposeWidgetById.mockImplementation(({ onError: widgetOnError }) => {
      if (widgetOnError) {
        widgetOnError(new Error('Test error'));
      }
      return <div>Error Widget</div>;
    });

    render(<WidgetGrid {...defaultProps} />);
    
    // Error should be logged to console
    expect(console.error).toHaveBeenCalled();
  });

  it('handles widget load callbacks', () => {
    // Mock ComposeWidgetById to trigger load
    mockComposeWidgetById.mockImplementation(({ onLoad: widgetOnLoad }) => {
      if (widgetOnLoad) {
        widgetOnLoad();
      }
      return <div>Loaded Widget</div>;
    });

    render(<WidgetGrid {...defaultProps} />);
    
    // Load should be logged to console
    expect(console.debug).toHaveBeenCalled();
  });

  it('filters out invalid widgets', () => {
    const invalidWidgets = [
      ...mockWidgets,
      { id: 'invalid-1', oid: '', title: 'Invalid Widget 1' }, // No OID
      { id: 'invalid-2', oid: 'blox-widget', title: 'BloX Widget', type: 'blox' }, // BloX type
    ];

    render(
      <WidgetGrid 
        widgets={invalidWidgets}
        dashboardOid="dashboard-123"
      />
    );
    
    // Should only render valid widgets
    expect(screen.getByTestId('widget-widget-1')).toBeInTheDocument();
    expect(screen.getByTestId('widget-widget-2')).toBeInTheDocument();
    expect(screen.getByTestId('widget-widget-3')).toBeInTheDocument();
    expect(screen.getByTestId('widget-widget-4')).toBeInTheDocument();
    
    // Invalid widgets should not be rendered
    expect(screen.queryByTestId('widget-invalid-1')).not.toBeInTheDocument();
    expect(screen.queryByTestId('widget-invalid-2')).not.toBeInTheDocument();
  });

  it('shows filtered widget count in empty state', () => {
    const invalidWidgets = [
      { id: 'invalid-1', oid: '', title: 'Invalid Widget 1' },
      { id: 'invalid-2', oid: 'blox-widget', title: 'BloX Widget', type: 'blox' },
    ];

    render(
      <WidgetGrid 
        widgets={invalidWidgets}
        dashboardOid="dashboard-123"
      />
    );
    
    expect(screen.getByText('No widgets available')).toBeInTheDocument();
    expect(screen.getByText('2 widgets were filtered out (excluded or invalid)')).toBeInTheDocument();
  });

  it('handles auto-height widgets correctly', () => {
    const { container } = render(
      <WidgetGrid 
        {...defaultProps}
        widgetHeight="auto"
      />
    );
    
    expect(container.firstChild).toHaveClass('auto-rows-max');
  });

  it('handles fixed-height widgets correctly', () => {
    const { container } = render(
      <WidgetGrid 
        {...defaultProps}
        widgetHeight={400}
      />
    );
    
    expect(container.firstChild).toHaveClass('auto-rows-fr');
  });

  it('applies correct gap classes', () => {
    const { container: smallGap } = render(
      <WidgetGrid {...defaultProps} gap="sm" />
    );
    const { container: largeGap } = render(
      <WidgetGrid {...defaultProps} gap="lg" />
    );
    
    expect(smallGap.firstChild).toHaveClass('grid');
    expect(largeGap.firstChild).toHaveClass('grid');
  });

  it('handles widget size configurations', () => {
    render(
      <WidgetGrid 
        {...defaultProps}
        widgetSize="large"
      />
    );
    
    // Should apply responsive widget classes
    const cards = screen.getAllByTestId('card');
    expect(cards[0]).toHaveClass('responsive-widget-classes');
  });
});