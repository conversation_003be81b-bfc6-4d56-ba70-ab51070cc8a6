import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import DashboardTabsContainer from '../DashboardTabsContainer';
import type { DashboardConfig } from '@/types/sisense';

// Mock the hooks
vi.mock('@/hooks/useLocalStorageState', () => ({
  useDashboardPreferences: vi.fn(() => [
    {
      activeDashboardId: 'test-dashboard-1',
      collapsedTabs: [],
      widgetSizes: {},
      lastRefreshTime: 0,
    },
    vi.fn(),
  ]),
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

const mockDashboards: DashboardConfig[] = [
  {
    id: 'test-dashboard-1',
    title: '1. Test Dashboard One_SDK',
    slug: 'test-dashboard-one',
    description: 'First test dashboard',
    widgetCount: 10,
    mapCount: 1,
    hasWidgetsTabber: false,
  },
  {
    id: 'test-dashboard-2',
    title: '2. Test Dashboard Two_SDK',
    slug: 'test-dashboard-two',
    description: 'Second test dashboard',
    widgetCount: 15,
    mapCount: 2,
    hasWidgetsTabber: true,
  },
];

describe('DashboardTabsContainer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  it('renders dashboard tabs correctly', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
      />
    );

    // Check if dashboard tabs are rendered
    expect(screen.getByText('Test Dashboard One')).toBeInTheDocument();
    expect(screen.getByText('Test Dashboard Two')).toBeInTheDocument();
  });

  it('displays dashboard descriptions when enabled', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        showDescriptions={true}
      />
    );

    expect(screen.getByText('First test dashboard')).toBeInTheDocument();
  });

  it('shows refresh button when refresh controls are enabled', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        showRefreshControls={true}
      />
    );

    const refreshButton = screen.getByTitle(/refresh/i);
    expect(refreshButton).toBeInTheDocument();
  });

  it('calls onDashboardChange when tab is clicked', () => {
    const onDashboardChange = vi.fn();
    
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        onDashboardChange={onDashboardChange}
      />
    );

    const secondTab = screen.getByText('Test Dashboard Two');
    fireEvent.click(secondTab);

    expect(onDashboardChange).toHaveBeenCalledWith('test-dashboard-2');
  });

  it('calls onRefresh when refresh button is clicked', () => {
    const onRefresh = vi.fn();
    
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        onRefresh={onRefresh}
        showRefreshControls={true}
      />
    );

    const refreshButton = screen.getByTitle(/refresh/i);
    fireEvent.click(refreshButton);

    expect(onRefresh).toHaveBeenCalledWith('test-dashboard-1');
  });

  it('displays widget and map counts', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
      />
    );

    expect(screen.getByText('10 widgets')).toBeInTheDocument();
    expect(screen.getByText('1 maps')).toBeInTheDocument();
  });

  it('shows tabbed indicator for dashboards with tabber widgets', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-2"
      />
    );

    expect(screen.getByText('Tabbed')).toBeInTheDocument();
  });

  it('handles empty dashboard list gracefully', () => {
    render(<DashboardTabsContainer dashboards={[]} />);

    expect(screen.getByText('No dashboards available')).toBeInTheDocument();
  });

  it('renders children content correctly', () => {
    const testContent = <div>Test dashboard content</div>;
    
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
      >
        {testContent}
      </DashboardTabsContainer>
    );

    expect(screen.getByText('Test dashboard content')).toBeInTheDocument();
  });

  it('renders function children with dashboard ID', () => {
    const childrenFunction = vi.fn((dashboardId: string) => (
      <div>Dashboard ID: {dashboardId}</div>
    ));
    
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
      >
        {childrenFunction}
      </DashboardTabsContainer>
    );

    expect(childrenFunction).toHaveBeenCalledWith('test-dashboard-1');
    expect(screen.getByText('Dashboard ID: test-dashboard-1')).toBeInTheDocument();
  });

  it('disables refresh button when refreshing', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        isRefreshing={true}
        showRefreshControls={true}
      />
    );

    const refreshButton = screen.getByTitle(/refresh/i);
    expect(refreshButton).toBeDisabled();
  });

  it('shows spinning icon when refreshing', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        isRefreshing={true}
        showRefreshControls={true}
      />
    );

    const refreshIcon = screen.getByTitle(/refresh/i).querySelector('svg');
    expect(refreshIcon).toHaveClass('animate-spin');
  });
});

describe('DashboardTabsContainer Navigation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  it('highlights active dashboard tab correctly', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-2"
      />
    );

    // The active tab should have appropriate styling/attributes
    const activeTab = screen.getByText('Test Dashboard Two');
    expect(activeTab.closest('[data-state="active"]')).toBeInTheDocument();
  });

  it('switches between dashboard tabs correctly', () => {
    const onDashboardChange = vi.fn();
    
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        onDashboardChange={onDashboardChange}
      />
    );

    // Click on second tab
    const secondTab = screen.getByText('Test Dashboard Two');
    fireEvent.click(secondTab);

    expect(onDashboardChange).toHaveBeenCalledWith('test-dashboard-2');
    expect(onDashboardChange).toHaveBeenCalledTimes(1);

    // Click on first tab
    const firstTab = screen.getByText('Test Dashboard One');
    fireEvent.click(firstTab);

    expect(onDashboardChange).toHaveBeenCalledWith('test-dashboard-1');
    expect(onDashboardChange).toHaveBeenCalledTimes(2);
  });

  it('handles keyboard navigation between tabs', () => {
    const onDashboardChange = vi.fn();
    
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        onDashboardChange={onDashboardChange}
      />
    );

    const firstTab = screen.getByText('Test Dashboard One');
    
    // Focus on first tab
    firstTab.focus();
    
    // Press arrow right to move to next tab
    fireEvent.keyDown(firstTab, { key: 'ArrowRight' });
    
    // Should focus on second tab (behavior depends on implementation)
    const secondTab = screen.getByText('Test Dashboard Two');
    expect(document.activeElement).toBe(secondTab);
  });

  it('persists tab selection in local storage', () => {
    const mockSetPreferences = vi.fn();
    
    // Mock the hook to return our mock setter
    vi.mocked(require('@/hooks/useLocalStorageState').useDashboardPreferences)
      .mockReturnValue([
        {
          activeDashboardId: 'test-dashboard-1',
          collapsedTabs: [],
          widgetSizes: {},
          lastRefreshTime: 0,
        },
        mockSetPreferences,
      ]);

    const onDashboardChange = vi.fn();
    
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        onDashboardChange={onDashboardChange}
      />
    );

    // Click on second tab
    const secondTab = screen.getByText('Test Dashboard Two');
    fireEvent.click(secondTab);

    expect(onDashboardChange).toHaveBeenCalledWith('test-dashboard-2');
  });

  it('handles tab switching with loading states', () => {
    const onDashboardChange = vi.fn();
    
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        onDashboardChange={onDashboardChange}
        isLoading={true}
      />
    );

    // Tabs should still be clickable during loading
    const secondTab = screen.getByText('Test Dashboard Two');
    fireEvent.click(secondTab);

    expect(onDashboardChange).toHaveBeenCalledWith('test-dashboard-2');
  });

  it('shows loading indicators during tab transitions', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        isLoading={true}
      />
    );

    // Should show loading state
    expect(screen.getByText(/loading/i) || screen.getByTestId('loading-indicator')).toBeInTheDocument();
  });

  it('handles error states during navigation', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        error="Failed to load dashboard"
      />
    );

    // Should show error message
    expect(screen.getByText('Failed to load dashboard')).toBeInTheDocument();
  });

  it('maintains scroll position during tab switches', () => {
    const onDashboardChange = vi.fn();
    
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        onDashboardChange={onDashboardChange}
      />
    );

    // Mock scroll position
    Object.defineProperty(window, 'scrollY', { value: 100, writable: true });

    // Switch tabs
    const secondTab = screen.getByText('Test Dashboard Two');
    fireEvent.click(secondTab);

    // Scroll position should be maintained or handled appropriately
    expect(onDashboardChange).toHaveBeenCalledWith('test-dashboard-2');
  });

  it('handles rapid tab switching correctly', () => {
    const onDashboardChange = vi.fn();
    
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
        onDashboardChange={onDashboardChange}
      />
    );

    const firstTab = screen.getByText('Test Dashboard One');
    const secondTab = screen.getByText('Test Dashboard Two');

    // Rapid clicking
    fireEvent.click(secondTab);
    fireEvent.click(firstTab);
    fireEvent.click(secondTab);

    expect(onDashboardChange).toHaveBeenCalledTimes(3);
    expect(onDashboardChange).toHaveBeenNthCalledWith(1, 'test-dashboard-2');
    expect(onDashboardChange).toHaveBeenNthCalledWith(2, 'test-dashboard-1');
    expect(onDashboardChange).toHaveBeenNthCalledWith(3, 'test-dashboard-2');
  });

  it('handles dashboard with no widgets gracefully', () => {
    const emptyDashboard: DashboardConfig = {
      id: 'empty-dashboard',
      title: 'Empty Dashboard',
      slug: 'empty-dashboard',
      description: 'Dashboard with no widgets',
      widgetCount: 0,
      mapCount: 0,
      hasWidgetsTabber: false,
    };

    render(
      <DashboardTabsContainer
        dashboards={[...mockDashboards, emptyDashboard]}
        activeDashboardId="empty-dashboard"
      />
    );

    expect(screen.getByText('Empty Dashboard')).toBeInTheDocument();
    expect(screen.getByText('0 widgets')).toBeInTheDocument();
  });

  it('handles accessibility attributes correctly', () => {
    render(
      <DashboardTabsContainer
        dashboards={mockDashboards}
        activeDashboardId="test-dashboard-1"
      />
    );

    const firstTab = screen.getByText('Test Dashboard One');
    const secondTab = screen.getByText('Test Dashboard Two');

    // Check ARIA attributes
    expect(firstTab).toHaveAttribute('role', 'tab');
    expect(secondTab).toHaveAttribute('role', 'tab');
    
    // Active tab should have aria-selected="true"
    expect(firstTab).toHaveAttribute('aria-selected', 'true');
    expect(secondTab).toHaveAttribute('aria-selected', 'false');
  });
});