import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { GlobalErrorBoundary } from '../GlobalErrorBoundary';
import { DashboardErrorBoundary } from '../DashboardErrorBoundary';
import { WidgetErrorBoundary } from '../WidgetErrorBoundary';
import { SisenseErrorBoundary } from '../SisenseErrorBoundary';

// Mock the error logging service
vi.mock('@/services/errorLoggingService', () => ({
  errorLoggingService: {
    logReactError: vi.fn(() => 'test-error-id'),
    logSisenseError: vi.fn(() => 'test-error-id'),
    createErrorReport: vi.fn(() => 'Test error report'),
  },
}));

// Component that throws an error for testing
const ThrowError: React.FC<{ shouldThrow?: boolean; errorMessage?: string }> = ({ 
  shouldThrow = true, 
  errorMessage = 'Test error' 
}) => {
  if (shouldThrow) {
    throw new Error(errorMessage);
  }
  return <div>No error</div>;
};

describe('Error Boundaries', () => {
  beforeEach(() => {
    // Suppress console.error for cleaner test output
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('GlobalErrorBoundary', () => {
    it('catches and displays errors with retry functionality', () => {
      render(
        <GlobalErrorBoundary>
          <ThrowError errorMessage="Global test error" />
        </GlobalErrorBoundary>
      );

      expect(screen.getByText('Application Error')).toBeInTheDocument();
      expect(screen.getByText(/A critical error occurred that requires reloading the application/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /reload page/i })).toBeInTheDocument();
    });

    it('shows custom fallback when provided', () => {
      const CustomFallback = <div>Custom error fallback</div>;
      
      render(
        <GlobalErrorBoundary fallback={CustomFallback}>
          <ThrowError />
        </GlobalErrorBoundary>
      );

      expect(screen.getByText('Custom error fallback')).toBeInTheDocument();
    });

    it('renders children when no error occurs', () => {
      render(
        <GlobalErrorBoundary>
          <ThrowError shouldThrow={false} />
        </GlobalErrorBoundary>
      );

      expect(screen.getByText('No error')).toBeInTheDocument();
    });
  });

  describe('DashboardErrorBoundary', () => {
    it('catches dashboard-specific errors', () => {
      render(
        <DashboardErrorBoundary dashboardId="test-dashboard">
          <ThrowError errorMessage="Dashboard load failed" />
        </DashboardErrorBoundary>
      );

      expect(screen.getByText('Dashboard Load Error')).toBeInTheDocument();
      expect(screen.getByText(/Failed to load Dashboard test-dashboard/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
    });

    it('handles network errors appropriately', () => {
      render(
        <DashboardErrorBoundary>
          <ThrowError errorMessage="Network timeout error" />
        </DashboardErrorBoundary>
      );

      expect(screen.getByText('Connection Error')).toBeInTheDocument();
      expect(screen.getByText(/Unable to connect to the Sisense server/)).toBeInTheDocument();
    });

    it('renders children when no error occurs', () => {
      render(
        <DashboardErrorBoundary>
          <ThrowError shouldThrow={false} />
        </DashboardErrorBoundary>
      );

      expect(screen.getByText('No error')).toBeInTheDocument();
    });
  });

  describe('WidgetErrorBoundary', () => {
    it('catches widget-specific errors in compact mode', () => {
      render(
        <WidgetErrorBoundary 
          widgetId="test-widget" 
          widgetTitle="Test Widget"
          compact={true}
        >
          <ThrowError errorMessage="Widget render failed" />
        </WidgetErrorBoundary>
      );

      expect(screen.getByText('Render Error')).toBeInTheDocument();
      expect(screen.getByText(/Test Widget failed to render properly/)).toBeInTheDocument();
    });

    it('catches widget-specific errors in full mode', () => {
      render(
        <WidgetErrorBoundary 
          widgetId="test-widget" 
          widgetTitle="Test Widget"
          compact={false}
        >
          <ThrowError errorMessage="Widget data error" />
        </WidgetErrorBoundary>
      );

      expect(screen.getByText('Data Error')).toBeInTheDocument();
      expect(screen.getByText(/Test Widget could not load its data/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /retry widget/i })).toBeInTheDocument();
    });

    it('handles data errors appropriately', () => {
      render(
        <WidgetErrorBoundary widgetId="test-widget">
          <ThrowError errorMessage="Data query failed" />
        </WidgetErrorBoundary>
      );

      expect(screen.getByText('Data Error')).toBeInTheDocument();
    });

    it('renders children when no error occurs', () => {
      render(
        <WidgetErrorBoundary>
          <ThrowError shouldThrow={false} />
        </WidgetErrorBoundary>
      );

      expect(screen.getByText('No error')).toBeInTheDocument();
    });
  });

  describe('SisenseErrorBoundary', () => {
    it('catches Sisense-specific errors', () => {
      render(
        <SisenseErrorBoundary>
          <ThrowError errorMessage="Sisense authentication failed" />
        </SisenseErrorBoundary>
      );

      expect(screen.getByText('Dashboard Error')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
    });

    it('detects authentication errors', () => {
      render(
        <SisenseErrorBoundary>
          <ThrowError errorMessage="Authentication token expired" />
        </SisenseErrorBoundary>
      );

      expect(screen.getByText(/Authentication failed/)).toBeInTheDocument();
    });

    it('detects network errors', () => {
      render(
        <SisenseErrorBoundary>
          <ThrowError errorMessage="Network connection failed" />
        </SisenseErrorBoundary>
      );

      expect(screen.getByText(/Unable to connect to Sisense/)).toBeInTheDocument();
    });

    it('renders children when no error occurs', () => {
      render(
        <SisenseErrorBoundary>
          <ThrowError shouldThrow={false} />
        </SisenseErrorBoundary>
      );

      expect(screen.getByText('No error')).toBeInTheDocument();
    });
  });

  describe('Error Boundary Integration', () => {
    it('can be nested for layered error handling', () => {
      render(
        <GlobalErrorBoundary>
          <DashboardErrorBoundary>
            <WidgetErrorBoundary widgetId="test-widget">
              <ThrowError errorMessage="Widget specific error" />
            </WidgetErrorBoundary>
          </DashboardErrorBoundary>
        </GlobalErrorBoundary>
      );

      // Should be caught by the innermost boundary (WidgetErrorBoundary)
      expect(screen.getByText('Widget Error')).toBeInTheDocument();
      expect(screen.getByText(/Widget test-widget encountered an unexpected error/)).toBeInTheDocument();
    });

    it('escalates to parent boundary when child boundary fails', () => {
      // This would be a more complex test requiring the error boundary itself to throw
      // For now, we'll just verify the basic nesting works
      render(
        <GlobalErrorBoundary>
          <DashboardErrorBoundary>
            <div>Nested boundaries work</div>
          </DashboardErrorBoundary>
        </GlobalErrorBoundary>
      );

      expect(screen.getByText('Nested boundaries work')).toBeInTheDocument();
    });
  });
});