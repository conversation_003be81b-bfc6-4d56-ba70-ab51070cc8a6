import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import DashboardPreferences from '../DashboardPreferences';
import { preferenceUtils } from '@/hooks/useLocalStorageState';

// Mock the hooks
const mockSetDashboardPrefs = vi.fn();
const mockSetAppPrefs = vi.fn();
const mockSetLayoutPrefs = vi.fn();

vi.mock('@/hooks/useLocalStorageState', () => ({
  useDashboardPreferences: vi.fn(() => [
    {
      activeDashboardId: 'test-dashboard',
      collapsedTabs: [],
      widgetSizes: {},
      lastRefreshTime: 0,
      gridColumns: {},
      autoRefreshEnabled: false,
      refreshInterval: 300000,
      showWidgetTitles: true,
      showWidgetIds: false,
      compactMode: false,
      theme: 'system',
      schemaVersion: 2,
    },
    mockSetDashboardPrefs,
  ]),
  useAppPreferences: vi.fn(() => [
    {
      debugMode: false,
      performanceMonitoring: false,
      errorReporting: true,
      analyticsEnabled: false,
      keyboardShortcuts: true,
      animations: true,
      reducedMotion: false,
      highContrast: false,
      fontSize: 'medium',
      language: 'en',
      timezone: 'UTC',
      schemaVersion: 2,
    },
    mockSetAppPrefs,
  ]),
  useDashboardLayoutPreferences: vi.fn(() => [
    {
      layout: 'grid',
      density: 'comfortable',
      sortBy: 'default',
      sortOrder: 'asc',
      filters: {
        showOnlyFavorites: false,
        hideErrorWidgets: false,
        widgetTypes: [],
      },
      schemaVersion: 2,
    },
    mockSetLayoutPrefs,
  ]),
  preferenceUtils: {
    exportPreferences: vi.fn(() => '{"test":"data"}'),
    importPreferences: vi.fn(() => true),
    clearAllPreferences: vi.fn(),
    getStorageUsage: vi.fn(() => ({
      totalSize: 1024,
      totalSizeFormatted: '1.00 KB',
      keyCount: 5,
      breakdown: {},
    })),
  },
}));

// Mock window methods
const mockAlert = vi.fn();
const mockConfirm = vi.fn();
Object.defineProperty(window, 'alert', { value: mockAlert });
Object.defineProperty(window, 'confirm', { value: mockConfirm });

// Mock URL and Blob for file download
const mockCreateObjectURL = vi.fn(() => 'mock-url');
const mockRevokeObjectURL = vi.fn();
Object.defineProperty(URL, 'createObjectURL', { value: mockCreateObjectURL });
Object.defineProperty(URL, 'revokeObjectURL', { value: mockRevokeObjectURL });

// Mock document methods for file download
const mockClick = vi.fn();
const mockAppendChild = vi.fn();
const mockRemoveChild = vi.fn();
const mockCreateElement = vi.fn(() => ({
  href: '',
  download: '',
  click: mockClick,
}));

Object.defineProperty(document, 'createElement', { value: mockCreateElement });
Object.defineProperty(document.body, 'appendChild', { value: mockAppendChild });
Object.defineProperty(document.body, 'removeChild', { value: mockRemoveChild });

describe('DashboardPreferences', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockConfirm.mockReturnValue(false);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders all preference tabs', () => {
    render(<DashboardPreferences dashboardId="test-dashboard" />);

    expect(screen.getByText('Dashboard Preferences')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Layout')).toBeInTheDocument();
    expect(screen.getByText('Application')).toBeInTheDocument();
    expect(screen.getByText('Data')).toBeInTheDocument();
  });

  it('renders close button when onClose is provided', () => {
    const onClose = vi.fn();
    render(
      <DashboardPreferences dashboardId="test-dashboard" onClose={onClose} />
    );

    const closeButton = screen.getByText('×');
    expect(closeButton).toBeInTheDocument();

    fireEvent.click(closeButton);
    expect(onClose).toHaveBeenCalled();
  });

  describe('Dashboard tab', () => {
    it('displays auto refresh toggle', () => {
      render(<DashboardPreferences dashboardId="test-dashboard" />);

      const autoRefreshToggle = screen.getByLabelText('Auto Refresh');
      expect(autoRefreshToggle).toBeInTheDocument();
      expect(autoRefreshToggle).not.toBeChecked();
    });

    it('shows refresh interval slider when auto refresh is enabled', () => {
      const mockUseDashboardPreferences = vi.mocked(
        require('@/hooks/useLocalStorageState').useDashboardPreferences
      );
      mockUseDashboardPreferences.mockReturnValue([
        {
          activeDashboardId: 'test-dashboard',
          autoRefreshEnabled: true,
          refreshInterval: 300000,
          showWidgetTitles: true,
          showWidgetIds: false,
          compactMode: false,
          theme: 'system',
          schemaVersion: 2,
          collapsedTabs: [],
          widgetSizes: {},
          lastRefreshTime: 0,
          gridColumns: {},
        },
        mockSetDashboardPrefs,
      ]);

      render(<DashboardPreferences dashboardId="test-dashboard" />);

      expect(screen.getByText(/Refresh Interval:/)).toBeInTheDocument();
      expect(screen.getByText('5 minutes')).toBeInTheDocument();
    });

    it('updates dashboard preferences when toggles are changed', () => {
      render(<DashboardPreferences dashboardId="test-dashboard" />);

      const showWidgetTitlesToggle = screen.getByLabelText('Show Widget Titles');
      fireEvent.click(showWidgetTitlesToggle);

      expect(mockSetDashboardPrefs).toHaveBeenCalledWith(
        expect.any(Function)
      );
    });

    it('updates theme preference', () => {
      render(<DashboardPreferences dashboardId="test-dashboard" />);

      // Click on the theme select trigger
      const themeSelect = screen.getByDisplayValue('System');
      fireEvent.click(themeSelect);

      // Wait for dropdown to appear and click on Dark option
      waitFor(() => {
        const darkOption = screen.getByText('Dark');
        fireEvent.click(darkOption);
      });

      expect(mockSetDashboardPrefs).toHaveBeenCalledWith(
        expect.any(Function)
      );
    });
  });

  describe('Layout tab', () => {
    beforeEach(() => {
      render(<DashboardPreferences dashboardId="test-dashboard" />);
      fireEvent.click(screen.getByText('Layout'));
    });

    it('displays layout style options', () => {
      expect(screen.getByText('Layout Style')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Grid')).toBeInTheDocument();
    });

    it('displays density options', () => {
      expect(screen.getByText('Density')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Comfortable')).toBeInTheDocument();
    });

    it('displays sort options', () => {
      expect(screen.getByText('Sort By')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Default')).toBeInTheDocument();
      expect(screen.getByText('Sort Order')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Ascending')).toBeInTheDocument();
    });

    it('displays filter options', () => {
      expect(screen.getByLabelText('Hide Error Widgets')).toBeInTheDocument();
    });

    it('updates layout preferences when changed', () => {
      const hideErrorWidgetsToggle = screen.getByLabelText('Hide Error Widgets');
      fireEvent.click(hideErrorWidgetsToggle);

      expect(mockSetLayoutPrefs).toHaveBeenCalledWith(
        expect.any(Function)
      );
    });
  });

  describe('Application tab', () => {
    beforeEach(() => {
      render(<DashboardPreferences dashboardId="test-dashboard" />);
      fireEvent.click(screen.getByText('Application'));
    });

    it('displays application preference toggles', () => {
      expect(screen.getByLabelText('Debug Mode')).toBeInTheDocument();
      expect(screen.getByLabelText('Performance Monitoring')).toBeInTheDocument();
      expect(screen.getByLabelText('Animations')).toBeInTheDocument();
      expect(screen.getByLabelText('Reduced Motion')).toBeInTheDocument();
      expect(screen.getByLabelText('High Contrast')).toBeInTheDocument();
    });

    it('displays font size selector', () => {
      expect(screen.getByText('Font Size')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Medium')).toBeInTheDocument();
    });

    it('updates app preferences when changed', () => {
      const debugModeToggle = screen.getByLabelText('Debug Mode');
      fireEvent.click(debugModeToggle);

      expect(mockSetAppPrefs).toHaveBeenCalledWith(
        expect.any(Function)
      );
    });
  });

  describe('Data tab', () => {
    beforeEach(() => {
      render(<DashboardPreferences dashboardId="test-dashboard" />);
      fireEvent.click(screen.getByText('Data'));
    });

    it('displays storage usage information', () => {
      expect(screen.getByText('Storage Usage')).toBeInTheDocument();
      expect(screen.getByText('1.00 KB (5 items)')).toBeInTheDocument();
    });

    it('displays export button', () => {
      const exportButton = screen.getByText('Export Preferences');
      expect(exportButton).toBeInTheDocument();
    });

    it('displays import textarea and button', () => {
      expect(
        screen.getByPlaceholderText('Paste exported preferences here to import...')
      ).toBeInTheDocument();
      expect(screen.getByText('Import Preferences')).toBeInTheDocument();
    });

    it('displays reset button', () => {
      expect(screen.getByText('Reset All Preferences')).toBeInTheDocument();
    });

    it('handles export preferences', () => {
      const exportButton = screen.getByText('Export Preferences');
      fireEvent.click(exportButton);

      expect(preferenceUtils.exportPreferences).toHaveBeenCalled();
      expect(mockCreateObjectURL).toHaveBeenCalled();
      expect(mockCreateElement).toHaveBeenCalledWith('a');
      expect(mockClick).toHaveBeenCalled();
    });

    it('handles import preferences', () => {
      const importTextarea = screen.getByPlaceholderText(
        'Paste exported preferences here to import...'
      );
      const importButton = screen.getByText('Import Preferences');

      // Initially disabled
      expect(importButton).toBeDisabled();

      // Add some text
      fireEvent.change(importTextarea, {
        target: { value: '{"test":"data"}' },
      });

      // Should be enabled now
      expect(importButton).not.toBeDisabled();

      // Click import
      fireEvent.click(importButton);

      expect(preferenceUtils.importPreferences).toHaveBeenCalledWith(
        '{"test":"data"}'
      );
      expect(mockAlert).toHaveBeenCalledWith(
        'Preferences imported successfully! Please refresh the page.'
      );
    });

    it('handles import failure', () => {
      const mockImportPreferences = vi.mocked(preferenceUtils.importPreferences);
      mockImportPreferences.mockReturnValue(false);

      const importTextarea = screen.getByPlaceholderText(
        'Paste exported preferences here to import...'
      );
      const importButton = screen.getByText('Import Preferences');

      fireEvent.change(importTextarea, {
        target: { value: 'invalid-json' },
      });
      fireEvent.click(importButton);

      expect(mockAlert).toHaveBeenCalledWith(
        'Failed to import preferences. Please check the format.'
      );
    });

    it('handles reset preferences with confirmation', () => {
      mockConfirm.mockReturnValue(true);

      const resetButton = screen.getByText('Reset All Preferences');
      fireEvent.click(resetButton);

      expect(mockConfirm).toHaveBeenCalledWith(
        'Are you sure you want to reset all preferences? This cannot be undone.'
      );
      expect(preferenceUtils.clearAllPreferences).toHaveBeenCalled();
      expect(mockAlert).toHaveBeenCalledWith(
        'Preferences reset successfully! Please refresh the page.'
      );
    });

    it('cancels reset when user declines confirmation', () => {
      mockConfirm.mockReturnValue(false);

      const resetButton = screen.getByText('Reset All Preferences');
      fireEvent.click(resetButton);

      expect(mockConfirm).toHaveBeenCalled();
      expect(preferenceUtils.clearAllPreferences).not.toHaveBeenCalled();
    });

    it('refreshes storage info', () => {
      const refreshButton = screen.getByText('Refresh');
      fireEvent.click(refreshButton);

      expect(preferenceUtils.getStorageUsage).toHaveBeenCalled();
    });
  });

  describe('accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<DashboardPreferences dashboardId="test-dashboard" />);

      // Check for proper tab structure
      const tabList = screen.getByRole('tablist');
      expect(tabList).toBeInTheDocument();

      const tabs = screen.getAllByRole('tab');
      expect(tabs).toHaveLength(4);

      // Check for proper form controls
      const switches = screen.getAllByRole('switch');
      expect(switches.length).toBeGreaterThan(0);
    });

    it('supports keyboard navigation', () => {
      render(<DashboardPreferences dashboardId="test-dashboard" />);

      const firstTab = screen.getByText('Dashboard');
      firstTab.focus();

      // Simulate Tab key to move to next focusable element
      fireEvent.keyDown(firstTab, { key: 'Tab' });

      // The focus should move to the next tab or control
      expect(document.activeElement).not.toBe(firstTab);
    });
  });

  describe('responsive behavior', () => {
    it('applies custom className', () => {
      const { container } = render(
        <DashboardPreferences
          dashboardId="test-dashboard"
          className="custom-class"
        />
      );

      expect(container.firstChild).toHaveClass('custom-class');
    });
  });

  describe('error handling', () => {
    it('handles preference update errors gracefully', () => {
      mockSetDashboardPrefs.mockImplementation(() => {
        throw new Error('Update failed');
      });

      render(<DashboardPreferences dashboardId="test-dashboard" />);

      const toggle = screen.getByLabelText('Show Widget Titles');
      
      // Should not crash when update fails
      expect(() => fireEvent.click(toggle)).not.toThrow();
    });
  });
});