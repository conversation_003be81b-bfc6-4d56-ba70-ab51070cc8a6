import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ComposeWidgetById from '../ComposeWidgetById';

// Mock Sisense SDK
const mockWidgetById = vi.fn();
vi.mock('@/lib/sisenseImports', () => ({
  WidgetById: mockWidgetById,
}));

// Mock shadcn/ui components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div className={className} data-testid="card">{children}</div>
  ),
  CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div className={className} data-testid="card-content">{children}</div>
  ),
  CardHeader: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div className={className} data-testid="card-header">{children}</div>
  ),
  CardTitle: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <h3 className={className} data-testid="card-title">{children}</h3>
  ),
}));

vi.mock('@/components/ui/skeleton', () => ({
  Skeleton: ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
    <div className={className} style={style} data-testid="widget-skeleton">Loading...</div>
  ),
}));

vi.mock('@/components/ui/alert', () => ({
  Alert: ({ children, variant, className }: { children: React.ReactNode; variant?: string; className?: string }) => (
    <div className={className} data-testid="alert" data-variant={variant}>{children}</div>
  ),
  AlertDescription: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div className={className} data-testid="alert-description">{children}</div>
  ),
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, variant, size, className }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      className={className}
      data-testid="button"
      data-variant={variant}
      data-size={size}
    >
      {children}
    </button>
  ),
}));

// Mock WidgetErrorBoundary
vi.mock('../WidgetErrorBoundary', () => ({
  WidgetErrorBoundary: ({ children, onRetry }: { children: React.ReactNode; onRetry?: () => void }) => (
    <div data-testid="widget-error-boundary">
      {children}
      {onRetry && <button onClick={onRetry} data-testid="error-boundary-retry">Retry</button>}
    </div>
  ),
}));

// Mock utils
vi.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

describe('ComposeWidgetById', () => {
  const defaultProps = {
    widgetOid: 'test-widget-id',
    dashboardOid: 'test-dashboard-id',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockWidgetById.mockImplementation(({ children, ...props }) => (
      <div data-testid="sisense-widget" {...props}>
        {children || 'Mock Sisense Widget'}
      </div>
    ));
  });

  it('renders without crashing', () => {
    render(<ComposeWidgetById {...defaultProps} />);
    
    expect(screen.getByTestId('sisense-widget')).toBeInTheDocument();
  });

  it('passes correct props to WidgetById component', () => {
    const styleOptions = { width: 400, height: 300 };
    
    render(
      <ComposeWidgetById 
        {...defaultProps}
        height={300}
        width={400}
        styleOptions={styleOptions}
      />
    );

    expect(mockWidgetById).toHaveBeenCalledWith(
      expect.objectContaining({
        widgetOid: 'test-widget-id',
        dashboardOid: 'test-dashboard-id',
        styleOptions: expect.objectContaining(styleOptions),
      }),
      expect.any(Object)
    );
  });

  it('renders title when showTitle is true', () => {
    render(
      <ComposeWidgetById 
        {...defaultProps}
        title="Test Widget Title"
        showTitle={true}
      />
    );

    expect(screen.getByText('Test Widget Title')).toBeInTheDocument();
  });

  it('does not render title when showTitle is false', () => {
    render(
      <ComposeWidgetById 
        {...defaultProps}
        title="Test Widget Title"
        showTitle={false}
      />
    );

    expect(screen.queryByText('Test Widget Title')).not.toBeInTheDocument();
  });

  it('renders widget ID when showWidgetId is true', () => {
    render(
      <ComposeWidgetById 
        {...defaultProps}
        showWidgetId={true}
      />
    );

    expect(screen.getByText('Widget ID: test-widget-id')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <ComposeWidgetById 
        {...defaultProps}
        className="custom-widget-class"
      />
    );

    expect(container.firstChild).toHaveClass('custom-widget-class');
  });

  it('shows loading state initially', () => {
    render(<ComposeWidgetById {...defaultProps} />);
    
    // Should show skeleton loading state
    expect(screen.getByTestId('widget-skeleton')).toBeInTheDocument();
  });

  it('calls onLoad callback when widget loads successfully', async () => {
    const onLoad = vi.fn();
    
    mockWidgetById.mockImplementation((props) => {
      // Simulate successful load
      setTimeout(() => props.onLoad?.(), 100);
      return <div data-testid="sisense-widget">Loaded Widget</div>;
    });

    render(
      <ComposeWidgetById 
        {...defaultProps}
        onLoad={onLoad}
      />
    );

    await waitFor(() => {
      expect(onLoad).toHaveBeenCalled();
    });
  });

  it('calls onError callback when widget fails to load', async () => {
    const onError = vi.fn();
    const testError = new Error('Widget load failed');
    
    mockWidgetById.mockImplementation((props) => {
      // Simulate error
      setTimeout(() => props.onError?.(testError), 100);
      return <div data-testid="sisense-widget">Error Widget</div>;
    });

    render(
      <ComposeWidgetById 
        {...defaultProps}
        onError={onError}
      />
    );

    await waitFor(() => {
      expect(onError).toHaveBeenCalledWith(testError);
    });
  });

  it('handles missing widgetOid gracefully', () => {
    render(
      <ComposeWidgetById 
        widgetOid=""
        dashboardOid="test-dashboard-id"
      />
    );

    // Should still render but with error state
    expect(screen.getByTestId('widget-skeleton')).toBeInTheDocument();
  });

  it('handles missing dashboardOid gracefully', () => {
    render(
      <ComposeWidgetById 
        widgetOid="test-widget-id"
        dashboardOid=""
      />
    );

    // Should still render but with error state
    expect(screen.getByTestId('widget-skeleton')).toBeInTheDocument();
  });

  it('renders with default dimensions when not specified', () => {
    render(<ComposeWidgetById {...defaultProps} />);

    expect(mockWidgetById).toHaveBeenCalledWith(
      expect.objectContaining({
        styleOptions: expect.objectContaining({
          width: 400,
          height: 300,
        }),
      }),
      expect.any(Object)
    );
  });

  it('renders with custom dimensions', () => {
    render(
      <ComposeWidgetById 
        {...defaultProps}
        width={600}
        height={400}
      />
    );

    expect(mockWidgetById).toHaveBeenCalledWith(
      expect.objectContaining({
        styleOptions: expect.objectContaining({
          width: 600,
          height: 400,
        }),
      }),
      expect.any(Object)
    );
  });

  it('merges custom styleOptions with default dimensions', () => {
    const customStyleOptions = {
      backgroundColor: '#f0f0f0',
      border: '1px solid #ccc',
    };

    render(
      <ComposeWidgetById 
        {...defaultProps}
        width={500}
        height={350}
        styleOptions={customStyleOptions}
      />
    );

    expect(mockWidgetById).toHaveBeenCalledWith(
      expect.objectContaining({
        styleOptions: expect.objectContaining({
          width: 500,
          height: 350,
          backgroundColor: '#f0f0f0',
          border: '1px solid #ccc',
        }),
      }),
      expect.any(Object)
    );
  });

  it('renders with Card wrapper when showTitle is true', () => {
    render(
      <ComposeWidgetById 
        {...defaultProps}
        title="Test Widget Title"
        showTitle={true}
      />
    );

    expect(screen.getByTestId('card')).toBeInTheDocument();
    expect(screen.getByTestId('card-header')).toBeInTheDocument();
    expect(screen.getByTestId('card-title')).toBeInTheDocument();
    expect(screen.getByTestId('card-content')).toBeInTheDocument();
  });

  it('renders without Card wrapper when showTitle is false', () => {
    render(
      <ComposeWidgetById 
        {...defaultProps}
        title="Test Widget Title"
        showTitle={false}
      />
    );

    expect(screen.queryByTestId('card')).not.toBeInTheDocument();
    expect(screen.queryByTestId('card-header')).not.toBeInTheDocument();
    expect(screen.queryByTestId('card-title')).not.toBeInTheDocument();
  });

  it('shows error fallback with retry button', async () => {
    const onRetry = vi.fn();
    const testError = new Error('Widget load failed');
    
    // Mock error state
    mockWidgetById.mockImplementation(() => {
      throw testError;
    });

    render(
      <ComposeWidgetById 
        {...defaultProps}
        onError={onRetry}
      />
    );

    // Wait for error state to appear
    await waitFor(() => {
      expect(screen.getByText(/Widget Load Error/i)).toBeInTheDocument();
    });

    expect(screen.getByText('Widget load failed')).toBeInTheDocument();
    expect(screen.getByText(`Widget ID: ${defaultProps.widgetOid}`)).toBeInTheDocument();
    
    const retryButton = screen.getByText(/Retry Widget/i);
    expect(retryButton).toBeInTheDocument();
    
    fireEvent.click(retryButton);
    // Retry functionality should reset the error state
  });

  it('shows compact error fallback for widgets without titles', async () => {
    const testError = new Error('Widget load failed');
    
    mockWidgetById.mockImplementation(() => {
      throw testError;
    });

    render(
      <ComposeWidgetById 
        {...defaultProps}
        showTitle={false}
      />
    );

    await waitFor(() => {
      expect(screen.getByText(/Widget Error/i)).toBeInTheDocument();
    });

    expect(screen.getByText('Failed to load widget')).toBeInTheDocument();
  });

  it('handles retry functionality correctly', async () => {
    let shouldError = true;
    const onLoad = vi.fn();
    
    mockWidgetById.mockImplementation(() => {
      if (shouldError) {
        throw new Error('Initial error');
      }
      return <div data-testid="sisense-widget">Loaded Widget</div>;
    });

    render(
      <ComposeWidgetById 
        {...defaultProps}
        onLoad={onLoad}
      />
    );

    // Wait for error state
    await waitFor(() => {
      expect(screen.getByText(/Widget Load Error/i)).toBeInTheDocument();
    });

    // Click retry
    shouldError = false;
    const retryButton = screen.getByText(/Retry Widget/i);
    fireEvent.click(retryButton);

    // Should show loading state again
    expect(screen.getByTestId('widget-skeleton')).toBeInTheDocument();
  });

  it('applies correct CSS classes', () => {
    const { container } = render(
      <ComposeWidgetById 
        {...defaultProps}
        className="custom-widget-class"
      />
    );

    expect(container.firstChild).toHaveClass('w-full');
    expect(container.firstChild).toHaveClass('custom-widget-class');
  });

  it('renders loading skeleton with correct height', () => {
    render(
      <ComposeWidgetById 
        {...defaultProps}
        height={600}
      />
    );

    const skeleton = screen.getByTestId('widget-skeleton');
    expect(skeleton).toHaveStyle({ height: '600px' });
  });

  it('shows loading indicator with spinner', () => {
    render(<ComposeWidgetById {...defaultProps} />);
    
    expect(screen.getByText('Loading widget...')).toBeInTheDocument();
    // The RefreshCw icon should be present with animate-spin class
    expect(screen.getByTestId('widget-skeleton')).toBeInTheDocument();
  });

  it('handles widget error boundary integration', () => {
    render(<ComposeWidgetById {...defaultProps} />);
    
    expect(screen.getByTestId('widget-error-boundary')).toBeInTheDocument();
  });

  it('memoizes props correctly to prevent unnecessary re-renders', () => {
    const { rerender } = render(<ComposeWidgetById {...defaultProps} />);
    
    const initialCallCount = mockWidgetById.mock.calls.length;
    
    // Re-render with same props
    rerender(<ComposeWidgetById {...defaultProps} />);
    
    // Should not call WidgetById again due to memoization
    expect(mockWidgetById.mock.calls.length).toBe(initialCallCount);
  });

  it('re-renders when props change', () => {
    const { rerender } = render(<ComposeWidgetById {...defaultProps} />);
    
    const initialCallCount = mockWidgetById.mock.calls.length;
    
    // Re-render with different props
    rerender(<ComposeWidgetById {...defaultProps} height={500} />);
    
    // Should call WidgetById again due to prop change
    expect(mockWidgetById.mock.calls.length).toBeGreaterThan(initialCallCount);
  });
});