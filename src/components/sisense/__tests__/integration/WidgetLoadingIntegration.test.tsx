import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { ComposeWidgetById } from '../../ComposeWidgetById';
import { WidgetGrid } from '../../WidgetGrid';
import { WidgetErrorBoundary } from '../../WidgetErrorBoundary';

// Mock Sisense SDK
const mockWidgetById = vi.fn();
vi.mock('@sisense/sdk-ui', () => ({
  WidgetById: mockWidgetById,
}));

// Mock error logging service
const mockLogSisenseError = vi.fn();
vi.mock('@/services/errorLoggingService', () => ({
  errorLoggingService: {
    logSisenseError: mockLogSisenseError,
  },
}));

describe('Widget Loading Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ComposeWidgetById with real Sisense data', () => {
    it('loads widget successfully with real data structure', async () => {
      const mockWidget = {
        widgetOid: 'real-widget-123',
        dashboardOid: 'real-dashboard-456',
        title: 'Revenue by Quarter',
      };

      mockWidgetById.mockImplementation((props) => {
        // Simulate successful load after delay
        setTimeout(() => {
          props.onLoad?.();
        }, 100);
        
        return (
          <div data-testid="loaded-widget">
            <div>Chart: {props.widgetOid}</div>
            <div>Dashboard: {props.dashboardOid}</div>
          </div>
        );
      });

      const onLoad = vi.fn();
      
      render(
        <ComposeWidgetById
          widgetOid={mockWidget.widgetOid}
          dashboardOid={mockWidget.dashboardOid}
          title={mockWidget.title}
          onLoad={onLoad}
        />
      );

      // Should show loading state initially
      expect(screen.getByTestId('widget-skeleton')).toBeInTheDocument();

      // Wait for widget to load
      await waitFor(() => {
        expect(screen.getByTestId('loaded-widget')).toBeInTheDocument();
      });

      expect(onLoad).toHaveBeenCalled();
      expect(screen.getByText('Chart: real-widget-123')).toBeInTheDocument();
      expect(screen.getByText('Dashboard: real-dashboard-456')).toBeInTheDocument();
    });

    it('handles authentication errors during widget load', async () => {
      const authError = new Error('Authentication failed');
      authError.name = 'AuthenticationError';

      mockWidgetById.mockImplementation((props) => {
        setTimeout(() => {
          props.onError?.(authError);
        }, 100);
        
        return <div data-testid="error-widget">Auth Error</div>;
      });

      const onError = vi.fn();
      
      render(
        <WidgetErrorBoundary widgetId="test-widget">
          <ComposeWidgetById
            widgetOid="test-widget"
            dashboardOid="test-dashboard"
            onError={onError}
          />
        </WidgetErrorBoundary>
      );

      await waitFor(() => {
        expect(onError).toHaveBeenCalledWith(authError);
      });

      expect(mockLogSisenseError).toHaveBeenCalledWith(authError, {
        widgetId: 'test-widget',
        customData: expect.any(Object),
      });
    });

    it('handles network timeouts during widget load', async () => {
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';

      mockWidgetById.mockImplementation((props) => {
        setTimeout(() => {
          props.onError?.(timeoutError);
        }, 100);
        
        return <div data-testid="timeout-widget">Timeout</div>;
      });

      const onError = vi.fn();
      
      render(
        <ComposeWidgetById
          widgetOid="timeout-widget"
          dashboardOid="test-dashboard"
          onError={onError}
        />
      );

      await waitFor(() => {
        expect(onError).toHaveBeenCalledWith(timeoutError);
      });
    });

    it('handles widget data loading errors', async () => {
      const dataError = new Error('Failed to load widget data');
      dataError.name = 'DataError';

      mockWidgetById.mockImplementation((props) => {
        setTimeout(() => {
          props.onError?.(dataError);
        }, 100);
        
        return <div data-testid="data-error-widget">Data Error</div>;
      });

      render(
        <WidgetErrorBoundary widgetId="data-widget" compact={true}>
          <ComposeWidgetById
            widgetOid="data-widget"
            dashboardOid="test-dashboard"
          />
        </WidgetErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByText('Data Error')).toBeInTheDocument();
      });
    });
  });

  describe('WidgetGrid with multiple widgets', () => {
    const mockWidgets = [
      { oid: 'widget-1', title: 'Revenue Chart', type: 'chart' },
      { oid: 'widget-2', title: 'Sales Table', type: 'table' },
      { oid: 'widget-3', title: 'KPI Indicator', type: 'indicator' },
    ];

    it('loads multiple widgets concurrently', async () => {
      let loadedWidgets = 0;
      
      mockWidgetById.mockImplementation((props) => {
        // Simulate staggered loading
        const delay = (loadedWidgets + 1) * 50;
        setTimeout(() => {
          loadedWidgets++;
          props.onLoad?.();
        }, delay);
        
        return (
          <div data-testid={`widget-${props.widgetOid}`}>
            Widget {props.widgetOid}
          </div>
        );
      });

      render(
        <WidgetGrid
          widgets={mockWidgets}
          dashboardOid="test-dashboard"
        />
      );

      // All widgets should eventually load
      await waitFor(() => {
        expect(screen.getByTestId('widget-widget-1')).toBeInTheDocument();
        expect(screen.getByTestId('widget-widget-2')).toBeInTheDocument();
        expect(screen.getByTestId('widget-widget-3')).toBeInTheDocument();
      });

      expect(loadedWidgets).toBe(3);
    });

    it('handles partial widget failures gracefully', async () => {
      mockWidgetById.mockImplementation((props) => {
        if (props.widgetOid === 'widget-2') {
          // Simulate failure for second widget
          setTimeout(() => {
            props.onError?.(new Error('Widget 2 failed'));
          }, 50);
          return <div data-testid="failed-widget">Failed</div>;
        }
        
        // Success for other widgets
        setTimeout(() => {
          props.onLoad?.();
        }, 100);
        
        return (
          <div data-testid={`widget-${props.widgetOid}`}>
            Widget {props.widgetOid}
          </div>
        );
      });

      render(
        <WidgetGrid
          widgets={mockWidgets}
          dashboardOid="test-dashboard"
        />
      );

      await waitFor(() => {
        // Successful widgets should load
        expect(screen.getByTestId('widget-widget-1')).toBeInTheDocument();
        expect(screen.getByTestId('widget-widget-3')).toBeInTheDocument();
        
        // Failed widget should show error
        expect(screen.getByText('Widget Error')).toBeInTheDocument();
      });
    });

    it('handles widget retry functionality', async () => {
      let attemptCount = 0;
      
      mockWidgetById.mockImplementation((props) => {
        attemptCount++;
        
        if (attemptCount === 1) {
          // First attempt fails
          setTimeout(() => {
            props.onError?.(new Error('First attempt failed'));
          }, 50);
          return <div data-testid="retry-widget">Retry Widget</div>;
        } else {
          // Second attempt succeeds
          setTimeout(() => {
            props.onLoad?.();
          }, 50);
          return (
            <div data-testid="successful-widget">
              Successful Widget
            </div>
          );
        }
      });

      render(
        <WidgetErrorBoundary widgetId="retry-widget">
          <ComposeWidgetById
            widgetOid="retry-widget"
            dashboardOid="test-dashboard"
          />
        </WidgetErrorBoundary>
      );

      // Wait for initial failure
      await waitFor(() => {
        expect(screen.getByText('Render Error')).toBeInTheDocument();
      });

      // Click retry button
      const retryButton = screen.getByText('Retry Widget');
      fireEvent.click(retryButton);

      // Wait for successful retry
      await waitFor(() => {
        expect(screen.getByTestId('successful-widget')).toBeInTheDocument();
      });

      expect(attemptCount).toBe(2);
    });
  });

  describe('Error boundary integration', () => {
    it('isolates widget errors from affecting other widgets', async () => {
      const widgets = [
        { oid: 'good-widget', title: 'Good Widget' },
        { oid: 'bad-widget', title: 'Bad Widget' },
        { oid: 'another-good-widget', title: 'Another Good Widget' },
      ];

      mockWidgetById.mockImplementation((props) => {
        if (props.widgetOid === 'bad-widget') {
          throw new Error('Widget rendering error');
        }
        
        return (
          <div data-testid={`widget-${props.widgetOid}`}>
            {props.widgetOid}
          </div>
        );
      });

      render(
        <WidgetGrid
          widgets={widgets}
          dashboardOid="test-dashboard"
        />
      );

      // Good widgets should render successfully
      expect(screen.getByTestId('widget-good-widget')).toBeInTheDocument();
      expect(screen.getByTestId('widget-another-good-widget')).toBeInTheDocument();
      
      // Bad widget should show error without breaking others
      expect(screen.getByText('Render Error')).toBeInTheDocument();
    });

    it('provides detailed error information in development', async () => {
      // Mock development environment
      const originalEnv = import.meta.env.DEV;
      Object.defineProperty(import.meta.env, 'DEV', { value: true });

      const detailedError = new Error('Detailed widget error with stack trace');
      detailedError.stack = 'Error: Detailed widget error\n    at Widget.render\n    at Component.render';

      mockWidgetById.mockImplementation(() => {
        throw detailedError;
      });

      render(
        <WidgetErrorBoundary widgetId="error-widget" showDetails={true}>
          <ComposeWidgetById
            widgetOid="error-widget"
            dashboardOid="test-dashboard"
          />
        </WidgetErrorBoundary>
      );

      // Should show detailed error information
      expect(screen.getByText('Technical Details')).toBeInTheDocument();
      
      // Restore environment
      Object.defineProperty(import.meta.env, 'DEV', { value: originalEnv });
    });
  });

  describe('Performance and memory management', () => {
    it('handles large number of widgets efficiently', async () => {
      const manyWidgets = Array.from({ length: 50 }, (_, i) => ({
        oid: `widget-${i}`,
        title: `Widget ${i}`,
        type: 'chart',
      }));

      let renderCount = 0;
      
      mockWidgetById.mockImplementation((props) => {
        renderCount++;
        return (
          <div data-testid={`widget-${props.widgetOid}`}>
            Widget {props.widgetOid}
          </div>
        );
      });

      const { unmount } = render(
        <WidgetGrid
          widgets={manyWidgets}
          dashboardOid="test-dashboard"
        />
      );

      // Should render all widgets
      expect(renderCount).toBe(50);

      // Cleanup should not cause memory leaks
      unmount();
      
      // Verify cleanup (this would be more comprehensive in a real test)
      expect(renderCount).toBe(50); // Should not increase after unmount
    });

    it('handles widget unmounting during load', async () => {
      let isUnmounted = false;
      
      mockWidgetById.mockImplementation((props) => {
        // Simulate slow loading widget
        setTimeout(() => {
          if (!isUnmounted) {
            props.onLoad?.();
          }
        }, 200);
        
        return (
          <div data-testid="slow-widget">
            Slow Loading Widget
          </div>
        );
      });

      const { unmount } = render(
        <ComposeWidgetById
          widgetOid="slow-widget"
          dashboardOid="test-dashboard"
        />
      );

      // Unmount before widget finishes loading
      setTimeout(() => {
        isUnmounted = true;
        unmount();
      }, 100);

      // Should not cause errors or memory leaks
      await new Promise(resolve => setTimeout(resolve, 300));
    });
  });
});