import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import ComposeDashboard from '../ComposeDashboard';

// Mock the hooks and components
vi.mock('@/hooks/useDashboardManager', () => ({
  useDashboardManager: () => ({
    dashboards: [],
    selectedDashboardId: null,
    selectedDashboard: undefined,
    widgets: [],
    tabs: [],
    isLoadingMetadata: false,
    isLoadingWidgets: false,
    isRefreshing: false,
    metadataError: null,
    widgetsError: null,
    healthStatus: 'healthy',
    configValidation: { isValid: true, errors: [] },
    selectDashboard: vi.fn(),
    refreshAll: vi.fn(),
    refreshMetadata: vi.fn(),
    refreshWidgets: vi.fn(),
    retryLastOperation: vi.fn(),
    clearErrors: vi.fn(),
    reset: vi.fn(),
    getDashboardById: vi.fn(),
    getDashboardBySlug: vi.fn(),
    getNextDashboard: vi.fn(),
    getPreviousDashboard: vi.fn(),
    cacheStats: { size: 0, maxSize: 10, entries: [] },
    clearCache: vi.fn(),
  }),
}));

vi.mock('../SisenseContextProvider', () => ({
  SisenseContextProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="sisense-context">{children}</div>,
}));

vi.mock('../SisenseErrorBoundary', () => ({
  SisenseErrorBoundary: ({ children }: { children: React.ReactNode }) => <div data-testid="sisense-error-boundary">{children}</div>,
}));

vi.mock('../DashboardErrorBoundary', () => ({
  DashboardErrorBoundary: ({ children }: { children: React.ReactNode }) => <div data-testid="dashboard-error-boundary">{children}</div>,
}));

vi.mock('../DashboardTabsContainer', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="dashboard-tabs-container">{children}</div>,
}));

describe('ComposeDashboard', () => {
  it('renders without crashing', () => {
    render(<ComposeDashboard />);
    
    // Check that the main structure is rendered
    expect(screen.getByTestId('sisense-context')).toBeInTheDocument();
    expect(screen.getByTestId('sisense-error-boundary')).toBeInTheDocument();
    expect(screen.getByTestId('dashboard-error-boundary')).toBeInTheDocument();
    expect(screen.getByTestId('dashboard-tabs-container')).toBeInTheDocument();
  });

  it('renders with custom className', () => {
    const { container } = render(<ComposeDashboard className="custom-class" />);
    
    // The className should be applied to the main container
    const mainContainer = container.querySelector('.custom-class');
    expect(mainContainer).toBeInTheDocument();
  });

  it('renders debug info when showDebugInfo is true', () => {
    render(<ComposeDashboard showDebugInfo={true} />);
    
    // Should show debug information
    expect(screen.getByText('Debug Information')).toBeInTheDocument();
  });

  it('does not render debug info when showDebugInfo is false', () => {
    render(<ComposeDashboard showDebugInfo={false} />);
    
    // Should not show debug information
    expect(screen.queryByText('Debug Information')).not.toBeInTheDocument();
  });

  it('renders refresh controls when showRefreshControls is true', () => {
    render(<ComposeDashboard showRefreshControls={true} />);
    
    // Should show refresh controls (this will be rendered by the RefreshControls component)
    // We can't test the exact button here due to mocking, but we can verify the structure
    expect(screen.getByTestId('dashboard-tabs-container')).toBeInTheDocument();
  });
});