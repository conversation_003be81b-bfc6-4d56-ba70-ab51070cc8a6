import { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, Eye, EyeOff } from 'lucide-react';
import { errorLoggingService } from '@/services/errorLoggingService';

interface Props {
  children: ReactNode;
  widgetId?: string;
  widgetTitle?: string;
  fallback?: ReactNode;
  onRetry?: () => void;
  onHide?: () => void;
  showDetails?: boolean;
  compact?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  isHidden: boolean;
}

/**
 * Error boundary for individual widget errors
 * Isolates widget failures to prevent them from breaking the entire dashboard
 */
export class WidgetErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isHidden: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Widget Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });

    // Log error with error logging service
    errorLoggingService.logReactError(error, errorInfo, {
      widgetId: this.props.widgetId,
      customData: {
        boundaryType: 'widget',
        widgetTitle: this.props.widgetTitle,
        compact: this.props.compact,
      }
    });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isHidden: false,
    });
    
    // Call external retry handler if provided
    this.props.onRetry?.();
  };

  handleHide = () => {
    this.setState({ isHidden: true });
    this.props.onHide?.();
  };

  handleShow = () => {
    this.setState({ isHidden: false });
  };

  private getErrorType(): 'render' | 'data' | 'network' | 'auth' | 'unknown' {
    const errorMessage = this.state.error?.message?.toLowerCase() || '';
    
    if (errorMessage.includes('render') || errorMessage.includes('component')) {
      return 'render';
    }
    if (errorMessage.includes('data') || errorMessage.includes('query') || errorMessage.includes('jaql')) {
      return 'data';
    }
    if (errorMessage.includes('network') || errorMessage.includes('fetch') || errorMessage.includes('timeout')) {
      return 'network';
    }
    if (errorMessage.includes('auth') || errorMessage.includes('token') || errorMessage.includes('unauthorized')) {
      return 'auth';
    }
    return 'unknown';
  }

  private getErrorMessage(): { title: string; description: string } {
    const errorType = this.getErrorType();
    const widgetName = this.props.widgetTitle || `Widget ${this.props.widgetId || 'Unknown'}`;
    
    switch (errorType) {
      case 'render':
        return {
          title: 'Render Error',
          description: `${widgetName} failed to render properly.`,
        };
      case 'data':
        return {
          title: 'Data Error',
          description: `${widgetName} could not load its data.`,
        };
      case 'network':
        return {
          title: 'Connection Error',
          description: `${widgetName} could not connect to the server.`,
        };
      case 'auth':
        return {
          title: 'Permission Error',
          description: `You don't have permission to view ${widgetName}.`,
        };
      default:
        return {
          title: 'Widget Error',
          description: `${widgetName} encountered an unexpected error.`,
        };
    }
  }

  render() {
    // Hidden state
    if (this.state.isHidden) {
      return (
        <Card className="border-dashed border-gray-300 dark:border-gray-600">
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
              <EyeOff className="h-4 w-4" />
              <span>Widget hidden due to error</span>
              <Button variant="ghost" size="sm" onClick={this.handleShow}>
                <Eye className="h-4 w-4 mr-1" />
                Show
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    if (this.state.hasError) {
      // Custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { title, description } = this.getErrorMessage();
      const showDetails = this.props.showDetails ?? process.env.NODE_ENV === 'development';
      const compact = this.props.compact ?? false;

      // Compact error display for grid layouts
      if (compact) {
        return (
          <Card className="border-red-200 dark:border-red-800">
            <CardContent className="p-4">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-red-900 dark:text-red-100 truncate">
                    {title}
                  </p>
                  <p className="text-xs text-red-700 dark:text-red-300 mt-1">
                    {description}
                  </p>
                  <div className="flex space-x-1 mt-2">
                    <Button variant="outline" size="sm" onClick={this.handleRetry}>
                      <RefreshCw className="h-3 w-3" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={this.handleHide}>
                      <EyeOff className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      }

      // Full error display
      return (
        <Card className="border-red-200 dark:border-red-800">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                <div>
                  <CardTitle className="text-red-900 dark:text-red-100 text-base">
                    {title}
                  </CardTitle>
                  <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                    {description}
                  </p>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={this.handleHide}>
                <EyeOff className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-0 space-y-3">
            {showDetails && this.state.error && (
              <Alert variant="destructive">
                <AlertDescription>
                  <details className="text-xs">
                    <summary className="cursor-pointer font-medium mb-2">
                      Technical Details
                    </summary>
                    <pre className="whitespace-pre-wrap break-words">
                      {this.state.error.message}
                    </pre>
                  </details>
                </AlertDescription>
              </Alert>
            )}
            
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={this.handleRetry} className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Widget
              </Button>
            </div>

            {this.props.widgetId && (
              <p className="text-xs text-muted-foreground">
                Widget ID: {this.props.widgetId}
              </p>
            )}
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

export default WidgetErrorBoundary;