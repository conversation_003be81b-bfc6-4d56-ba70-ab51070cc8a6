import React, { useCallback, useMemo, useRef, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertTriangle, RefreshCw, Settings } from 'lucide-react';

import { SisenseContextProvider } from './SisenseContextProvider';
import { SisenseErrorBoundary } from './SisenseErrorBoundary';
import { DashboardErrorBoundary } from './DashboardErrorBoundary';
import DashboardTabsContainer from './DashboardTabsContainer';
import { TabsContainer } from './TabsContainer';
import { WidgetGrid } from './WidgetGrid';
import { DashboardSkeleton, LoadingSpinner, RefreshIndicator, EmptyState } from './DashboardLoadingStates';
import { RefreshControls } from './RefreshControls';
import ResponsiveLayout from './ResponsiveLayout';

import { useDashboardManager } from '@/hooks/useDashboardManager';
import type { SisenseErrorInfo } from '@/types/sisense';

export interface ComposeDashboardProps {
  /** Custom className for the dashboard container */
  className?: string;
  /** Default dashboard ID to load on initialization */
  defaultDashboardId?: string;
  /** Whether to show debug information for development */
  showDebugInfo?: boolean;
  /** Custom Sisense URL (overrides environment variable) */
  sisenseUrl?: string;
  /** Custom Sisense token (overrides environment variable) */
  sisenseToken?: string;
  /** Whether to enable automatic refresh */
  enableAutoRefresh?: boolean;
  /** Refresh interval in milliseconds */
  refreshInterval?: number;
  /** Whether to show refresh controls */
  showRefreshControls?: boolean;
  /** Whether to show dashboard descriptions */
  showDescriptions?: boolean;
  /** Custom error handler */
  onError?: (error: SisenseErrorInfo) => void;
  /** Custom loading state handler */
  onLoadingChange?: (isLoading: boolean) => void;
  /** Custom dashboard change handler */
  onDashboardChange?: (dashboardId: string) => void;
}

/**
 * Main ComposeDashboard component - Primary dashboard interface
 * 
 * This component serves as the main entry point for the Sisense Compose SDK dashboard.
 * It integrates the SisenseContextProvider with dashboard configuration, manages
 * component composition, and provides debug mode for development.
 * 
 * Features:
 * - Sisense SDK context integration
 * - Dashboard tab management
 * - Widget rendering with error boundaries
 * - Loading states and error handling
 * - Debug mode for development
 * - Responsive design
 */
const ComposeDashboard: React.FC<ComposeDashboardProps> = ({
  className,
  defaultDashboardId,
  showDebugInfo = process.env.NODE_ENV === 'development',
  sisenseUrl,
  sisenseToken,
  enableAutoRefresh = false,
  refreshInterval = 300000, // 5 minutes
  showRefreshControls = true,
  showDescriptions = true,
  onError,
  onLoadingChange,
  onDashboardChange,
}) => {
  // Refs for maintaining scroll position during refresh
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const activeTabRef = useRef<string | null>(null);
  // Dashboard manager hook for coordinating all dashboard operations
  const {
    dashboards,
    selectedDashboardId,
    selectedDashboard,
    widgets,
    tabs,
    isLoadingMetadata,
    isLoadingWidgets,
    isRefreshing,
    metadataError,
    widgetsError,
    healthStatus,
    configValidation,
    selectDashboard,
    refreshAll,
    retryLastOperation,
    clearErrors,
    reset,
    cacheStats
  } = useDashboardManager({
    autoLoadWidgets: true,
    enableCaching: true,
    enableHealthCheck: true,
    refreshInterval: enableAutoRefresh ? refreshInterval : undefined,
    maxRetries: 3,
    onDashboardChange,
    onError,
    onLoadingChange
  });

  // Effect to select the correct dashboard based on URL parameter
  useEffect(() => {
    if (defaultDashboardId && selectedDashboardId !== defaultDashboardId) {
      // Find the dashboard by ID or slug
      const dashboard = dashboards.find(d => d.id === defaultDashboardId || d.slug === defaultDashboardId);
      if (dashboard) {
        selectDashboard(dashboard.id);
      }
    }
  }, [defaultDashboardId, selectedDashboardId, dashboards, selectDashboard]);

  // Handle dashboard tab change with scroll position reset - optimized with stable reference
  const handleDashboardChange = useCallback((dashboardId: string) => {
    // Reset scroll position when changing dashboards
    scrollPositionRef.current = { x: 0, y: 0 };
    selectDashboard(dashboardId);
  }, [selectDashboard]);

  // Save scroll position before refresh
  const saveScrollPosition = useCallback(() => {
    if (scrollContainerRef.current) {
      scrollPositionRef.current = {
        x: scrollContainerRef.current.scrollLeft,
        y: scrollContainerRef.current.scrollTop,
      };
    }
    // Save active tab
    activeTabRef.current = selectedDashboardId;
  }, [selectedDashboardId]);

  // Restore scroll position after refresh
  const restoreScrollPosition = useCallback(() => {
    if (scrollContainerRef.current && scrollPositionRef.current) {
      // Use requestAnimationFrame to ensure DOM is updated
      requestAnimationFrame(() => {
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollTo(
            scrollPositionRef.current.x,
            scrollPositionRef.current.y
          );
        }
      });
    }
  }, []);

  // Handle refresh button click with scroll position preservation
  const handleRefresh = useCallback(async (dashboardId?: string) => {
    try {
      // Save current scroll position and tab
      saveScrollPosition();
      
      if (dashboardId && dashboardId !== selectedDashboardId) {
        // Switch to the requested dashboard first
        await selectDashboard(dashboardId);
      }
      
      await refreshAll();
      
      // Restore scroll position after refresh completes
      setTimeout(restoreScrollPosition, 100);
    } catch (error) {
      console.error('Failed to refresh dashboard:', error);
    }
  }, [selectedDashboardId, selectDashboard, refreshAll, saveScrollPosition, restoreScrollPosition]);

  // Handle auto-refresh toggle
  const handleAutoRefreshToggle = useCallback((enabled: boolean) => {
    // This would be handled by the dashboard manager
    // For now, we'll just log the change
    console.log('Auto-refresh toggled:', enabled);
  }, []);

  // Handle refresh interval change
  const handleRefreshIntervalChange = useCallback((interval: number) => {
    // This would be handled by the dashboard manager
    // For now, we'll just log the change
    console.log('Refresh interval changed:', interval);
  }, []);

  // Handle retry operation
  const handleRetry = useCallback(async () => {
    try {
      clearErrors();
      await retryLastOperation();
    } catch (error) {
      console.error('Retry operation failed:', error);
    }
  }, [clearErrors, retryLastOperation]);

  // Handle reset operation
  const handleReset = useCallback(() => {
    reset();
  }, [reset]);

  // Memoize dashboard content rendering for performance
  const renderDashboardContent = useCallback((dashboardId: string) => {
    const dashboard = dashboards.find(d => d.id === dashboardId);
    
    if (!dashboard) {
      return (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Dashboard not found: {dashboardId}
          </AlertDescription>
        </Alert>
      );
    }

    // Show loading state for widgets
    if (isLoadingWidgets && dashboardId === selectedDashboardId) {
      return (
        <div className="space-y-4">
          <LoadingSpinner 
            size="md" 
            message="Loading dashboard widgets..." 
            className="py-8"
          />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: dashboard?.widgetCount || 6 }).map((_, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-3/4 mb-2" />
                  <Skeleton className="h-32 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      );
    }

    // Show error state for widgets
    if (widgetsError && dashboardId === selectedDashboardId) {
      return (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to load widgets: {widgetsError}</span>
            <Button variant="outline" size="sm" onClick={handleRetry}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      );
    }

    // Render widgets based on dashboard configuration
    if (dashboardId === selectedDashboardId && widgets.length > 0) {
      // Dashboard has tabber widgets - use TabsContainer
      if (dashboard.hasWidgetsTabber && tabs.length > 0) {
        return (
          <TabsContainer
            widgets={widgets}
            dashboardOid={dashboard.oid || dashboard.id}
            showWidgetIds={showDebugInfo}
          />
        );
      }
      
      // Dashboard without tabs - use WidgetGrid with ResponsiveLayout wrapper
      return (
        <ResponsiveLayout
          enableScrolling={true}
          gridConfig="standard"
          maintainScrollPosition={true}
        >
          <WidgetGrid
            widgets={widgets}
            dashboardOid={dashboard.oid || dashboard.id}
            autoColumns={true}
            gap="md"
            enableScrolling={false} // Let ResponsiveLayout handle scrolling
            responsiveConfig="standard"
            widgetSize="medium"
            touchFriendly={true}
            adaptiveLayout={true}
            className="responsive-widget-grid"
          />
        </ResponsiveLayout>
      );
    }

    // Empty state
    return (
      <EmptyState
        title="No widgets available"
        description={`No widgets found for ${dashboard?.title || 'this dashboard'}`}
        action={showRefreshControls && (
          <Button 
            variant="outline" 
            onClick={() => handleRefresh(dashboardId)}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh Dashboard
          </Button>
        )}
      />
    );
  }, [
    dashboards,
    selectedDashboardId,
    isLoadingWidgets,
    widgetsError,
    widgets,
    tabs,
    showDebugInfo,
    showRefreshControls,
    isRefreshing,
    handleRetry,
    handleRefresh
  ]);

  // Debug information panel
  const debugInfo = useMemo(() => {
    if (!showDebugInfo) return null;

    return (
      <Card className="mb-4 border-dashed border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-orange-800 dark:text-orange-200">
              Debug Information
            </h4>
            <Settings className="h-4 w-4 text-orange-600 dark:text-orange-400" />
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
            <div>
              <span className="font-medium">Selected:</span>
              <br />
              {selectedDashboard?.title || 'None'}
            </div>
            <div>
              <span className="font-medium">Widgets:</span>
              <br />
              {widgets.length} loaded
            </div>
            <div>
              <span className="font-medium">Tabs:</span>
              <br />
              {tabs.length} configured
            </div>
            <div>
              <span className="font-medium">Health:</span>
              <br />
              <span className={`inline-block w-2 h-2 rounded-full mr-1 ${
                healthStatus === 'healthy' ? 'bg-green-500' : 
                healthStatus === 'unhealthy' ? 'bg-red-500' : 'bg-yellow-500'
              }`} />
              {healthStatus}
            </div>
          </div>
          {!configValidation.isValid && (
            <div className="mt-2 p-2 bg-red-100 dark:bg-red-900/20 rounded text-xs">
              <span className="font-medium text-red-800 dark:text-red-200">Config Issues:</span>
              <ul className="mt-1 list-disc list-inside text-red-700 dark:text-red-300">
                {configValidation.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
          <div className="mt-2 text-xs text-orange-700 dark:text-orange-300">
            Cache: {cacheStats.size}/{cacheStats.maxSize} entries
          </div>
        </CardContent>
      </Card>
    );
  }, [
    showDebugInfo,
    selectedDashboard,
    widgets.length,
    tabs.length,
    healthStatus,
    configValidation,
    cacheStats
  ]);

  // Show configuration error if Sisense config is invalid
  if (!configValidation.isValid && configValidation.errors.some(error => 
    error.includes('URL') || error.includes('token')
  )) {
    return (
      <div className={className}>
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p>Sisense configuration error:</p>
              <ul className="list-disc list-inside text-sm">
                {configValidation.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
              <p className="text-xs mt-2">
                Please check your environment variables or component props.
              </p>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Show metadata loading state
  if (isLoadingMetadata) {
    return (
      <div className={className}>
        <DashboardSkeleton 
          tabCount={5}
          widgetCount={6}
          showHeader={true}
        />
      </div>
    );
  }

  // Show metadata error state
  if (metadataError) {
    return (
      <div className={className}>
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <div>
              <p>Failed to load dashboard metadata</p>
              <p className="text-xs mt-1">{metadataError.message}</p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={handleRetry}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Button variant="outline" size="sm" onClick={handleReset}>
                Reset
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Main dashboard render
  return (
    <SisenseErrorBoundary>
      <SisenseContextProvider url={sisenseUrl} token={sisenseToken}>
        <DashboardErrorBoundary
          dashboardId={selectedDashboardId || undefined}
          onRetry={handleRetry}
          onReset={handleReset}
          showDetails={showDebugInfo}
        >
          <div className={className} ref={scrollContainerRef}>
            {debugInfo}
            
            {/* Refresh indicator overlay */}
            <RefreshIndicator 
              isRefreshing={isRefreshing}
              message="Refreshing dashboard data..."
            />
            
            {/* Enhanced refresh controls */}
            {showRefreshControls && (
              <div className="mb-4 flex justify-end">
                <RefreshControls
                  isRefreshing={isRefreshing}
                  onRefresh={handleRefresh}
                  onAutoRefreshToggle={handleAutoRefreshToggle}
                  onIntervalChange={handleRefreshIntervalChange}
                  autoRefreshEnabled={enableAutoRefresh}
                  refreshInterval={refreshInterval}
                  showAdvancedControls={showDebugInfo}
                  showStatus={true}
                  variant="outline"
                  size="sm"
                />
              </div>
            )}
            
            <DashboardTabsContainer
              dashboards={dashboards}
              activeDashboardId={selectedDashboardId || undefined}
              onDashboardChange={handleDashboardChange}
              onRefresh={handleRefresh}
              isRefreshing={isRefreshing}
              showRefreshControls={false} // We're handling refresh controls above
              showDescriptions={showDescriptions}
            >
              {renderDashboardContent}
            </DashboardTabsContainer>
          </div>
        </DashboardErrorBoundary>
      </SisenseContextProvider>
    </SisenseErrorBoundary>
  );
};

export default ComposeDashboard;