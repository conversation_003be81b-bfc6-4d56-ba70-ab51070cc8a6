# DashboardTabsContainer Component

A comprehensive React component that provides a tabbed interface for managing multiple Sisense dashboards with local storage persistence, keyboard navigation, and refresh controls.

## Features

- **Tabbed Interface**: Uses shadcn/ui Tabs components for clean, accessible navigation
- **Local Storage Persistence**: Automatically saves and restores the active dashboard selection
- **Keyboard Navigation**: Full keyboard support with arrow key navigation between tabs
- **Refresh Controls**: Built-in refresh button with loading states
- **Responsive Design**: Adapts to different screen sizes with responsive tab layout
- **Dashboard Metadata**: Displays widget counts, map counts, and tabber indicators
- **Accessibility**: Full ARIA support and keyboard navigation
- **Customizable**: Flexible props for customization and integration

## Usage

### Basic Usage

```tsx
import { DashboardTabsContainer } from '@/components/sisense';
import { IIJA_DASHBOARDS } from '@/config/dashboards';

function MyDashboard() {
  const [activeDashboardId, setActiveDashboardId] = useState(IIJA_DASHBOARDS[0].id);

  return (
    <DashboardTabsContainer
      dashboards={IIJA_DASHBOARDS}
      activeDashboardId={activeDashboardId}
      onDashboardChange={setActiveDashboardId}
    >
      <div>Dashboard content goes here</div>
    </DashboardTabsContainer>
  );
}
```

### Advanced Usage with Hooks

```tsx
import { DashboardTabsContainer } from '@/components/sisense';
import { useDashboardWidgets } from '@/hooks';
import { IIJA_DASHBOARDS } from '@/config/dashboards';

function AdvancedDashboard() {
  const [activeDashboardId, setActiveDashboardId] = useState(IIJA_DASHBOARDS[0].id);
  
  const { widgets, isLoading, error, refetch, isRefetching } = useDashboardWidgets(
    activeDashboardId
  );

  const handleRefresh = (dashboardId: string) => {
    refetch();
  };

  return (
    <DashboardTabsContainer
      dashboards={IIJA_DASHBOARDS}
      activeDashboardId={activeDashboardId}
      onDashboardChange={setActiveDashboardId}
      onRefresh={handleRefresh}
      isRefreshing={isRefetching}
      showRefreshControls={true}
      showDescriptions={true}
    >
      {(dashboardId) => (
        <WidgetGrid widgets={widgets} dashboardOid={dashboardId} />
      )}
    </DashboardTabsContainer>
  );
}
```

## Props

### DashboardTabsContainerProps

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `dashboards` | `DashboardConfig[]` | `IIJA_DASHBOARDS` | Array of dashboard configurations to display as tabs |
| `activeDashboardId` | `string` | - | Currently active dashboard ID |
| `onDashboardChange` | `(dashboardId: string) => void` | - | Callback when dashboard tab changes |
| `onRefresh` | `(dashboardId: string) => void` | - | Callback when refresh button is clicked |
| `isRefreshing` | `boolean` | `false` | Whether refresh is currently in progress |
| `className` | `string` | - | Custom className for the container |
| `showRefreshControls` | `boolean` | `true` | Whether to show refresh controls |
| `showDescriptions` | `boolean` | `true` | Whether to show dashboard descriptions |
| `children` | `React.ReactNode \| ((dashboardId: string) => React.ReactNode)` | - | Children to render within each dashboard tab |

### DashboardConfig Interface

```tsx
interface DashboardConfig {
  id: string;                    // Unique dashboard identifier
  title: string;                 // Dashboard display title
  slug: string;                  // URL-friendly slug
  description: string;           // Dashboard description
  oid?: string;                  // Sisense object ID
  hasWidgetsTabber?: boolean;    // Whether dashboard has tabbed widgets
  widgetCount?: number;          // Number of widgets in dashboard
  mapCount?: number;             // Number of map widgets
}
```

## Local Storage Integration

The component automatically persists user preferences to localStorage:

```tsx
interface DashboardPreferences {
  activeDashboardId: string;                                    // Last selected dashboard
  collapsedTabs: string[];                                      // Collapsed tab states
  widgetSizes: Record<string, { width: number; height: number }>; // Widget size preferences
  lastRefreshTime: number;                                      // Last refresh timestamp
}
```

Storage key: `sisense-dashboard-preferences`

## Keyboard Navigation

- **Arrow Left/Right**: Navigate between dashboard tabs
- **Tab**: Focus navigation through interactive elements
- **Enter/Space**: Activate focused elements
- **Escape**: Close any open dropdowns or modals

## Accessibility Features

- Full ARIA support with proper roles and labels
- Keyboard navigation support
- Screen reader friendly
- Focus management
- High contrast support
- Semantic HTML structure

## Styling

The component uses shadcn/ui components with Tailwind CSS classes:

- **Tabs**: Responsive grid layout that adapts to screen size
- **Cards**: Clean card design for dashboard content
- **Buttons**: Consistent button styling with loading states
- **Typography**: Proper text hierarchy and contrast

### Responsive Breakpoints

- **Mobile**: Single column tab layout
- **Tablet**: 3-column tab grid
- **Desktop**: 5-column tab grid
- **Large Desktop**: 9-column tab grid

## Integration with Other Components

### With WidgetGrid

```tsx
<DashboardTabsContainer>
  {(dashboardId) => (
    <WidgetGrid
      widgets={widgets}
      dashboardOid={dashboardId}
      className="grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
    />
  )}
</DashboardTabsContainer>
```

### With TabsContainer (for nested tabs)

```tsx
<DashboardTabsContainer>
  {(dashboardId) => (
    <TabsContainer
      tabs={tabs}
      widgets={widgets}
      dashboardOid={dashboardId}
    />
  )}
</DashboardTabsContainer>
```

## Error Handling

The component handles various error states:

- **Empty Dashboards**: Shows "No dashboards available" message
- **Invalid Dashboard ID**: Falls back to first available dashboard
- **localStorage Errors**: Gracefully handles storage failures
- **Render Errors**: Provides error boundaries for child components

## Performance Considerations

- **Lazy Rendering**: Only renders active tab content
- **Memoization**: Uses React.memo for performance optimization
- **Local Storage**: Efficient preference persistence
- **Event Handling**: Optimized event listeners with cleanup

## Browser Support

- Modern browsers with ES2015+ support
- localStorage support required for persistence
- CSS Grid support for responsive layout

## Dependencies

- React 18+
- @radix-ui/react-tabs
- shadcn/ui components (Tabs, Card, Button)
- Tailwind CSS
- Lucide React (for icons)

## Examples

See `DashboardTabsExample.tsx` for a complete working example that demonstrates:

- Integration with `useDashboardWidgets` hook
- Loading and error states
- Refresh functionality
- Widget grid rendering
- Responsive design

## Migration Guide

If migrating from a previous dashboard implementation:

1. Replace existing tab logic with `DashboardTabsContainer`
2. Update dashboard configuration to use `DashboardConfig` interface
3. Implement `onDashboardChange` and `onRefresh` callbacks
4. Update child components to use the new render prop pattern
5. Test keyboard navigation and accessibility features

## Troubleshooting

### Common Issues

1. **Tabs not switching**: Check that `onDashboardChange` is properly implemented
2. **Preferences not persisting**: Verify localStorage is available and not blocked
3. **Styling issues**: Ensure Tailwind CSS and shadcn/ui are properly configured
4. **Keyboard navigation not working**: Check that the component has proper focus management

### Debug Mode

Enable debug information by checking the browser's localStorage:

```javascript
// View current preferences
console.log(localStorage.getItem('sisense-dashboard-preferences'));

// Clear preferences (for testing)
localStorage.removeItem('sisense-dashboard-preferences');
```