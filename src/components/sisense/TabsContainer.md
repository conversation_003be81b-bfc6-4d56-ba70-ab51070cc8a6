# TabsContainer Component

The `TabsContainer` component provides nested tab organization for Sisense widgets based on tabber widget configurations. It automatically parses tab definitions from `WidgetsTabber` widgets and organizes other widgets accordingly.

## Features

- **Automatic Tab Parsing**: Extracts tab definitions from tabber widget scripts
- **Widget Organization**: Groups widgets based on `displayWidgetIds` and `hideWidgetIds`
- **Untabbed Widget Handling**: Displays widgets not assigned to any tab in a separate section
- **shadcn/ui Integration**: Uses Tabs, TabsList, TabsTrigger, TabsContent, and Separator components
- **Responsive Design**: Adapts to different screen sizes and tab counts

## Usage

```tsx
import { TabsContainer } from '@/components/sisense';

function DashboardContent({ widgets, dashboardOid }) {
  return (
    <TabsContainer
      widgets={widgets}
      dashboardOid={dashboardOid}
      showWidgetTitles={true}
      showWidgetIds={false}
      widgetHeight={400}
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `widgets` | `ApiWidget[]` | - | Array of widgets to organize and display |
| `dashboardOid` | `string` | - | Dashboard OID for widget rendering |
| `className` | `string` | - | Additional CSS classes |
| `showWidgetTitles` | `boolean` | `true` | Whether to show widget titles |
| `showWidgetIds` | `boolean` | `false` | Whether to show widget IDs (debug mode) |
| `widgetHeight` | `number` | `400` | Default height for widgets |
| `defaultTab` | `string` | - | Default active tab |
| `onTabChange` | `(tabId: string) => void` | - | Callback when tab changes |

## Tab Organization Logic

1. **Tab Parsing**: Scans widgets for `WidgetsTabber` type and parses their scripts
2. **Widget Filtering**: Uses `displayWidgetIds` and `hideWidgetIds` to determine widget visibility
3. **Untabbed Widgets**: Widgets not referenced in any tab are shown in "Other Widgets" tab
4. **Validation**: Ensures referenced widget IDs exist and removes invalid references

## Tab Definition Structure

```typescript
interface TabDefinition {
  title: string;
  displayWidgetIds: string[];
  hideWidgetIds: string[];
  sourceTabberId?: string;
}
```

## Example Widget Script

Tabber widgets contain scripts that define tab organization:

```javascript
// Example tabber widget script
{
  title: "Revenue Analysis",
  displayWidgetIds: ["widget-123", "widget-456"],
  hideWidgetIds: ["widget-789"]
}
```

## Rendering Behavior

- **With Tabs**: Renders tabbed interface with organized widgets
- **Without Tabs**: Renders widgets directly in a grid layout
- **Empty State**: Shows "No organized widgets found" message
- **Mixed Content**: Shows both tabbed and untabbed widgets with separator

## Integration with DashboardTabsContainer

The `TabsContainer` is designed to work within the `DashboardTabsContainer` for nested tab organization:

```tsx
<DashboardTabsContainer>
  {(dashboardId) => (
    <TabsContainer
      widgets={widgetsByDashboard[dashboardId]}
      dashboardOid={dashboardId}
    />
  )}
</DashboardTabsContainer>
```

## Accessibility

- Keyboard navigation support
- ARIA labels for screen readers
- Focus management between tabs
- Semantic HTML structure

## Performance

- Memoized tab parsing and validation
- Efficient widget filtering
- Lazy rendering of tab content
- Optimized re-renders with React state management