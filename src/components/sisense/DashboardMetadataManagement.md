# Dashboard Metadata Management

This document describes the enhanced dashboard metadata management functionality implemented for the Sisense Compose Dashboard application.

## Overview

The dashboard metadata management system provides comprehensive functionality for loading, caching, and managing dashboard configurations with proper error handling and refresh capabilities. It includes health monitoring, configuration validation, and persistent state management.

## Key Components

### 1. Enhanced useDashboardMetadata Hook

The `useDashboardMetadata` hook has been enhanced with the following features:

#### Features
- **Dashboard Loading & Caching**: Loads dashboard metadata with local storage caching
- **Health Monitoring**: Checks Sisense service health status
- **Configuration Validation**: Validates Sisense connection configuration
- **Error Handling**: Comprehensive error handling with retry logic
- **Refresh Functionality**: Manual and automatic refresh capabilities
- **State Persistence**: Saves dashboard selection and preferences to localStorage

#### Usage
```typescript
const {
  dashboards,
  selectedDashboardId,
  isLoading,
  isRefreshing,
  error,
  healthStatus,
  configValidation,
  selectDashboard,
  refreshMetadata,
  checkHealth,
  validateConfig,
  retryLastOperation,
  clearError,
  reset
} = useDashboardMetadata({
  autoSelect: true,
  persistSelection: true,
  enableHealthCheck: true,
  refreshInterval: 300000, // 5 minutes
  maxRetries: 3,
  onDashboardChange: (dashboardId) => console.log('Dashboard changed:', dashboardId),
  onError: (error) => console.error('Error:', error),
  onHealthChange: (isHealthy) => console.log('Health status:', isHealthy)
});
```

### 2. useDashboardManager Hook

A comprehensive hook that combines metadata management with widget loading and caching:

#### Features
- **Unified Management**: Combines dashboard metadata and widget management
- **Advanced Caching**: Multi-level caching with expiration and cleanup
- **Coordinated Loading**: Manages loading states across metadata and widgets
- **Error Coordination**: Unified error handling across all operations
- **Performance Optimization**: Lazy loading and efficient state management

#### Usage
```typescript
const {
  dashboards,
  selectedDashboard,
  widgets,
  tabs,
  isLoadingMetadata,
  isLoadingWidgets,
  isRefreshing,
  metadataError,
  widgetsError,
  healthStatus,
  selectDashboard,
  refreshAll,
  cacheStats,
  clearCache
} = useDashboardManager({
  autoLoadWidgets: true,
  enableCaching: true,
  enableHealthCheck: true,
  onDashboardChange: (id) => console.log('Dashboard changed:', id),
  onError: (error) => console.error('Error:', error)
});
```

### 3. DashboardMetadataService

A service class that handles the business logic for dashboard operations:

#### Features
- **Singleton Pattern**: Single instance for consistent state management
- **Cache Management**: Intelligent caching with expiration and cleanup
- **Local Storage Integration**: Persistent storage for dashboards and preferences
- **Health & Configuration Checks**: System status monitoring
- **Utility Functions**: Helper functions for dashboard operations

#### Usage
```typescript
import { dashboardMetadataService, dashboardUtils } from '@/services/dashboardMetadataService';

// Load dashboards
const { dashboards, error } = await dashboardMetadataService.loadDashboardMetadata();

// Select dashboard
const result = dashboardMetadataService.selectDashboard(dashboardId, dashboards);

// Check system health
const health = await dashboardMetadataService.checkSystemHealth();

// Utility functions
const stats = dashboardUtils.getDashboardStats(dashboards);
const slug = dashboardUtils.createSlug(title);
```

## Implementation Details

### Error Handling

The system implements comprehensive error handling with typed errors:

```typescript
interface SisenseErrorInfo {
  type: SisenseErrorType;
  message: string;
  originalError?: Error;
  retry?: () => void;
}
```

Error types include:
- `AUTHENTICATION`: Authentication failures
- `NETWORK`: Network connectivity issues
- `DASHBOARD_LOAD`: Dashboard loading failures
- `WIDGET_LOAD`: Widget loading failures
- `PARSING`: Data parsing errors
- `UNKNOWN`: Unexpected errors

### Caching Strategy

The caching system uses multiple levels:

1. **Memory Cache**: In-memory caching for active session data
2. **Local Storage Cache**: Persistent caching across sessions
3. **Dashboard Cache**: Specific caching for dashboard metadata and widgets

Cache features:
- Automatic expiration (5 minutes default)
- Manual cache clearing
- Cache statistics and monitoring
- Intelligent cache invalidation

### State Management

The system maintains state across multiple dimensions:

```typescript
interface DashboardMetadataState {
  dashboards: DashboardConfig[];
  selectedDashboardId: string | null;
  isLoading: boolean;
  isRefreshing: boolean;
  error: SisenseErrorInfo | null;
  lastUpdated: number | null;
  lastRefreshTime: number | null;
  healthStatus: 'unknown' | 'healthy' | 'unhealthy';
  configValidation: { isValid: boolean; errors: string[] };
}
```

### Health Monitoring

The system continuously monitors:
- Sisense service health status
- Configuration validity
- Cache performance
- Error rates and types

## Configuration

### Environment Variables

Required environment variables:
```env
VITE_SISENSE_URL=https://your-sisense-instance.com
VITE_SISENSE_TOKEN=your-api-token
```

### Dashboard Configuration

Dashboard configurations are defined in `@/config/dashboards.ts`:

```typescript
export const IIJA_DASHBOARDS: DashboardConfig[] = [
  {
    id: 'dashboard-id',
    title: 'Dashboard Title',
    slug: 'dashboard-slug',
    description: 'Dashboard description',
    oid: 'dashboard-oid',
    hasWidgetsTabber: false,
    widgetCount: 21,
    mapCount: 1
  }
];
```

### Default Configuration

```typescript
export const DEFAULT_DASHBOARD_CONFIG = {
  defaultDashboardId: IIJA_DASHBOARDS[0].id,
  refreshInterval: 300000, // 5 minutes
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  loadingTimeout: 30000 // 30 seconds
};
```

## Usage Examples

### Basic Dashboard Selection

```typescript
import { useDashboardMetadata } from '@/hooks';

function DashboardSelector() {
  const { dashboards, selectedDashboardId, selectDashboard } = useDashboardMetadata();

  return (
    <div>
      {dashboards.map(dashboard => (
        <button
          key={dashboard.id}
          onClick={() => selectDashboard(dashboard.id)}
          className={selectedDashboardId === dashboard.id ? 'active' : ''}
        >
          {dashboard.title}
        </button>
      ))}
    </div>
  );
}
```

### Error Handling

```typescript
import { useDashboardManager } from '@/hooks';

function DashboardWithErrorHandling() {
  const {
    metadataError,
    retryLastOperation,
    clearErrors
  } = useDashboardManager({
    onError: (error) => {
      console.error(`Dashboard error (${error.type}):`, error.message);
    }
  });

  if (metadataError) {
    return (
      <div className="error-container">
        <p>Error: {metadataError.message}</p>
        <button onClick={retryLastOperation}>Retry</button>
        <button onClick={clearErrors}>Dismiss</button>
      </div>
    );
  }

  return <div>Dashboard content...</div>;
}
```

### Health Monitoring

```typescript
import { useDashboardMetadata } from '@/hooks';

function HealthMonitor() {
  const {
    healthStatus,
    configValidation,
    checkHealth
  } = useDashboardMetadata({
    enableHealthCheck: true,
    onHealthChange: (isHealthy) => {
      if (!isHealthy) {
        console.warn('Sisense service is unhealthy');
      }
    }
  });

  return (
    <div>
      <div>Health: {healthStatus}</div>
      <div>Config: {configValidation.isValid ? 'Valid' : 'Invalid'}</div>
      <button onClick={checkHealth}>Check Health</button>
    </div>
  );
}
```

## Requirements Satisfied

This implementation satisfies the following requirements from task 4.3:

✅ **Dashboard loading and caching logic**: Implemented comprehensive caching with memory and localStorage
✅ **Dashboard selection state management**: Full state management with persistence
✅ **Proper error handling for dashboard metadata failures**: Typed errors with retry logic
✅ **Refresh functionality for dashboard data**: Manual and automatic refresh capabilities

The implementation also addresses requirements:
- **1.1**: Dashboard loading and display functionality
- **5.1**: Refresh and interaction controls
- **5.3**: State persistence and refresh functionality

## Testing

The system includes comprehensive error scenarios and edge cases:
- Network failures
- Authentication errors
- Invalid configurations
- Cache expiration
- State persistence
- Health monitoring

## Performance Considerations

- Lazy loading of dashboard data
- Intelligent caching with expiration
- Debounced refresh operations
- Memory leak prevention with proper cleanup
- Optimized re-renders with React.memo and useCallback

## Future Enhancements

Potential future improvements:
- Real-time dashboard updates via WebSocket
- Advanced caching strategies (LRU, etc.)
- Dashboard analytics and usage tracking
- Offline support with service workers
- Dashboard versioning and rollback