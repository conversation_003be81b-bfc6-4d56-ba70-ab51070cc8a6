import React, { useState, useCallback } from 'react';
import { Settings, Download, Upload, RotateCcw, Info } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  useDashboardPreferences, 
  useAppPreferences, 
  useDashboardLayoutPreferences,
  preferenceUtils 
} from '@/hooks/useLocalStorageState';

export interface DashboardPreferencesProps {
  dashboardId: string;
  onClose?: () => void;
  className?: string;
}

export const DashboardPreferences: React.FC<DashboardPreferencesProps> = ({
  dashboardId,
  onClose,
  className
}) => {
  const [dashboardPrefs, setDashboardPrefs] = useDashboardPreferences(dashboardId);
  const [appPrefs, setAppPrefs] = useAppPreferences();
  const [layoutPrefs, setLayoutPrefs] = useDashboardLayoutPreferences(dashboardId);
  const [exportData, setExportData] = useState<string>('');
  const [importData, setImportData] = useState<string>('');
  const [storageInfo, setStorageInfo] = useState(preferenceUtils.getStorageUsage());

  // Handle preference changes
  const updateDashboardPref = useCallback(<K extends keyof typeof dashboardPrefs>(
    key: K, 
    value: typeof dashboardPrefs[K]
  ) => {
    setDashboardPrefs(prev => ({ ...prev, [key]: value }));
  }, [setDashboardPrefs]);

  const updateAppPref = useCallback(<K extends keyof typeof appPrefs>(
    key: K, 
    value: typeof appPrefs[K]
  ) => {
    setAppPrefs(prev => ({ ...prev, [key]: value }));
  }, [setAppPrefs]);

  const updateLayoutPref = useCallback(<K extends keyof typeof layoutPrefs>(
    key: K, 
    value: typeof layoutPrefs[K]
  ) => {
    setLayoutPrefs(prev => ({ ...prev, [key]: value }));
  }, [setLayoutPrefs]);

  // Export preferences
  const handleExport = useCallback(() => {
    const exported = preferenceUtils.exportPreferences();
    setExportData(exported);
    
    // Create download link
    const blob = new Blob([exported], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sisense-preferences-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, []);

  // Import preferences
  const handleImport = useCallback(() => {
    if (importData.trim()) {
      const success = preferenceUtils.importPreferences(importData);
      if (success) {
        alert('Preferences imported successfully! Please refresh the page.');
        setImportData('');
      } else {
        alert('Failed to import preferences. Please check the format.');
      }
    }
  }, [importData]);

  // Reset preferences
  const handleReset = useCallback(() => {
    if (confirm('Are you sure you want to reset all preferences? This cannot be undone.')) {
      preferenceUtils.clearAllPreferences();
      alert('Preferences reset successfully! Please refresh the page.');
    }
  }, []);

  // Refresh storage info
  const refreshStorageInfo = useCallback(() => {
    setStorageInfo(preferenceUtils.getStorageUsage());
  }, []);

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Dashboard Preferences
        </CardTitle>
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            ×
          </Button>
        )}
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="dashboard" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="layout">Layout</TabsTrigger>
            <TabsTrigger value="app">Application</TabsTrigger>
            <TabsTrigger value="data">Data</TabsTrigger>
          </TabsList>

          {/* Dashboard Preferences */}
          <TabsContent value="dashboard" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Auto Refresh</label>
                <Switch
                  checked={dashboardPrefs.autoRefreshEnabled}
                  onCheckedChange={(checked) => updateDashboardPref('autoRefreshEnabled', checked)}
                />
              </div>

              {dashboardPrefs.autoRefreshEnabled && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Refresh Interval: {Math.floor(dashboardPrefs.refreshInterval / 60000)} minutes
                  </label>
                  <Slider
                    value={[dashboardPrefs.refreshInterval / 60000]}
                    onValueChange={([value]) => updateDashboardPref('refreshInterval', value * 60000)}
                    min={1}
                    max={60}
                    step={1}
                    className="w-full"
                  />
                </div>
              )}

              <Separator />

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Show Widget Titles</label>
                <Switch
                  checked={dashboardPrefs.showWidgetTitles}
                  onCheckedChange={(checked) => updateDashboardPref('showWidgetTitles', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Show Widget IDs (Debug)</label>
                <Switch
                  checked={dashboardPrefs.showWidgetIds}
                  onCheckedChange={(checked) => updateDashboardPref('showWidgetIds', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Compact Mode</label>
                <Switch
                  checked={dashboardPrefs.compactMode}
                  onCheckedChange={(checked) => updateDashboardPref('compactMode', checked)}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Theme</label>
                <Select
                  value={dashboardPrefs.theme}
                  onValueChange={(value: 'light' | 'dark' | 'system') => 
                    updateDashboardPref('theme', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          {/* Layout Preferences */}
          <TabsContent value="layout" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Layout Style</label>
                <Select
                  value={layoutPrefs.layout}
                  onValueChange={(value: 'grid' | 'list' | 'masonry') => 
                    updateLayoutPref('layout', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="grid">Grid</SelectItem>
                    <SelectItem value="list">List</SelectItem>
                    <SelectItem value="masonry">Masonry</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Density</label>
                <Select
                  value={layoutPrefs.density}
                  onValueChange={(value: 'compact' | 'comfortable' | 'spacious') => 
                    updateLayoutPref('density', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="compact">Compact</SelectItem>
                    <SelectItem value="comfortable">Comfortable</SelectItem>
                    <SelectItem value="spacious">Spacious</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="space-y-2">
                <label className="text-sm font-medium">Sort By</label>
                <Select
                  value={layoutPrefs.sortBy}
                  onValueChange={(value: 'default' | 'title' | 'type' | 'lastModified') => 
                    updateLayoutPref('sortBy', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default</SelectItem>
                    <SelectItem value="title">Title</SelectItem>
                    <SelectItem value="type">Type</SelectItem>
                    <SelectItem value="lastModified">Last Modified</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Sort Order</label>
                <Select
                  value={layoutPrefs.sortOrder}
                  onValueChange={(value: 'asc' | 'desc') => 
                    updateLayoutPref('sortOrder', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">Ascending</SelectItem>
                    <SelectItem value="desc">Descending</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Hide Error Widgets</label>
                <Switch
                  checked={layoutPrefs.filters.hideErrorWidgets}
                  onCheckedChange={(checked) => 
                    updateLayoutPref('filters', { ...layoutPrefs.filters, hideErrorWidgets: checked })
                  }
                />
              </div>
            </div>
          </TabsContent>

          {/* Application Preferences */}
          <TabsContent value="app" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Debug Mode</label>
                <Switch
                  checked={appPrefs.debugMode}
                  onCheckedChange={(checked) => updateAppPref('debugMode', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Performance Monitoring</label>
                <Switch
                  checked={appPrefs.performanceMonitoring}
                  onCheckedChange={(checked) => updateAppPref('performanceMonitoring', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Animations</label>
                <Switch
                  checked={appPrefs.animations}
                  onCheckedChange={(checked) => updateAppPref('animations', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Reduced Motion</label>
                <Switch
                  checked={appPrefs.reducedMotion}
                  onCheckedChange={(checked) => updateAppPref('reducedMotion', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">High Contrast</label>
                <Switch
                  checked={appPrefs.highContrast}
                  onCheckedChange={(checked) => updateAppPref('highContrast', checked)}
                />
              </div>

              <Separator />

              <div className="space-y-2">
                <label className="text-sm font-medium">Font Size</label>
                <Select
                  value={appPrefs.fontSize}
                  onValueChange={(value: 'small' | 'medium' | 'large') => 
                    updateAppPref('fontSize', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">Small</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="large">Large</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          {/* Data Management */}
          <TabsContent value="data" className="space-y-4">
            <div className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Manage your preference data. Export to backup your settings or import from a backup.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Storage Usage</span>
                  <Button variant="outline" size="sm" onClick={refreshStorageInfo}>
                    Refresh
                  </Button>
                </div>
                <div className="text-sm text-muted-foreground">
                  {storageInfo.totalSizeFormatted} ({storageInfo.keyCount} items)
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Button onClick={handleExport} className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Export Preferences
                </Button>
                
                <div className="space-y-2">
                  <textarea
                    placeholder="Paste exported preferences here to import..."
                    value={importData}
                    onChange={(e) => setImportData(e.target.value)}
                    className="w-full h-24 p-2 border rounded text-xs"
                  />
                  <Button 
                    onClick={handleImport} 
                    disabled={!importData.trim()}
                    className="w-full"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Import Preferences
                  </Button>
                </div>
              </div>

              <Separator />

              <Button 
                onClick={handleReset} 
                variant="destructive" 
                className="w-full"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset All Preferences
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DashboardPreferences;