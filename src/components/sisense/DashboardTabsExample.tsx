import React, { useState } from 'react';
import DashboardTabsContainer from './DashboardTabsContainer';
import { IIJA_DASHBOARDS } from '@/config/dashboards';

/**
 * Example component demonstrating DashboardTabsContainer usage
 * This shows how to integrate the component with state management and callbacks
 */
const DashboardTabsExample: React.FC = () => {
  const [activeDashboardId, setActiveDashboardId] = useState(IIJA_DASHBOARDS[0].id);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleDashboardChange = (dashboardId: string) => {
    console.log('Dashboard changed to:', dashboardId);
    setActiveDashboardId(dashboardId);
  };

  const handleRefresh = async (dashboardId: string) => {
    console.log('Refreshing dashboard:', dashboardId);
    setIsRefreshing(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsRefreshing(false);
    console.log('Dashboard refreshed:', dashboardId);
  };

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold mb-2">IIJA Dashboard Tabs Example</h1>
        <p className="text-muted-foreground">
          This example demonstrates the DashboardTabsContainer component with all features enabled.
        </p>
      </div>

      <DashboardTabsContainer
        dashboards={IIJA_DASHBOARDS}
        activeDashboardId={activeDashboardId}
        onDashboardChange={handleDashboardChange}
        onRefresh={handleRefresh}
        isRefreshing={isRefreshing}
        showRefreshControls={true}
        showDescriptions={true}
        className="w-full"
      >
        {(dashboardId) => (
          <div className="space-y-4">
            <div className="bg-muted/50 rounded-lg p-4">
              <h3 className="font-semibold mb-2">Dashboard Content Area</h3>
              <p className="text-sm text-muted-foreground">
                This is where the dashboard widgets would be rendered for dashboard: {dashboardId}
              </p>
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Placeholder widget cards */}
                {Array.from({ length: 6 }, (_, i) => (
                  <div
                    key={i}
                    className="bg-background border rounded-lg p-4 h-32 flex items-center justify-center"
                  >
                    <span className="text-sm text-muted-foreground">
                      Widget {i + 1}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </DashboardTabsContainer>

      {/* Debug Information */}
      <div className="mt-8 p-4 bg-muted/30 rounded-lg">
        <h3 className="font-semibold mb-2">Debug Information</h3>
        <div className="text-sm space-y-1">
          <p><strong>Active Dashboard:</strong> {activeDashboardId}</p>
          <p><strong>Is Refreshing:</strong> {isRefreshing ? 'Yes' : 'No'}</p>
          <p><strong>Total Dashboards:</strong> {IIJA_DASHBOARDS.length}</p>
        </div>
      </div>
    </div>
  );
};

export default DashboardTabsExample;