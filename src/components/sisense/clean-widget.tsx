/**
 * CleanWidget Component
 * Wrapper around Sisense WidgetById with consistent styling and features
 */

import { WidgetById } from '@sisense/sdk-ui';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ExternalLink, Info } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import sisenseConfig from '@/config/sisense';
import type { Filter } from '@sisense/sdk-data';

interface CleanWidgetProps {
  dashboardOid: string;
  widgetOid: string;
  filters?: Filter[];
  filtersMergeStrategy?: 'widgetFirst' | 'codeFirst';
  showWidgetId?: boolean;
  className?: string;
}

export default function CleanWidget({
  dashboardOid,
  widgetOid,
  filters = [],
  filtersMergeStrategy = 'codeFirst',
  showWidgetId = false,
  className = ''
}: CleanWidgetProps) {
  const sisenseUrl = `${sisenseConfig.url}/app/main#/dashboards/${dashboardOid}?widget=${widgetOid}`;

  return (
    <Card className={`relative ${className}`}>
      {/* Widget Info Dropdown */}
      <div className="absolute top-2 right-2 z-10">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Info className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <a
                href={sisenseUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2"
              >
                <ExternalLink className="h-4 w-4" />
                Open in Sisense
              </a>
            </DropdownMenuItem>
            {showWidgetId && (
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(widgetOid)}
              >
                Copy Widget ID
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Sisense Widget */}
      <div className="p-4">
        <WidgetById
          dashboardOid={dashboardOid}
          widgetOid={widgetOid}
          filters={filters}
          filtersMergeStrategy={filtersMergeStrategy}
        />
      </div>
    </Card>
  );
}