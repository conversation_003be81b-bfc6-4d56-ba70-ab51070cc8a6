import { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert<PERSON>riangle, RefreshCw } from 'lucide-react';
import { errorLoggingService } from '@/services/errorLoggingService';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Error boundary specifically for Sisense context and SDK-related errors
 * Provides user-friendly error messages and recovery options
 */
export class SisenseErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Sisense Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });

    // Log error with error logging service
    errorLoggingService.logSisenseError(error, {
      customData: {
        boundaryType: 'sisense',
        isAuthError: this.isAuthenticationError(),
        isNetworkError: this.isNetworkError(),
      }
    });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <CardTitle className="text-red-900 dark:text-red-100">
                Dashboard Error
              </CardTitle>
              <CardDescription>
                {this.isAuthenticationError() 
                  ? 'Authentication failed. Please check your Sisense credentials.'
                  : this.isNetworkError()
                  ? 'Unable to connect to Sisense. Please check your network connection.'
                  : 'An error occurred while loading the dashboard.'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="text-sm">
                  <summary className="cursor-pointer font-medium text-gray-700 dark:text-gray-300">
                    Error Details
                  </summary>
                  <pre className="mt-2 whitespace-pre-wrap break-words text-xs text-red-600 dark:text-red-400">
                    {this.state.error.message}
                  </pre>
                </details>
              )}
              <div className="flex flex-col gap-2">
                <Button onClick={this.handleRetry} className="w-full">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => window.location.reload()} 
                  className="w-full"
                >
                  Reload Page
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }

  private isAuthenticationError(): boolean {
    const errorMessage = this.state.error?.message?.toLowerCase() || '';
    return errorMessage.includes('auth') || 
           errorMessage.includes('token') || 
           errorMessage.includes('unauthorized') ||
           errorMessage.includes('403') ||
           errorMessage.includes('401');
  }

  private isNetworkError(): boolean {
    const errorMessage = this.state.error?.message?.toLowerCase() || '';
    return errorMessage.includes('network') || 
           errorMessage.includes('fetch') || 
           errorMessage.includes('connection') ||
           errorMessage.includes('timeout');
  }
}

export default SisenseErrorBoundary;