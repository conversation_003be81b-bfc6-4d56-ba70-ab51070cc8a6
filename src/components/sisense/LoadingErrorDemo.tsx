import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { 
  DashboardSkeleton, 
  WidgetSkeleton, 
  TabsSkeleton, 
  LoadingSpinner, 
  RefreshIndicator, 
  EmptyState,
  LoadingState,
  ErrorState,
  ConnectionStatus
} from './DashboardLoadingStates';
import { DashboardErrorBoundary } from './DashboardErrorBoundary';
import { WidgetErrorBoundary } from './WidgetErrorBoundary';
import { SisenseErrorBoundary } from './SisenseErrorBoundary';
import { RefreshControls } from './RefreshControls';
import { Database } from 'lucide-react';

/**
 * Demo component to showcase all loading states and error handling
 * This component is for testing and demonstration purposes only
 */
export const LoadingErrorDemo: React.FC = () => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected' | 'error'>('connected');
  const [showErrorBoundary, setShowErrorBoundary] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsRefreshing(false);
  };

  const simulateProgress = () => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const ErrorComponent = () => {
    if (showErrorBoundary) {
      throw new Error('Demo error for testing error boundary');
    }
    return <div>No error</div>;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold">Loading States & Error Handling Demo</h1>
        <p className="text-muted-foreground">
          Comprehensive showcase of all loading states and error handling components
        </p>
      </div>

      <Tabs defaultValue="loading" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="loading">Loading States</TabsTrigger>
          <TabsTrigger value="errors">Error States</TabsTrigger>
          <TabsTrigger value="boundaries">Error Boundaries</TabsTrigger>
          <TabsTrigger value="controls">Controls</TabsTrigger>
        </TabsList>

        <TabsContent value="loading" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Dashboard Skeleton */}
            <Card>
              <CardHeader>
                <CardTitle>Dashboard Skeleton</CardTitle>
              </CardHeader>
              <CardContent>
                <DashboardSkeleton tabCount={3} widgetCount={4} />
              </CardContent>
            </Card>

            {/* Widget Skeleton */}
            <Card>
              <CardHeader>
                <CardTitle>Widget Skeleton</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <WidgetSkeleton showTitle={true} />
                <WidgetSkeleton showTitle={false} height="h-32" />
              </CardContent>
            </Card>

            {/* Loading Spinner */}
            <Card>
              <CardHeader>
                <CardTitle>Loading Spinners</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <LoadingSpinner size="sm" message="Small spinner" />
                <LoadingSpinner size="md" message="Medium spinner" />
                <LoadingSpinner size="lg" message="Large spinner" />
              </CardContent>
            </Card>

            {/* Enhanced Loading States */}
            <Card>
              <CardHeader>
                <CardTitle>Enhanced Loading States</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <LoadingState type="dashboard" />
                <LoadingState type="widgets" />
                <LoadingState type="data" />
                <LoadingState 
                  type="connection" 
                  progress={progress}
                  showProgress={true}
                  message="Loading with progress..."
                />
                <Button onClick={simulateProgress} size="sm">
                  Simulate Progress
                </Button>
              </CardContent>
            </Card>

            {/* Tabs Skeleton */}
            <Card>
              <CardHeader>
                <CardTitle>Tabs Skeleton</CardTitle>
              </CardHeader>
              <CardContent>
                <TabsSkeleton tabCount={4} widgetCount={3} />
              </CardContent>
            </Card>

            {/* Empty State */}
            <Card>
              <CardHeader>
                <CardTitle>Empty State</CardTitle>
              </CardHeader>
              <CardContent>
                <EmptyState
                  title="No widgets found"
                  description="This dashboard doesn't have any widgets to display."
                  icon={<Database className="h-6 w-6" />}
                  action={
                    <Button variant="outline" size="sm">
                      Refresh Dashboard
                    </Button>
                  }
                />
              </CardContent>
            </Card>
          </div>

          {/* Refresh Indicator */}
          <RefreshIndicator 
            isRefreshing={isRefreshing}
            message="Demo refresh in progress..."
          />
        </TabsContent>

        <TabsContent value="errors" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Network Error */}
            <Card>
              <CardHeader>
                <CardTitle>Network Error</CardTitle>
              </CardHeader>
              <CardContent>
                <ErrorState
                  type="network"
                  onRetry={() => console.log('Retry network')}
                  onReset={() => console.log('Reset network')}
                />
              </CardContent>
            </Card>

            {/* Auth Error */}
            <Card>
              <CardHeader>
                <CardTitle>Authentication Error</CardTitle>
              </CardHeader>
              <CardContent>
                <ErrorState
                  type="auth"
                  onRetry={() => console.log('Retry auth')}
                  showDetails={true}
                  details="Token expired at 2024-01-01T12:00:00Z"
                />
              </CardContent>
            </Card>

            {/* Data Error */}
            <Card>
              <CardHeader>
                <CardTitle>Data Error</CardTitle>
              </CardHeader>
              <CardContent>
                <ErrorState
                  type="data"
                  title="Dashboard Data Unavailable"
                  message="The requested dashboard data could not be loaded."
                  onRetry={() => console.log('Retry data')}
                />
              </CardContent>
            </Card>

            {/* Permission Error */}
            <Card>
              <CardHeader>
                <CardTitle>Permission Error</CardTitle>
              </CardHeader>
              <CardContent>
                <ErrorState
                  type="permission"
                  onReset={() => console.log('Reset permission')}
                />
              </CardContent>
            </Card>
          </div>

          {/* Connection Status */}
          <Card>
            <CardHeader>
              <CardTitle>Connection Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <ConnectionStatus 
                  status="connected" 
                  serverUrl="https://demo.sisense.com"
                />
                <ConnectionStatus 
                  status="connecting" 
                  serverUrl="https://demo.sisense.com"
                />
                <ConnectionStatus 
                  status="disconnected" 
                  serverUrl="https://demo.sisense.com"
                />
                <ConnectionStatus 
                  status="error" 
                  serverUrl="https://demo.sisense.com"
                  lastAttempt={Date.now() - 30000}
                />
              </div>
              <div className="flex space-x-2">
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => setConnectionStatus('connecting')}
                >
                  Connecting
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => setConnectionStatus('connected')}
                >
                  Connected
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => setConnectionStatus('error')}
                >
                  Error
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="boundaries" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Sisense Error Boundary */}
            <Card>
              <CardHeader>
                <CardTitle>Sisense Error Boundary</CardTitle>
              </CardHeader>
              <CardContent>
                <SisenseErrorBoundary>
                  <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded">
                    <p className="text-green-800 dark:text-green-200">
                      Sisense context is working properly
                    </p>
                  </div>
                </SisenseErrorBoundary>
              </CardContent>
            </Card>

            {/* Dashboard Error Boundary */}
            <Card>
              <CardHeader>
                <CardTitle>Dashboard Error Boundary</CardTitle>
              </CardHeader>
              <CardContent>
                <DashboardErrorBoundary
                  dashboardId="demo-dashboard"
                  showDetails={true}
                  onRetry={() => console.log('Dashboard retry')}
                  onReset={() => console.log('Dashboard reset')}
                >
                  <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded">
                    <p className="text-blue-800 dark:text-blue-200">
                      Dashboard is loading correctly
                    </p>
                  </div>
                </DashboardErrorBoundary>
              </CardContent>
            </Card>

            {/* Widget Error Boundary */}
            <Card>
              <CardHeader>
                <CardTitle>Widget Error Boundary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <WidgetErrorBoundary
                  widgetId="demo-widget-1"
                  widgetTitle="Demo Widget"
                  compact={false}
                  showDetails={true}
                  onRetry={() => console.log('Widget retry')}
                >
                  <div className="p-4 bg-purple-50 dark:bg-purple-950/20 rounded">
                    <p className="text-purple-800 dark:text-purple-200">
                      Widget is rendering correctly
                    </p>
                  </div>
                </WidgetErrorBoundary>

                <WidgetErrorBoundary
                  widgetId="demo-widget-2"
                  widgetTitle="Compact Widget"
                  compact={true}
                  onRetry={() => console.log('Compact widget retry')}
                >
                  <div className="p-2 bg-orange-50 dark:bg-orange-950/20 rounded">
                    <p className="text-orange-800 dark:text-orange-200 text-sm">
                      Compact widget OK
                    </p>
                  </div>
                </WidgetErrorBoundary>
              </CardContent>
            </Card>

            {/* Error Boundary Test */}
            <Card>
              <CardHeader>
                <CardTitle>Test Error Boundary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex space-x-2">
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => setShowErrorBoundary(true)}
                  >
                    Trigger Error
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setShowErrorBoundary(false)}
                  >
                    Reset
                  </Button>
                </div>
                <SisenseErrorBoundary>
                  <ErrorComponent />
                </SisenseErrorBoundary>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="controls" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Refresh Controls */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Refresh Controls</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <RefreshControls
                  isRefreshing={isRefreshing}
                  onRefresh={handleRefresh}
                  showStatus={true}
                  lastRefreshTime={Date.now() - 120000}
                />
              </CardContent>
            </Card>

            {/* Advanced Refresh Controls */}
            <Card>
              <CardHeader>
                <CardTitle>Advanced Refresh Controls</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <RefreshControls
                  isRefreshing={isRefreshing}
                  onRefresh={handleRefresh}
                  onAutoRefreshToggle={(enabled) => console.log('Auto refresh:', enabled)}
                  onIntervalChange={(interval) => console.log('Interval:', interval)}
                  autoRefreshEnabled={false}
                  refreshInterval={300000}
                  showAdvancedControls={true}
                  showStatus={true}
                  lastRefreshTime={Date.now() - 300000}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LoadingErrorDemo;