/**
 * Performance-optimized WidgetGrid component with advanced memoization and lazy loading
 */

import React, { useMemo, useCallback, useEffect } from 'react';
import { ComposeWidgetById } from './ComposeWidgetById';
import { Card, CardContent } from '@/components/ui/card';
import type { ApiWidget } from '@/types/sisense';
import { cn, filterWidgets, isValidWidget } from '@/lib/utils';
import { 
  useOptimizedIntersectionObserver, 
  useWidgetPerformance,
  PERFORMANCE_CONFIG 
} from '@/lib/performanceOptimizations';

export interface OptimizedWidgetGridProps {
  widgets: ApiWidget[];
  dashboardOid: string;
  className?: string;
  columns?: number;
  showWidgetTitles?: boolean;
  showWidgetIds?: boolean;
  widgetHeight?: number | 'auto';
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  enableScrolling?: boolean;
  maxHeight?: string;
  autoColumns?: boolean;
  // Performance optimization props
  enableLazyLoading?: boolean;
  chunkSize?: number;
  preloadDistance?: number;
  enableVirtualization?: boolean;
}

// Memoized widget item with performance optimizations
interface OptimizedWidgetItemProps {
  widget: ApiWidget;
  widgetOid: string;
  dashboardOid: string;
  showWidgetTitles: boolean;
  showWidgetIds: boolean;
  widgetHeight: number | 'auto';
  onError: (widgetOid: string, error: Error) => void;
  onLoad: (widgetOid: string) => void;
  enableLazyLoading: boolean;
  index: number;
}

const OptimizedWidgetItem = React.memo<OptimizedWidgetItemProps>(({
  widget,
  widgetOid,
  dashboardOid,
  showWidgetTitles,
  showWidgetIds,
  widgetHeight,
  onError,
  onLoad,
  enableLazyLoading
}) => {
  const { startRender, endRender } = useWidgetPerformance(widgetOid);
  
  // Use intersection observer for lazy loading
  const { ref: containerRef, isIntersecting } = useOptimizedIntersectionObserver({
    rootMargin: PERFORMANCE_CONFIG.INTERSECTION_OBSERVER.ROOT_MARGIN,
    threshold: PERFORMANCE_CONFIG.INTERSECTION_OBSERVER.THRESHOLD,
    freezeOnceVisible: PERFORMANCE_CONFIG.INTERSECTION_OBSERVER.FREEZE_ON_VISIBLE,
  });

  // Memoize error and load handlers to prevent unnecessary re-renders
  const handleError = useCallback((error: Error) => {
    onError(widgetOid, error);
  }, [widgetOid, onError]);

  const handleLoad = useCallback(() => {
    onLoad(widgetOid);
  }, [widgetOid, onLoad]);

  // Performance monitoring
  useEffect(() => {
    startRender();
    return endRender;
  });

  // Render placeholder if lazy loading is enabled and widget is not visible
  if (enableLazyLoading && !isIntersecting) {
    return (
      <Card
        ref={containerRef}
        className={cn(
          "min-h-0 flex flex-col",
          widgetHeight === 'auto' ? 'h-auto' : 'h-full',
          "border border-border/30 bg-muted/20"
        )}
      >
        <CardContent className="flex-1 p-2 sm:p-3 md:p-4">
          <div className="space-y-3 animate-pulse">
            {showWidgetTitles && widget.title && (
              <div className="h-5 bg-muted rounded w-3/4" />
            )}
            <div 
              className="bg-muted rounded"
              style={{ 
                height: widgetHeight === 'auto' ? '300px' : `${widgetHeight}px` 
              }}
            />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render actual widget
  return (
    <Card
      ref={containerRef}
      className={cn(
        "min-h-0 flex flex-col",
        widgetHeight === 'auto' ? 'h-auto' : 'h-full',
        "transition-all duration-200 ease-in-out",
        "hover:shadow-md hover:scale-[1.02]",
        "border border-border/50 hover:border-border",
        "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
      )}
    >
      <CardContent className={cn(
        "flex-1 p-2 sm:p-3 md:p-4",
        widgetHeight === 'auto' ? 'h-auto' : 'h-full'
      )}>
        <ComposeWidgetById
          widgetOid={widgetOid}
          dashboardOid={dashboardOid}
          title={widget.title || undefined}
          showTitle={showWidgetTitles}
          showWidgetId={showWidgetIds}
          height={widgetHeight === 'auto' ? undefined : widgetHeight}
          className="h-full"
          onError={handleError}
          onLoad={handleLoad}
        />
      </CardContent>
    </Card>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for optimal re-rendering
  return (
    prevProps.widgetOid === nextProps.widgetOid &&
    prevProps.dashboardOid === nextProps.dashboardOid &&
    prevProps.widget.title === nextProps.widget.title &&
    prevProps.showWidgetTitles === nextProps.showWidgetTitles &&
    prevProps.showWidgetIds === nextProps.showWidgetIds &&
    prevProps.widgetHeight === nextProps.widgetHeight &&
    prevProps.enableLazyLoading === nextProps.enableLazyLoading &&
    prevProps.index === nextProps.index
  );
});

OptimizedWidgetItem.displayName = 'OptimizedWidgetItem';

// Main optimized widget grid component
export const OptimizedWidgetGrid: React.FC<OptimizedWidgetGridProps> = React.memo(({
  widgets,
  dashboardOid,
  className,
  columns,
  showWidgetTitles = true,
  showWidgetIds = false,
  widgetHeight = 400,
  gap = 'md',
  enableScrolling = false,
  maxHeight,
  autoColumns = true,
  enableLazyLoading = true,
  chunkSize = PERFORMANCE_CONFIG.LAZY_LOADING.CHUNK_SIZE,

  enableVirtualization = false
}) => {
  // Memoize filtered widgets to prevent unnecessary re-filtering
  const filteredWidgets = useMemo(() => {
    return filterWidgets(widgets, {
      excludeBlox: true,
      excludeTabbers: true,
      customFilter: isValidWidget
    });
  }, [widgets]);

  // Memoize grid configuration
  const gridConfig = useMemo(() => {
    let columnClasses: string;
    
    if (columns) {
      const maxCols = Math.min(columns, 6);
      columnClasses = [
        'grid-cols-1',
        maxCols >= 2 ? 'sm:grid-cols-2' : '',
        maxCols >= 3 ? 'md:grid-cols-3' : '',
        maxCols >= 4 ? 'lg:grid-cols-4' : '',
        maxCols >= 5 ? 'xl:grid-cols-5' : '',
        maxCols >= 6 ? '2xl:grid-cols-6' : ''
      ].filter(Boolean).join(' ');
    } else if (autoColumns && filteredWidgets.length > 0) {
      if (filteredWidgets.length === 1) {
        columnClasses = 'grid-cols-1';
      } else if (filteredWidgets.length === 2) {
        columnClasses = 'grid-cols-1 md:grid-cols-2';
      } else if (filteredWidgets.length === 3) {
        columnClasses = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
      } else if (filteredWidgets.length === 4) {
        columnClasses = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4';
      } else {
        columnClasses = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
      }
    } else {
      columnClasses = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
    }
    
    const gapClasses = {
      sm: 'gap-2 sm:gap-3',
      md: 'gap-3 sm:gap-4',
      lg: 'gap-4 sm:gap-6',
      xl: 'gap-6 sm:gap-8'
    };

    return {
      columns: columnClasses,
      gap: gapClasses[gap],
    };
  }, [columns, autoColumns, filteredWidgets.length, gap]);

  // Memoize stable callback references
  const handleWidgetError = useCallback((widgetOid: string, error: Error) => {
    console.error(`Widget ${widgetOid} failed to load:`, error);
  }, []);

  const handleWidgetLoad = useCallback((widgetOid: string) => {
    console.debug(`Widget ${widgetOid} loaded successfully`);
  }, []);

  // Chunked rendering for better performance with large widget lists
  const chunkedWidgets = useMemo(() => {
    if (!enableVirtualization || filteredWidgets.length <= chunkSize) {
      return [filteredWidgets];
    }

    const chunks: ApiWidget[][] = [];
    for (let i = 0; i < filteredWidgets.length; i += chunkSize) {
      chunks.push(filteredWidgets.slice(i, i + chunkSize));
    }
    return chunks;
  }, [filteredWidgets, chunkSize, enableVirtualization]);

  if (!filteredWidgets || filteredWidgets.length === 0) {
    return (
      <div className={cn(
        "flex items-center justify-center p-8",
        enableScrolling && maxHeight && `max-h-[${maxHeight}]`,
        className
      )}>
        <Card className="max-w-md">
          <CardContent className="p-8 text-center">
            <div className="text-muted-foreground">
              <div className="text-lg font-medium mb-2">No widgets available</div>
              <div className="text-sm">
                {widgets.length > 0 
                  ? `${widgets.length} widgets were filtered out (excluded or invalid)`
                  : "This dashboard doesn't have any widgets to display."
                }
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const containerClasses = cn(
    "grid",
    gridConfig.columns,
    gridConfig.gap,
    widgetHeight === 'auto' ? 'auto-rows-max' : 'auto-rows-fr',
    "items-start",
    enableScrolling && "overflow-auto",
    enableScrolling && maxHeight && `max-h-[${maxHeight}]`,
    enableScrolling && "will-change-scroll overscroll-contain scroll-smooth",
    "p-1 sm:p-2",
    className
  );

  // Render widgets with chunking if virtualization is enabled
  if (enableVirtualization && chunkedWidgets.length > 1) {
    return (
      <div className={containerClasses}>
        {chunkedWidgets.map((chunk, chunkIndex) => (
          <React.Fragment key={`chunk-${chunkIndex}`}>
            {chunk.map((widget, widgetIndex) => {
              const widgetOid = widget.oid || widget.id;
              const globalIndex = chunkIndex * chunkSize + widgetIndex;
              
              if (!widgetOid) {
                console.warn('Widget missing OID/ID:', widget);
                return null;
              }

              return (
                <OptimizedWidgetItem
                  key={widgetOid}
                  widget={widget}
                  widgetOid={widgetOid}
                  dashboardOid={dashboardOid}
                  showWidgetTitles={showWidgetTitles}
                  showWidgetIds={showWidgetIds}
                  widgetHeight={widgetHeight}
                  onError={handleWidgetError}
                  onLoad={handleWidgetLoad}
                  enableLazyLoading={enableLazyLoading}
                  index={globalIndex}
                />
              );
            })}
          </React.Fragment>
        ))}
      </div>
    );
  }

  // Standard rendering
  return (
    <div className={containerClasses}>
      {filteredWidgets.map((widget, index) => {
        const widgetOid = widget.oid || widget.id;
        
        if (!widgetOid) {
          console.warn('Widget missing OID/ID:', widget);
          return null;
        }

        return (
          <OptimizedWidgetItem
            key={widgetOid}
            widget={widget}
            widgetOid={widgetOid}
            dashboardOid={dashboardOid}
            showWidgetTitles={showWidgetTitles}
            showWidgetIds={showWidgetIds}
            widgetHeight={widgetHeight}
            onError={handleWidgetError}
            onLoad={handleWidgetLoad}
            enableLazyLoading={enableLazyLoading}
            index={index}
          />
        );
      })}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison for the grid component
  return (
    prevProps.dashboardOid === nextProps.dashboardOid &&
    prevProps.widgets.length === nextProps.widgets.length &&
    prevProps.columns === nextProps.columns &&
    prevProps.showWidgetTitles === nextProps.showWidgetTitles &&
    prevProps.showWidgetIds === nextProps.showWidgetIds &&
    prevProps.widgetHeight === nextProps.widgetHeight &&
    prevProps.gap === nextProps.gap &&
    prevProps.enableScrolling === nextProps.enableScrolling &&
    prevProps.maxHeight === nextProps.maxHeight &&
    prevProps.autoColumns === nextProps.autoColumns &&
    prevProps.enableLazyLoading === nextProps.enableLazyLoading &&
    // Deep compare widgets array by checking OIDs
    prevProps.widgets.every((widget, index) => {
      const nextWidget = nextProps.widgets[index];
      return nextWidget && (widget.oid || widget.id) === (nextWidget.oid || nextWidget.id);
    })
  );
});

OptimizedWidgetGrid.displayName = 'OptimizedWidgetGrid';

export default OptimizedWidgetGrid;