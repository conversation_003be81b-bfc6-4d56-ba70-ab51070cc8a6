import { <PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import ThemeToggle from "@/components/theme/ThemeToggle"
import ProfileDropdown from "@/components/layout/ProfileDropdown"

export function Header({ onMenuClick }: { onMenuClick?: () => void }) {
  return (
    <header className="sticky top-0 z-40 border-b bg-background/80 backdrop-blur-sm">
      <div className="flex h-14 items-center gap-4 px-6">
        {/* Mobile menu button */}
        <Button
          variant="ghost"
          size="sm"
          className="md:hidden"
          onClick={onMenuClick}
        >
          <Menu className="size-4" />
        </Button>

        {/* Search */}
        <div className="flex flex-1 items-center">
          <div className="relative max-w-md flex-1">
            <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
            <input
              type="search"
              placeholder="Search dashboards..."
              className="w-full rounded-md border bg-background pl-9 pr-3 py-2 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Bell className="size-4" />
          </Button>
          <ThemeToggle />
          <ProfileDropdown />
        </div>
      </div>
    </header>
  )
}

export default Header
