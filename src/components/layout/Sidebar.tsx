import { Home, LineChart, Users, Settings, Calendar, ChevronDown, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useLocation, useNavigate } from "react-router-dom"
import { useState } from "react"
import type { PropsWithChildren, ComponentType } from "react"

export type NavItem = {
  label: string
  href?: string
  icon?: ComponentType<{ className?: string }>
  children?: NavItem[]
}

const defaultItems: NavItem[] = [
  { label: "Home", href: "#", icon: Home },
  { label: "Analytics", href: "#", icon: LineChart },
  { label: "Users", href: "#", icon: Users },
  { label: "Calendar", href: "#", icon: Calendar },
  { label: "Settings", href: "#", icon: Settings },
]

// Component for rendering individual nav items (supports nesting)
function NavItemComponent({ 
  item, 
  location, 
  navigate, 
  onClose 
}: { 
  item: NavItem
  location: any
  navigate: any
  onClose?: () => void
}) {
  const [isExpanded, setIsExpanded] = useState(true) // Default expanded for dashboards
  const Icon = item.icon
  
  // Check if this item or any of its children are active
  const isActive = item.href ? (
    location.pathname === item.href || 
    (item.href !== '/' && location.pathname.startsWith(item.href))
  ) : false
  
  const hasActiveChild = item.children?.some(child => 
    child.href && (location.pathname === child.href || 
    (child.href !== '/' && location.pathname.startsWith(child.href)))
  )
  
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    if (item.href) {
      navigate(item.href)
      onClose?.()
    } else if (item.children) {
      setIsExpanded(!isExpanded)
    }
  }
  
  return (
    <li>
      <button
        onClick={handleClick}
        data-active={isActive || hasActiveChild}
        className="group flex items-center gap-2 rounded-md px-3 py-2 text-sm transition-colors hover:bg-sidebar-accent/70 data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground w-full text-left"
        aria-current={isActive ? "page" : undefined}
      >
        {Icon ? <Icon className="size-4 text-muted-foreground group-data-[active=true]:text-inherit" /> : null}
        <span className="flex-1">{item.label}</span>
        {item.children && (
          isExpanded ? 
            <ChevronDown className="size-4 text-muted-foreground" /> : 
            <ChevronRight className="size-4 text-muted-foreground" />
        )}
      </button>
      
      {/* Render children if expanded */}
      {item.children && isExpanded && (
        <ul className="ml-6 mt-1 space-y-0.5 border-l border-sidebar-border/50 pl-3">
          {item.children.map((child) => {
            const ChildIcon = child.icon
            const isChildActive = child.href ? (
              location.pathname === child.href || 
              (child.href !== '/' && location.pathname.startsWith(child.href))
            ) : false
            
            const handleChildClick = (e: React.MouseEvent) => {
              e.preventDefault()
              if (child.href) {
                navigate(child.href)
                onClose?.()
              }
            }
            
            return (
              <li key={child.label}>
                <button
                  onClick={handleChildClick}
                  data-active={isChildActive}
                  className="group flex items-center gap-2 rounded-md px-3 py-1.5 text-sm transition-colors hover:bg-sidebar-accent/70 data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground w-full text-left"
                  aria-current={isChildActive ? "page" : undefined}
                >
                  {ChildIcon ? <ChildIcon className="size-3.5 text-muted-foreground group-data-[active=true]:text-inherit" /> : null}
                  <span className="text-xs">{child.label}</span>
                </button>
              </li>
            )
          })}
        </ul>
      )}
    </li>
  )
}

export function Sidebar(
  props: PropsWithChildren<{
    items?: NavItem[]
    open?: boolean
    onClose?: () => void
  }>,
) {
  const { items = defaultItems, open = false, onClose } = props
  const location = useLocation()
  const navigate = useNavigate()

  return (
    <>
      {/* Backdrop for mobile */}
      <div
        className={`fixed inset-0 z-40 bg-black/30 md:hidden ${open ? "block" : "hidden"}`}
        onClick={onClose}
        aria-hidden
      />

      <aside
        className={
          `fixed inset-y-0 left-0 z-50 w-64 transform border-r bg-sidebar text-sidebar-foreground border-sidebar-border transition-transform duration-200 md:relative md:translate-x-0 ` +
          (open ? "translate-x-0" : "-translate-x-full md:translate-x-0")
        }
        aria-label="Sidebar"
      >
        <div className="flex h-14 items-center gap-2 border-b border-sidebar-border px-4">
          <div className="size-6 rounded-md bg-primary" />
          <span className="font-semibold tracking-tight">Pro Dashboard</span>
          <div className="ml-auto md:hidden">
            <Button variant="ghost" size="sm" onClick={onClose}>Close</Button>
          </div>
        </div>

        <nav className="px-2 py-3">
          <ul className="space-y-0.5">
            {items.map((item) => (
              <NavItemComponent 
                key={item.label} 
                item={item} 
                location={location} 
                navigate={navigate} 
                onClose={onClose} 
              />
            ))}
          </ul>
        </nav>

        <div className="absolute inset-x-0 bottom-0 hidden px-4 py-3 text-xs text-muted-foreground/80 md:block">
          <div className="rounded-md border border-sidebar-border/60 bg-sidebar p-3">
            <p>v0.1 — Internal preview</p>
          </div>
        </div>
      </aside>
    </>
  )
}

export default Sidebar
