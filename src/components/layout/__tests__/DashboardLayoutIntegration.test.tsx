import { render, screen } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { describe, it, expect } from 'vitest';
import DashboardLayout from '../DashboardLayout';

// Mock the Sisense components
vi.mock('../../sisense', () => ({
  SisenseContextProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="sisense-context">{children}</div>
  ),
  SisenseErrorBoundary: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="sisense-error-boundary">{children}</div>
  )
}));

// Mock the Header component
vi.mock('../Header', () => ({
  default: ({ onMenuClick }: { onMenuClick?: () => void }) => (
    <header data-testid="header">
      <button onClick={onMenuClick} data-testid="menu-button">Menu</button>
    </header>
  )
}));

// Mock the Sidebar component
vi.mock('../Sidebar', () => ({
  default: ({ items, open, onClose }: any) => (
    <aside data-testid="sidebar" data-open={open}>
      <button onClick={onClose} data-testid="close-sidebar">Close</button>
      {items?.map((item: any, index: number) => (
        <div key={index} data-testid={`nav-item-${item.label}`}>
          {item.label}
        </div>
      ))}
    </aside>
  )
}));

describe('DashboardLayout Integration', () => {
  const mockNavItems = [
    { label: 'Home', href: '/', icon: () => null },
    { label: 'Dashboard', href: '/dashboard', icon: () => null }
  ];

  const renderLayout = (props = {}) => {
    return render(
      <BrowserRouter>
        <DashboardLayout navItems={mockNavItems} {...props}>
          <div data-testid="test-content">Test Content</div>
        </DashboardLayout>
      </BrowserRouter>
    );
  };

  it('renders complete layout structure', () => {
    renderLayout();
    
    // Check that Sisense context is provided
    expect(screen.getByTestId('sisense-context')).toBeInTheDocument();
    
    // Check that error boundary is present
    expect(screen.getByTestId('sisense-error-boundary')).toBeInTheDocument();
    
    // Check that header is rendered
    expect(screen.getByTestId('header')).toBeInTheDocument();
    
    // Check that sidebar is rendered
    expect(screen.getByTestId('sidebar')).toBeInTheDocument();
    
    // Check that main content is rendered
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
  });

  it('passes navigation items to sidebar', () => {
    renderLayout();
    
    // Check that navigation items are rendered in sidebar
    expect(screen.getByTestId('nav-item-Home')).toBeInTheDocument();
    expect(screen.getByTestId('nav-item-Dashboard')).toBeInTheDocument();
  });

  it('has proper layout structure and styling', () => {
    renderLayout();
    
    // Check main container has proper flex layout
    const mainContainer = screen.getByTestId('sisense-context').parentElement;
    expect(mainContainer).toHaveClass('flex', 'min-h-svh');
  });

  it('handles sidebar open/close state', () => {
    renderLayout();
    
    const sidebar = screen.getByTestId('sidebar');
    const menuButton = screen.getByTestId('menu-button');
    
    // Initially sidebar should be closed
    expect(sidebar).toHaveAttribute('data-open', 'false');
    
    // Click menu button to open sidebar
    menuButton.click();
    
    // Sidebar should now be open
    expect(sidebar).toHaveAttribute('data-open', 'true');
  });

  it('integrates with main content spacing', () => {
    renderLayout();
    
    // Check that main content has proper spacing class
    const mainElement = screen.getByRole('main');
    expect(mainElement).toHaveClass('flex-1', 'p-6');
    
    // Check that content wrapper has spacing utilities
    const contentWrapper = mainElement.firstChild;
    expect(contentWrapper).toHaveClass('main-content-spacing');
  });
});