import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { describe, it, expect } from 'vitest';
import Header from '../Header';
import ThemeToggle from '../../theme/ThemeToggle';
import ProfileDropdown from '../ProfileDropdown';

// Mock the theme toggle and profile dropdown components
vi.mock('../../theme/ThemeToggle', () => ({
  default: () => <div data-testid="theme-toggle">Theme Toggle</div>
}));

vi.mock('../ProfileDropdown', () => ({
  default: () => <div data-testid="profile-dropdown">Profile Dropdown</div>
}));

describe('Header Integration', () => {
  const renderHeader = (props = {}) => {
    return render(
      <BrowserRouter>
        <Header {...props} />
      </BrowserRouter>
    );
  };

  it('renders header with all integrated components', () => {
    renderHeader();
    
    // Check that search input is present
    expect(screen.getByPlaceholderText('Search dashboards...')).toBeInTheDocument();
    
    // Check that theme toggle is integrated
    expect(screen.getByTestId('theme-toggle')).toBeInTheDocument();
    
    // Check that profile dropdown is integrated
    expect(screen.getByTestId('profile-dropdown')).toBeInTheDocument();
    
    // Check that notification button is present (by finding the bell icon)
    const buttons = screen.getAllByRole('button');
    const bellButton = buttons.find(button => button.querySelector('.lucide-bell'));
    expect(bellButton).toBeInTheDocument();
  });

  it('renders mobile menu button', () => {
    renderHeader();
    
    // Check that mobile menu button is present (first button with menu icon)
    const buttons = screen.getAllByRole('button');
    const menuButton = buttons.find(button => button.querySelector('.lucide-menu'));
    expect(menuButton).toBeInTheDocument();
  });

  it('calls onMenuClick when mobile menu is clicked', () => {
    const mockOnMenuClick = vi.fn();
    renderHeader({ onMenuClick: mockOnMenuClick });
    
    const menuButton = screen.getAllByRole('button')[0]; // First button should be menu
    menuButton.click();
    
    expect(mockOnMenuClick).toHaveBeenCalledTimes(1);
  });

  it('has proper header styling and layout', () => {
    renderHeader();
    
    const header = screen.getByRole('banner');
    expect(header).toHaveClass('sticky', 'top-0', 'z-40', 'border-b');
  });
});