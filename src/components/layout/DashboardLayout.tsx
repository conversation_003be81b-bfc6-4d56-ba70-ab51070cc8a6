import { useState } from "react"
import type { PropsWithChildren } from "react"
import { Outlet } from "react-router-dom"
import Header from "@/components/layout/Header"
import Sidebar, { type NavItem } from "@/components/layout/Sidebar"
import { SisenseContextProvider, SisenseErrorBoundary } from "@/components/sisense"

export function DashboardLayout(
  props: PropsWithChildren<{ navItems?: NavItem[] }>,
) {
  const { children, navItems } = props
  const [open, setOpen] = useState(false)

  return (
    <SisenseErrorBoundary>
      <SisenseContextProvider>
        <div className="flex min-h-svh bg-background text-foreground">
          {/* Sidebar */}
          <Sidebar items={navItems} open={open} onClose={() => setOpen(false)} />

          {/* Main content area */}
          <div className="flex flex-1 flex-col">
            {/* Header */}
            <Header onMenuClick={() => setOpen(true)} />

            {/* Main content */}
            <main className="flex-1 p-6">
              <div className="main-content-spacing">
                {children || <Outlet />}
              </div>
            </main>
          </div>
        </div>
      </SisenseContextProvider>
    </SisenseErrorBoundary>
  )
}

export default DashboardLayout
