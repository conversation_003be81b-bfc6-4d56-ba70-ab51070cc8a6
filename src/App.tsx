import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useNavigate } from "react-router-dom"
import { Helmet } from "react-helmet-async"
import { BarChart3, TrendingUp, Users, DollarSign } from "lucide-react"

function App() {
  const navigate = useNavigate()

  return (
    <>
      <Helmet>
        <title>IIJA Dashboard - Infrastructure Investment and Jobs Act Analytics</title>
        <meta name="description" content="Interactive dashboard for Infrastructure Investment and Jobs Act (IIJA) data visualization and analytics" />
      </Helmet>

      <div className="dashboard-page-header">
        <div className="flex flex-col gap-3 md:flex-row md:items-center md:justify-between page-header-consistent">
          <div>
            <h1 className="page-title-consistent">IIJA Dashboard</h1>
            <p className="page-description-consistent">Infrastructure Investment and Jobs Act analytics and insights</p>
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => navigate('/dashboard/summary-dashboard')}
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              View Dashboard
            </Button>
            <Button size="sm">Export Data</Button>
          </div>
        </div>
      </div>

      <section className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Total Funding
            </CardDescription>
            <CardTitle className="text-2xl">$1.2T</CardTitle>
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Active Projects
            </CardDescription>
            <CardTitle className="text-2xl">8,547</CardTitle>
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              States Participating
            </CardDescription>
            <CardTitle className="text-2xl">50</CardTitle>
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Completion Rate
            </CardDescription>
            <CardTitle className="text-2xl">67%</CardTitle>
          </CardHeader>
        </Card>
      </section>

      <section className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Access</CardTitle>
            <CardDescription>Navigate to key dashboard sections</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2">
              <Button 
                variant="outline" 
                className="justify-start"
                onClick={() => navigate('/dashboard/summary-dashboard')}
              >
                Summary Dashboard
              </Button>
              <Button 
                variant="outline" 
                className="justify-start"
                onClick={() => navigate('/dashboard/contract-awards')}
              >
                Contract Awards
              </Button>
              <Button 
                variant="outline" 
                className="justify-start"
                onClick={() => navigate('/dashboard/federal-aid-obligations')}
              >
                Federal Aid Obligations
              </Button>
              <Button 
                variant="outline" 
                className="justify-start"
                onClick={() => navigate('/dashboard/material-prices')}
              >
                Material Prices
              </Button>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Recent Updates</CardTitle>
            <CardDescription>Latest changes and data updates</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center justify-between">
                <span>Material prices updated</span>
                <span className="text-muted-foreground">2h ago</span>
              </li>
              <li className="flex items-center justify-between">
                <span>New contract awards added</span>
                <span className="text-muted-foreground">1d ago</span>
              </li>
              <li className="flex items-center justify-between">
                <span>State funding data refreshed</span>
                <span className="text-muted-foreground">2d ago</span>
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button 
              size="sm"
              onClick={() => navigate('/dashboard/summary-dashboard')}
            >
              View All Dashboards
            </Button>
          </CardFooter>
        </Card>
      </section>
    </>
  )
}

export default App