import { createBrowserRouter, Navigate, Outlet } from 'react-router-dom';
import App from '@/App';
import ComposeDashboardPage from '@/components/sisense/ComposeDashboardPage';
import RouteGuard from '@/components/router/RouteGuard';
import RouteErrorBoundary from '@/components/router/RouteErrorBoundary';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  Home, 
  BarChart3, 
  Settings, 
  Users, 
  TrendingUp,
  DollarSign,
  Building,
  MapPin,
  FileText,
  Truck,
  PiggyBank,
  Package
} from 'lucide-react';
import type { NavItem } from '@/components/layout/Sidebar';
import { isValidDashboardId } from '@/lib/navigation';

// Navigation items for the sidebar with all dashboards
export const navItems: NavItem[] = [
  { label: 'Home', href: '/', icon: Home },
  { 
    label: 'Dashboards', 
    icon: BarChart3,
    children: [
      { label: 'Summary Dashboard', href: '/dashboard/summary-dashboard', icon: TrendingUp },
      { label: 'Contract Awards', href: '/dashboard/contract-awards', icon: FileText },
      { label: 'Value Put in Place', href: '/dashboard/value-put-in-place', icon: DollarSign },
      { label: 'Federal Aid Obligations', href: '/dashboard/federal-aid-obligations', icon: Building },
      { label: 'IIJA State Funding', href: '/dashboard/iija-state-funding', icon: MapPin },
      { label: 'State Legislative Initiatives', href: '/dashboard/state-legislative-initiatives', icon: FileText },
      { label: 'Federal Aid Highway Funds', href: '/dashboard/federal-aid-highway-funds', icon: Truck },
      { label: 'State DOT Budgets', href: '/dashboard/state-dot-budgets', icon: PiggyBank },
      { label: 'Material Prices', href: '/dashboard/material-prices', icon: Package },
    ]
  },
  { label: 'Users', href: '/users', icon: Users },
  { label: 'Settings', href: '/settings', icon: Settings },
];

// Route definitions
export const router = createBrowserRouter([
  {
    path: '/',
    element: <DashboardLayout navItems={navItems} />,
    children: [
      {
        index: true,
        element: <App />,
      },
      {
        path: 'dashboard',
        element: (
          <RouteGuard requireSisenseConfig={true}>
            <Outlet />
          </RouteGuard>
        ),
        children: [
          {
            index: true,
            element: <Navigate to="/dashboard/summary-dashboard" replace />,
          },
          {
            path: 'summary-dashboard',
            element: <ComposeDashboardPage />,
          },
          {
            path: 'contract-awards',
            element: <ComposeDashboardPage />,
          },
          {
            path: 'value-put-in-place',
            element: <ComposeDashboardPage />,
          },
          {
            path: 'federal-aid-obligations',
            element: <ComposeDashboardPage />,
          },
          {
            path: 'iija-state-funding',
            element: <ComposeDashboardPage />,
          },
          {
            path: 'state-legislative-initiatives',
            element: <ComposeDashboardPage />,
          },
          {
            path: 'federal-aid-highway-funds',
            element: <ComposeDashboardPage />,
          },
          {
            path: 'state-dot-budgets',
            element: <ComposeDashboardPage />,
          },
          {
            path: 'material-prices',
            element: <ComposeDashboardPage />,
          },
          {
            path: ':dashboardId',
            element: <ComposeDashboardPage />,
            loader: ({ params }) => {
              // Validate dashboard ID exists
              const { dashboardId } = params;
              if (dashboardId && !isValidDashboardId(dashboardId)) {
                throw new Response('Dashboard not found', { status: 404 });
              }
              return null;
            },
          },
        ],
      },
      {
        path: 'users',
        element: (
          <RouteGuard requireAuth={true}>
            <div className="p-6">
              <h1 className="text-2xl font-semibold mb-4">Users</h1>
              <p className="text-muted-foreground">Users page coming soon...</p>
            </div>
          </RouteGuard>
        ),
      },
      {
        path: 'settings',
        element: (
          <RouteGuard requireAuth={true}>
            <div className="p-6">
              <h1 className="text-2xl font-semibold mb-4">Settings</h1>
              <p className="text-muted-foreground">Settings page coming soon...</p>
            </div>
          </RouteGuard>
        ),
      },
    ],
    errorElement: <RouteErrorBoundary />,
  },
  {
    path: '*',
    element: <Navigate to="/" replace />,
  },
]);

export default router;