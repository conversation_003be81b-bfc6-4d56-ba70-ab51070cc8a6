@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans: Inter, sans-serif;
  --font-mono: JetBrains Mono, monospace;
  --font-serif: Source Serif 4, serif;
  --radius: 0.375rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --radius: 0.375rem;
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.3211 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3211 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3211 0 0);
  --primary: oklch(0.4055 0.0981 244.6519);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9670 0.0029 264.5419);
  --secondary-foreground: oklch(0.4920 0.1899 28.4930);
  --muted: oklch(0.9846 0.0017 247.8389);
  --muted-foreground: oklch(0.5510 0.0234 264.3637);
  --accent: oklch(0.9514 0.0250 236.8242);
  --accent-foreground: oklch(0.3791 0.1378 265.5222);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --border: oklch(0.9276 0.0058 264.5313);
  --input: oklch(0.9276 0.0058 264.5313);
  --ring: oklch(0.6231 0.1880 259.8145);
  --chart-1: oklch(0.6231 0.1880 259.8145);
  --chart-2: oklch(0.5461 0.2152 262.8809);
  --chart-3: oklch(0.4882 0.2172 264.3763);
  --chart-4: oklch(0.4244 0.1809 265.6377);
  --chart-5: oklch(0.3791 0.1378 265.5222);
  --sidebar: oklch(0.9846 0.0017 247.8389);
  --sidebar-foreground: oklch(0.3211 0 0);
  --sidebar-primary: oklch(0.6231 0.1880 259.8145);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9514 0.0250 236.8242);
  --sidebar-accent-foreground: oklch(0.3791 0.1378 265.5222);
  --sidebar-border: oklch(0.9276 0.0058 264.5313);
  --sidebar-ring: oklch(0.6231 0.1880 259.8145);
  --destructive-foreground: oklch(1.0000 0 0);
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --shadow-color: oklch(0 0 0);
  --shadow-opacity: 0.1;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.2046 0 0);
  --foreground: oklch(0.9219 0 0);
  --card: oklch(0.2686 0 0);
  --card-foreground: oklch(0.9219 0 0);
  --popover: oklch(0.2686 0 0);
  --popover-foreground: oklch(0.9219 0 0);
  --primary: oklch(0.4055 0.0981 244.6519);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.4920 0.1899 28.4930);
  --secondary-foreground: oklch(0.9219 0 0);
  --muted: oklch(0.2686 0 0);
  --muted-foreground: oklch(0.7155 0 0);
  --accent: oklch(0.4055 0.0981 244.6519);
  --accent-foreground: oklch(0.8823 0.0571 254.1284);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --border: oklch(0.3715 0 0);
  --input: oklch(0.3715 0 0);
  --ring: oklch(0.6231 0.1880 259.8145);
  --chart-1: oklch(0.7137 0.1434 254.6240);
  --chart-2: oklch(0.6231 0.1880 259.8145);
  --chart-3: oklch(0.5461 0.2152 262.8809);
  --chart-4: oklch(0.4882 0.2172 264.3763);
  --chart-5: oklch(0.4244 0.1809 265.6377);
  --sidebar: oklch(0.2046 0 0);
  --sidebar-foreground: oklch(0.9219 0 0);
  --sidebar-primary: oklch(0.6231 0.1880 259.8145);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.3791 0.1378 265.5222);
  --sidebar-accent-foreground: oklch(0.8823 0.0571 254.1284);
  --sidebar-border: oklch(0.3715 0 0);
  --sidebar-ring: oklch(0.6231 0.1880 259.8145);
  --destructive-foreground: oklch(1.0000 0 0);
  --radius: 0.375rem;
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --shadow-color: oklch(0 0 0);
  --shadow-opacity: 0.1;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}

@layer utilities {
  /* Enhanced touch-friendly utilities */
  .tap-highlight-transparent {
    -webkit-tap-highlight-color: transparent;
  }
  
  /* iOS momentum scrolling */
  .-webkit-overflow-scrolling-touch {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Disable iOS callout */
  .-webkit-touch-callout-none {
    -webkit-touch-callout: none;
  }
  
  /* Prevent text selection on touch */
  .select-none-touch {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }
  
  /* Enhanced scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-none {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }
  
  .scrollbar-track-transparent {
    scrollbar-color: transparent transparent;
  }
  
  .scrollbar-thumb-border\/20::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border) / 0.2);
    border-radius: 9999px;
  }
  
  .hover\:scrollbar-thumb-border\/40:hover::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border) / 0.4);
  }
  
  /* Enhanced responsive grid utilities */
  .responsive-grid-auto {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  
  .responsive-grid-compact {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .responsive-grid-spacious {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
  
  .responsive-grid-mobile {
    grid-template-columns: 1fr;
  }
  
  .responsive-grid-tablet {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
  
  .responsive-grid-desktop {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  }
  
  /* Touch-friendly minimum sizes */
  .min-touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  .min-touch-target-large {
    min-height: 48px;
    min-width: 48px;
  }
  
  /* Enhanced safe area insets for mobile devices */
  .safe-area-inset-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-inset-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-area-inset-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-area-inset-right {
    padding-right: env(safe-area-inset-right);
  }
  
  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .pt-safe {
    padding-top: env(safe-area-inset-top);
  }
  
  .pl-safe {
    padding-left: env(safe-area-inset-left);
  }
  
  .pr-safe {
    padding-right: env(safe-area-inset-right);
  }
  
  /* Mobile-specific viewport utilities */
  .min-h-screen-mobile {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height */
  }
  
  .h-screen-mobile {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height */
  }
  
  /* Enhanced mobile scrolling */
  .scroll-mobile {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    scroll-behavior: smooth;
  }
  
  /* Mobile-optimized animations */
  .animate-mobile-scale {
    transition: transform 0.15s ease-out;
  }
  
  .animate-mobile-scale:active {
    transform: scale(0.95);
  }
  
  /* Responsive text utilities */
  .text-responsive-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }
  
  @media (min-width: 640px) {
    .text-responsive-xs {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
  }
  
  .text-responsive-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  
  @media (min-width: 640px) {
    .text-responsive-sm {
      font-size: 1rem;
      line-height: 1.5rem;
    }
  }
  
  .text-responsive-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  @media (min-width: 640px) {
    .text-responsive-base {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }
  }
  
  /* Responsive spacing utilities */
  .space-responsive-x > :not([hidden]) ~ :not([hidden]) {
    margin-left: 0.5rem;
  }
  
  @media (min-width: 640px) {
    .space-responsive-x > :not([hidden]) ~ :not([hidden]) {
      margin-left: 1rem;
    }
  }
  
  .space-responsive-y > :not([hidden]) ~ :not([hidden]) {
    margin-top: 0.5rem;
  }
  
  @media (min-width: 640px) {
    .space-responsive-y > :not([hidden]) ~ :not([hidden]) {
      margin-top: 1rem;
    }
  }
  
  /* Container query utilities for widget responsiveness */
  @container (max-width: 300px) {
    .widget-compact {
      padding: 0.5rem;
      font-size: 0.75rem;
    }
  }
  
  @container (min-width: 400px) {
    .widget-spacious {
      padding: 1.5rem;
      font-size: 1rem;
    }
  }
  
  /* Dashboard layout integration utilities */
  .dashboard-container {
    @apply w-full;
  }
  
  .dashboard-page-header {
    @apply mb-6;
  }
  
  .dashboard-page-title {
    @apply text-2xl font-semibold tracking-tight md:text-3xl;
  }
  
  .dashboard-page-description {
    @apply text-sm text-muted-foreground mt-1;
  }
  
  /* Ensure consistent spacing with existing layout */
  .main-content-spacing {
    @apply space-y-6;
  }
  
  /* Header integration utilities */
  .page-header-consistent {
    @apply mb-4;
  }
  
  .page-title-consistent {
    @apply text-2xl font-semibold tracking-tight md:text-3xl;
  }
  
  .page-description-consistent {
    @apply text-sm text-muted-foreground mt-1;
  }
}