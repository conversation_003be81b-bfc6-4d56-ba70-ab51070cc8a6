// Sisense Compose SDK Constants

// Environment configuration
export const SISENSE_CONFIG = {
  URL: import.meta.env.VITE_SISENSE_URL || '',
  TOKEN: import.meta.env.VITE_SISENSE_TOKEN || '',
  API_VERSION: 'v1',
  TIMEOUT: 30000, // 30 seconds
} as const;

// API endpoints
export const SISENSE_ENDPOINTS = {
  DASHBOARDS: '/api/v1/dashboards',
  WIDGETS: '/api/v1/dashboards/{dashboardId}/widgets',
  WIDGET_DATA: '/api/v1/widgets/{widgetId}/data',
  HEALTH: '/api/health',
} as const;

// Widget rendering configuration
export const WIDGET_CONFIG = {
  DEFAULT_HEIGHT: 400,
  DEFAULT_WIDTH: '100%',
  LOADING_TIMEOUT: 15000, // 15 seconds
  ERROR_RETRY_DELAY: 2000, // 2 seconds
  MAX_RETRIES: 3,
} as const;

// Grid layout configuration
export const GRID_CONFIG = {
  DEFAULT_COLUMNS: {
    mobile: 1,
    tablet: 2,
    desktop: 3,
    wide: 4,
  },
  GAP: 'gap-4',
  CONTAINER_PADDING: 'p-4',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  DASHBOARD_PREFERENCES: 'sisense-dashboard-preferences',
  ACTIVE_DASHBOARD: 'sisense-active-dashboard',
  WIDGET_CACHE: 'sisense-widget-cache',
  USER_SETTINGS: 'sisense-user-settings',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  AUTHENTICATION_FAILED: 'Authentication failed. Please check your Sisense credentials.',
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  DASHBOARD_LOAD_FAILED: 'Failed to load dashboard. Please try refreshing the page.',
  WIDGET_LOAD_FAILED: 'Failed to load widget. Click to retry.',
  PARSING_ERROR: 'Error parsing dashboard data. Please contact support.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  DASHBOARD_LOADED: 'Dashboard loaded successfully',
  WIDGETS_REFRESHED: 'Widgets refreshed successfully',
  PREFERENCES_SAVED: 'Preferences saved successfully',
} as const;

// Loading states
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;

export type LoadingState = typeof LOADING_STATES[keyof typeof LOADING_STATES];

// Tab parsing configuration
export const TAB_CONFIG = {
  DEFAULT_TAB_TITLE: 'Untitled Tab',
  MAX_TAB_TITLE_LENGTH: 50,
  TAB_SCRIPT_PATTERNS: {
    DISPLAY_WIDGETS: /displayWidgetIds\s*:\s*\[(.*?)\]/g,
    HIDE_WIDGETS: /hideWidgetIds\s*:\s*\[(.*?)\]/g,
    TAB_TITLE: /title\s*:\s*['"`](.*?)['"`]/g,
  },
} as const;

// Performance configuration
export const PERFORMANCE_CONFIG = {
  LAZY_LOADING_THRESHOLD: 100, // pixels from viewport
  DEBOUNCE_DELAY: 300, // milliseconds
  THROTTLE_DELAY: 100, // milliseconds
  CACHE_TTL: 300000, // 5 minutes
} as const;

// Accessibility configuration
export const A11Y_CONFIG = {
  FOCUS_VISIBLE_CLASS: 'focus-visible:ring-2 focus-visible:ring-blue-500',
  SKIP_LINK_CLASS: 'sr-only focus:not-sr-only',
  ARIA_LABELS: {
    DASHBOARD_TAB: 'Dashboard tab',
    WIDGET_CONTAINER: 'Widget container',
    REFRESH_BUTTON: 'Refresh dashboard',
    ERROR_BOUNDARY: 'Error occurred in widget',
    LOADING_INDICATOR: 'Loading content',
  },
} as const;