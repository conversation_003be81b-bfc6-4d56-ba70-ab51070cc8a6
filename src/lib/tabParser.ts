import type { ApiWidget, TabDefinition } from '@/types/sisense';
import { TAB_CONFIG } from '@/constants/sisense';

/**
 * Tab parsing utilities for processing tabber widget scripts
 */

// Extract widget IDs from script strings
const extractWidgetIds = (scriptContent: string, pattern: RegExp): string[] => {
  const matches = [];
  let match;
  
  // Reset regex lastIndex to ensure proper matching
  pattern.lastIndex = 0;
  
  while ((match = pattern.exec(scriptContent)) !== null) {
    const idsString = match[1];
    if (idsString) {
      // Parse the array-like string and extract IDs
      const ids = idsString
        .split(',')
        .map(id => id.trim().replace(/['"]/g, ''))
        .filter(id => id.length > 0);
      matches.push(...ids);
    }
  }
  
  return [...new Set(matches)]; // Remove duplicates
};

// Extract tab title from script
const extractTabTitle = (scriptContent: string): string => {
  const pattern = TAB_CONFIG.TAB_SCRIPT_PATTERNS.TAB_TITLE;
  pattern.lastIndex = 0; // Reset regex
  
  const match = pattern.exec(scriptContent);
  if (match && match[1]) {
    const title = match[1].trim();
    return title.length > TAB_CONFIG.MAX_TAB_TITLE_LENGTH 
      ? title.substring(0, TAB_CONFIG.MAX_TAB_TITLE_LENGTH) + '...'
      : title;
  }
  
  return TAB_CONFIG.DEFAULT_TAB_TITLE;
};

// Parse individual tab definition from script section
const parseTabSection = (
  scriptSection: string, 
  sourceTabberId?: string
): TabDefinition | null => {
  try {
    const displayWidgetIds = extractWidgetIds(
      scriptSection, 
      TAB_CONFIG.TAB_SCRIPT_PATTERNS.DISPLAY_WIDGETS
    );
    
    const hideWidgetIds = extractWidgetIds(
      scriptSection, 
      TAB_CONFIG.TAB_SCRIPT_PATTERNS.HIDE_WIDGETS
    );
    
    const title = extractTabTitle(scriptSection);
    
    // Only create tab definition if we have some widget configuration
    if (displayWidgetIds.length > 0 || hideWidgetIds.length > 0) {
      return {
        title,
        displayWidgetIds,
        hideWidgetIds,
        sourceTabberId
      };
    }
    
    return null;
  } catch (error) {
    console.warn('Error parsing tab section:', error);
    return null;
  }
};

// Main tab parsing function
export const parseTabScript = (
  widget: ApiWidget, 
  allWidgets: ApiWidget[] = []
): TabDefinition[] => {
  if (!widget.script || typeof widget.script !== 'string') {
    return [];
  }

  try {
    const script = widget.script.trim();
    if (!script) {
      return [];
    }

    // Split script into potential tab sections
    // Look for common tab definition patterns
    const tabSections = script.split(/(?=\{[^}]*(?:displayWidgetIds|hideWidgetIds))/);
    
    const tabs: TabDefinition[] = [];
    
    for (const section of tabSections) {
      if (section.trim()) {
        const tabDef = parseTabSection(section, widget.oid || widget.id);
        if (tabDef) {
          tabs.push(tabDef);
        }
      }
    }

    // If no tabs were parsed but we have a script, try to parse as a single tab
    if (tabs.length === 0) {
      const singleTab = parseTabSection(script, widget.oid || widget.id);
      if (singleTab) {
        tabs.push(singleTab);
      }
    }

    // Validate that referenced widget IDs exist
    const validWidgetIds = new Set(
      allWidgets.map(w => w.oid || w.id).filter(Boolean)
    );

    return tabs.map(tab => ({
      ...tab,
      displayWidgetIds: tab.displayWidgetIds.filter(id => validWidgetIds.has(id)),
      hideWidgetIds: tab.hideWidgetIds.filter(id => validWidgetIds.has(id))
    })).filter(tab => 
      tab.displayWidgetIds.length > 0 || tab.hideWidgetIds.length > 0
    );

  } catch (error) {
    console.error('Error parsing tab script for widget:', widget.oid || widget.id, error);
    return [];
  }
};

// Parse all tabber widgets in a dashboard
export const parseAllTabberWidgets = (widgets: ApiWidget[]): TabDefinition[] => {
  const allTabs: TabDefinition[] = [];
  
  widgets.forEach(widget => {
    if (widget.type === 'WidgetsTabber' || widget.type.toLowerCase().includes('tabber')) {
      const tabs = parseTabScript(widget, widgets);
      allTabs.push(...tabs);
    }
  });
  
  return allTabs;
};

// Validate tab definitions
export const validateTabDefinitions = (
  tabs: TabDefinition[], 
  availableWidgets: ApiWidget[]
): { valid: TabDefinition[]; invalid: TabDefinition[]; warnings: string[] } => {
  const validWidgetIds = new Set(
    availableWidgets.map(w => w.oid || w.id).filter(Boolean)
  );
  
  const valid: TabDefinition[] = [];
  const invalid: TabDefinition[] = [];
  const warnings: string[] = [];
  
  tabs.forEach(tab => {
    const validDisplayIds = tab.displayWidgetIds.filter(id => validWidgetIds.has(id));
    const validHideIds = tab.hideWidgetIds.filter(id => validWidgetIds.has(id));
    
    const invalidDisplayIds = tab.displayWidgetIds.filter(id => !validWidgetIds.has(id));
    const invalidHideIds = tab.hideWidgetIds.filter(id => !validWidgetIds.has(id));
    
    if (invalidDisplayIds.length > 0) {
      warnings.push(`Tab "${tab.title}" references non-existent display widgets: ${invalidDisplayIds.join(', ')}`);
    }
    
    if (invalidHideIds.length > 0) {
      warnings.push(`Tab "${tab.title}" references non-existent hide widgets: ${invalidHideIds.join(', ')}`);
    }
    
    if (validDisplayIds.length > 0 || validHideIds.length > 0) {
      valid.push({
        ...tab,
        displayWidgetIds: validDisplayIds,
        hideWidgetIds: validHideIds
      });
    } else {
      invalid.push(tab);
      warnings.push(`Tab "${tab.title}" has no valid widget references`);
    }
  });
  
  return { valid, invalid, warnings };
};

// Get tab statistics
export const getTabStats = (tabs: TabDefinition[]) => {
  const totalTabs = tabs.length;
  const totalDisplayWidgets = tabs.reduce((sum, tab) => sum + tab.displayWidgetIds.length, 0);
  const totalHideWidgets = tabs.reduce((sum, tab) => sum + tab.hideWidgetIds.length, 0);
  
  const uniqueDisplayWidgets = new Set(
    tabs.flatMap(tab => tab.displayWidgetIds)
  ).size;
  
  const uniqueHideWidgets = new Set(
    tabs.flatMap(tab => tab.hideWidgetIds)
  ).size;
  
  return {
    totalTabs,
    totalDisplayWidgets,
    totalHideWidgets,
    uniqueDisplayWidgets,
    uniqueHideWidgets,
    averageWidgetsPerTab: totalTabs > 0 ? totalDisplayWidgets / totalTabs : 0
  };
};

// Debug utilities for tab parsing
export const debugTabParsing = (widget: ApiWidget, allWidgets: ApiWidget[] = []) => {
  const tabs = parseTabScript(widget, allWidgets);
  const stats = getTabStats(tabs);
  const validation = validateTabDefinitions(tabs, allWidgets);
  
  return {
    widget: {
      id: widget.oid || widget.id,
      type: widget.type,
      hasScript: !!widget.script,
      scriptLength: widget.script?.length || 0
    },
    tabs,
    stats,
    validation,
    rawScript: widget.script
  };
};