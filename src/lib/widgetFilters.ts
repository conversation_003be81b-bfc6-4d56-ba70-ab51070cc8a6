import type { ApiWidget, TabDefinition } from '@/types/sisense';
import { 
  EXCLUDED_WIDGET_IDS, 
  EXCLUDED_WIDGET_TYPES, 
  TABBER_WIDGET_TYPES, 
  MAP_WIDGET_TYPES 
} from '@/config/dashboards';

/**
 * Widget filtering and validation utilities
 */

// Core filtering functions
export const isExcluded = (widget: ApiWidget): boolean => {
  // Check if widget ID is in exclusion list
  if (EXCLUDED_WIDGET_IDS.includes(widget.id)) {
    return true;
  }

  // Check if widget OID is in exclusion list (if available)
  if (widget.oid && EXCLUDED_WIDGET_IDS.includes(widget.oid)) {
    return true;
  }
  
  // Check if widget type is in exclusion list
  if (EXCLUDED_WIDGET_TYPES.includes(widget.type)) {
    return true;
  }
  
  // Check if widget subtype is in exclusion list
  if (widget.subtype && EXCLUDED_WIDGET_TYPES.includes(widget.subtype)) {
    return true;
  }

  // Exclude BloX widgets (case-insensitive)
  if (widget.type.toLowerCase().includes('blox')) {
    return true;
  }

  // Exclude widgets with empty or invalid IDs
  if (!widget.id && !widget.oid) {
    return true;
  }
  
  return false;
};

export const isTabber = (widget: ApiWidget): boolean => {
  // Check primary type
  if (TABBER_WIDGET_TYPES.includes(widget.type)) {
    return true;
  }

  // Check subtype
  if (widget.subtype && TABBER_WIDGET_TYPES.includes(widget.subtype)) {
    return true;
  }

  // Check for tabber-like patterns in type name
  const tabberPatterns = ['tabber', 'tabs', 'tab-container'];
  const typeString = `${widget.type} ${widget.subtype || ''}`.toLowerCase();
  
  return tabberPatterns.some(pattern => typeString.includes(pattern));
};

export const isMapWidget = (widget: ApiWidget): boolean => {
  // Check explicit isMap flag
  if (widget.isMap === true) {
    return true;
  }

  // Check if type matches known map widget types
  if (MAP_WIDGET_TYPES.some(type => widget.type.includes(type))) {
    return true;
  }

  // Check for map-like patterns in type name
  const mapPatterns = ['map', 'geo', 'spatial', 'location'];
  const typeString = `${widget.type} ${widget.subtype || ''}`.toLowerCase();
  
  return mapPatterns.some(pattern => typeString.includes(pattern));
};

// Widget type validation
export const isValidWidget = (widget: ApiWidget): boolean => {
  // Must have either id or oid
  if (!widget.id && !widget.oid) {
    return false;
  }

  // Must have a type
  if (!widget.type || widget.type.trim() === '') {
    return false;
  }

  // Check for minimum required properties
  return true;
};

// Advanced filtering functions
export const filterWidgets = (
  widgets: ApiWidget[], 
  options: {
    excludeBlox?: boolean;
    excludeMaps?: boolean;
    excludeTabbers?: boolean;
    includeOnly?: string[];
    excludeTypes?: string[];
    customFilter?: (widget: ApiWidget) => boolean;
  } = {}
): ApiWidget[] => {
  const {
    excludeBlox = true,
    excludeMaps = false,
    excludeTabbers = false,
    includeOnly,
    excludeTypes = [],
    customFilter
  } = options;

  return widgets.filter(widget => {
    // Basic validation
    if (!isValidWidget(widget)) {
      return false;
    }

    // Apply standard exclusion rules
    if (excludeBlox && isExcluded(widget)) {
      return false;
    }

    // Exclude maps if requested
    if (excludeMaps && isMapWidget(widget)) {
      return false;
    }

    // Exclude tabbers if requested
    if (excludeTabbers && isTabber(widget)) {
      return false;
    }

    // Include only specific widget IDs if provided
    if (includeOnly && includeOnly.length > 0) {
      const widgetId = widget.oid || widget.id;
      if (!includeOnly.includes(widgetId)) {
        return false;
      }
    }

    // Exclude specific types if provided
    if (excludeTypes.length > 0) {
      if (excludeTypes.includes(widget.type) || 
          (widget.subtype && excludeTypes.includes(widget.subtype))) {
        return false;
      }
    }

    // Apply custom filter if provided
    if (customFilter && !customFilter(widget)) {
      return false;
    }

    return true;
  });
};

// Tab-specific filtering
export const filterWidgetsForTab = (
  widgets: ApiWidget[], 
  tabDefinition: TabDefinition
): ApiWidget[] => {
  const { displayWidgetIds, hideWidgetIds } = tabDefinition;

  return widgets.filter(widget => {
    const widgetId = widget.oid || widget.id;

    // If hideWidgetIds is specified, exclude those widgets
    if (hideWidgetIds && hideWidgetIds.length > 0) {
      if (hideWidgetIds.includes(widgetId)) {
        return false;
      }
    }

    // If displayWidgetIds is specified, only include those widgets
    if (displayWidgetIds && displayWidgetIds.length > 0) {
      if (!displayWidgetIds.includes(widgetId)) {
        return false;
      }
    }

    // Apply standard filtering
    return !isExcluded(widget) && isValidWidget(widget);
  });
};

// Get widgets that are not in any tab (untabbed widgets)
export const getUntabbedWidgets = (
  widgets: ApiWidget[], 
  tabs: TabDefinition[]
): ApiWidget[] => {
  // Collect all widget IDs that are assigned to tabs
  const tabbedWidgetIds = new Set<string>();
  
  tabs.forEach(tab => {
    if (tab.displayWidgetIds) {
      tab.displayWidgetIds.forEach(id => tabbedWidgetIds.add(id));
    }
  });

  // Return widgets that are not in any tab and not excluded
  return widgets.filter(widget => {
    const widgetId = widget.oid || widget.id;
    return !tabbedWidgetIds.has(widgetId) && 
           !isExcluded(widget) && 
           !isTabber(widget) && 
           isValidWidget(widget);
  });
};

// Widget categorization
export const categorizeWidgets = (widgets: ApiWidget[]) => {
  const categories = {
    valid: [] as ApiWidget[],
    excluded: [] as ApiWidget[],
    tabbers: [] as ApiWidget[],
    maps: [] as ApiWidget[],
    invalid: [] as ApiWidget[]
  };

  widgets.forEach(widget => {
    if (!isValidWidget(widget)) {
      categories.invalid.push(widget);
    } else if (isExcluded(widget)) {
      categories.excluded.push(widget);
    } else if (isTabber(widget)) {
      categories.tabbers.push(widget);
    } else if (isMapWidget(widget)) {
      categories.maps.push(widget);
    } else {
      categories.valid.push(widget);
    }
  });

  return categories;
};

// Widget statistics
export const getWidgetStats = (widgets: ApiWidget[]) => {
  const categories = categorizeWidgets(widgets);
  
  return {
    total: widgets.length,
    valid: categories.valid.length,
    excluded: categories.excluded.length,
    tabbers: categories.tabbers.length,
    maps: categories.maps.length,
    invalid: categories.invalid.length,
    renderableCount: categories.valid.length + categories.maps.length,
    categories
  };
};

// Debug utilities
export const debugWidget = (widget: ApiWidget) => {
  return {
    id: widget.id,
    oid: widget.oid,
    title: widget.title,
    type: widget.type,
    subtype: widget.subtype,
    isMap: widget.isMap,
    checks: {
      isValid: isValidWidget(widget),
      isExcluded: isExcluded(widget),
      isTabber: isTabber(widget),
      isMapWidget: isMapWidget(widget)
    }
  };
};

export const debugWidgetList = (widgets: ApiWidget[]) => {
  const stats = getWidgetStats(widgets);
  const debugInfo = widgets.map(debugWidget);
  
  return {
    stats,
    widgets: debugInfo,
    summary: {
      totalWidgets: widgets.length,
      renderableWidgets: stats.renderableCount,
      excludedWidgets: stats.excluded,
      tabberWidgets: stats.tabbers,
      mapWidgets: stats.maps,
      invalidWidgets: stats.invalid
    }
  };
};