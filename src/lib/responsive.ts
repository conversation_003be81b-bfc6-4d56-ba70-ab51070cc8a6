/**
 * Responsive utilities for dashboard layouts
 * Provides breakpoint management, responsive grid configurations, and touch-friendly interactions
 */

// Enhanced responsive breakpoints with mobile-first approach
export const BREAKPOINTS = {
  xs: 475,   // Extra small devices (small phones)
  sm: 640,   // Small devices (landscape phones)
  md: 768,   // Medium devices (tablets)
  lg: 1024,  // Large devices (desktops)
  xl: 1280,  // Extra large devices (large desktops)
  '2xl': 1536, // 2X large devices (larger desktops)
  '3xl': 1920  // Ultra-wide displays
} as const;

export type BreakpointKey = keyof typeof BREAKPOINTS;

/**
 * Enhanced device type detection with more granular categories
 */
export type DeviceType = 'mobile-small' | 'mobile' | 'tablet' | 'desktop' | 'large-desktop' | 'ultra-wide';

export const getDeviceType = (width: number): DeviceType => {
  if (width < BREAKPOINTS.xs) return 'mobile-small';
  if (width < BREAKPOINTS.md) return 'mobile';
  if (width < BREAKPOINTS.lg) return 'tablet';
  if (width < BREAKPOINTS.xl) return 'desktop';
  if (width < BREAKPOINTS['2xl']) return 'large-desktop';
  return 'ultra-wide';
};

/**
 * Device orientation detection
 */
export type DeviceOrientation = 'portrait' | 'landscape';

export const getDeviceOrientation = (width: number, height: number): DeviceOrientation => {
  return width > height ? 'landscape' : 'portrait';
};

/**
 * Touch device detection
 */
export const isTouchDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

/**
 * Enhanced responsive grid configuration with mobile-first approach
 */
export interface ResponsiveGridConfig {
  columns: {
    mobileSmall: number;
    mobile: number;
    tablet: number;
    desktop: number;
    largeDesktop: number;
    ultraWide: number;
  };
  gap: {
    mobileSmall: string;
    mobile: string;
    tablet: string;
    desktop: string;
    largeDesktop: string;
    ultraWide: string;
  };
  padding: {
    mobileSmall: string;
    mobile: string;
    tablet: string;
    desktop: string;
    largeDesktop: string;
    ultraWide: string;
  };
  maxHeight?: {
    mobileSmall: string;
    mobile: string;
    tablet: string;
    desktop: string;
    largeDesktop: string;
    ultraWide: string;
  };
  minWidgetWidth?: {
    mobileSmall: string;
    mobile: string;
    tablet: string;
    desktop: string;
    largeDesktop: string;
    ultraWide: string;
  };
  aspectRatio?: {
    mobileSmall: string;
    mobile: string;
    tablet: string;
    desktop: string;
    largeDesktop: string;
    ultraWide: string;
  };
}

/**
 * Enhanced responsive grid configurations with mobile-first approach
 */
export const RESPONSIVE_GRID_CONFIGS: Record<string, ResponsiveGridConfig> = {
  // Compact layout for many widgets with enhanced mobile support
  compact: {
    columns: {
      mobileSmall: 1,
      mobile: 1,
      tablet: 2,
      desktop: 3,
      largeDesktop: 4,
      ultraWide: 5
    },
    gap: {
      mobileSmall: 'gap-1',
      mobile: 'gap-2',
      tablet: 'gap-3',
      desktop: 'gap-4',
      largeDesktop: 'gap-4',
      ultraWide: 'gap-5'
    },
    padding: {
      mobileSmall: 'p-1',
      mobile: 'p-2',
      tablet: 'p-3',
      desktop: 'p-4',
      largeDesktop: 'p-4',
      ultraWide: 'p-5'
    },
    maxHeight: {
      mobileSmall: '65vh',
      mobile: '70vh',
      tablet: '75vh',
      desktop: '80vh',
      largeDesktop: '85vh',
      ultraWide: '90vh'
    },
    minWidgetWidth: {
      mobileSmall: '280px',
      mobile: '300px',
      tablet: '320px',
      desktop: '350px',
      largeDesktop: '380px',
      ultraWide: '400px'
    }
  },

  // Standard layout for most dashboards with enhanced mobile support
  standard: {
    columns: {
      mobileSmall: 1,
      mobile: 1,
      tablet: 2,
      desktop: 3,
      largeDesktop: 4,
      ultraWide: 5
    },
    gap: {
      mobileSmall: 'gap-2',
      mobile: 'gap-3',
      tablet: 'gap-4',
      desktop: 'gap-6',
      largeDesktop: 'gap-6',
      ultraWide: 'gap-8'
    },
    padding: {
      mobileSmall: 'p-2',
      mobile: 'p-3',
      tablet: 'p-4',
      desktop: 'p-6',
      largeDesktop: 'p-6',
      ultraWide: 'p-8'
    },
    maxHeight: {
      mobileSmall: '70vh',
      mobile: '75vh',
      tablet: '80vh',
      desktop: '85vh',
      largeDesktop: '90vh',
      ultraWide: '95vh'
    },
    minWidgetWidth: {
      mobileSmall: '300px',
      mobile: '320px',
      tablet: '350px',
      desktop: '380px',
      largeDesktop: '400px',
      ultraWide: '450px'
    }
  },

  // Spacious layout for fewer, larger widgets with mobile optimization
  spacious: {
    columns: {
      mobileSmall: 1,
      mobile: 1,
      tablet: 1,
      desktop: 2,
      largeDesktop: 3,
      ultraWide: 4
    },
    gap: {
      mobileSmall: 'gap-3',
      mobile: 'gap-4',
      tablet: 'gap-6',
      desktop: 'gap-8',
      largeDesktop: 'gap-8',
      ultraWide: 'gap-10'
    },
    padding: {
      mobileSmall: 'p-3',
      mobile: 'p-4',
      tablet: 'p-6',
      desktop: 'p-8',
      largeDesktop: 'p-8',
      ultraWide: 'p-10'
    },
    maxHeight: {
      mobileSmall: '75vh',
      mobile: '80vh',
      tablet: '85vh',
      desktop: '90vh',
      largeDesktop: '95vh',
      ultraWide: '100vh'
    },
    minWidgetWidth: {
      mobileSmall: '320px',
      mobile: '350px',
      tablet: '400px',
      desktop: '450px',
      largeDesktop: '500px',
      ultraWide: '550px'
    }
  },

  // Dense layout for maximum widgets with mobile considerations
  dense: {
    columns: {
      mobileSmall: 1,
      mobile: 1,
      tablet: 3,
      desktop: 4,
      largeDesktop: 6,
      ultraWide: 8
    },
    gap: {
      mobileSmall: 'gap-1',
      mobile: 'gap-1',
      tablet: 'gap-2',
      desktop: 'gap-3',
      largeDesktop: 'gap-3',
      ultraWide: 'gap-4'
    },
    padding: {
      mobileSmall: 'p-1',
      mobile: 'p-1',
      tablet: 'p-2',
      desktop: 'p-3',
      largeDesktop: 'p-3',
      ultraWide: 'p-4'
    },
    maxHeight: {
      mobileSmall: '60vh',
      mobile: '65vh',
      tablet: '70vh',
      desktop: '75vh',
      largeDesktop: '80vh',
      ultraWide: '85vh'
    },
    minWidgetWidth: {
      mobileSmall: '250px',
      mobile: '280px',
      tablet: '300px',
      desktop: '320px',
      largeDesktop: '350px',
      ultraWide: '380px'
    }
  },

  // Mobile-optimized layout for touch devices
  mobile: {
    columns: {
      mobileSmall: 1,
      mobile: 1,
      tablet: 1,
      desktop: 2,
      largeDesktop: 3,
      ultraWide: 4
    },
    gap: {
      mobileSmall: 'gap-4',
      mobile: 'gap-4',
      tablet: 'gap-5',
      desktop: 'gap-6',
      largeDesktop: 'gap-6',
      ultraWide: 'gap-8'
    },
    padding: {
      mobileSmall: 'p-4',
      mobile: 'p-4',
      tablet: 'p-5',
      desktop: 'p-6',
      largeDesktop: 'p-6',
      ultraWide: 'p-8'
    },
    maxHeight: {
      mobileSmall: '80vh',
      mobile: '85vh',
      tablet: '90vh',
      desktop: '90vh',
      largeDesktop: '95vh',
      ultraWide: '100vh'
    },
    minWidgetWidth: {
      mobileSmall: '100%',
      mobile: '100%',
      tablet: '100%',
      desktop: '400px',
      largeDesktop: '450px',
      ultraWide: '500px'
    },
    aspectRatio: {
      mobileSmall: 'aspect-[4/3]',
      mobile: 'aspect-[4/3]',
      tablet: 'aspect-[16/10]',
      desktop: 'aspect-[16/9]',
      largeDesktop: 'aspect-[16/9]',
      ultraWide: 'aspect-[21/9]'
    }
  }
};

/**
 * Generate enhanced Tailwind CSS classes for responsive grid layout
 */
export const generateResponsiveGridClasses = (
  config: ResponsiveGridConfig,
  widgetCount?: number
): string => {
  const { columns, gap, padding, minWidgetWidth } = config;
  
  // Auto-adjust columns based on widget count if provided
  const adjustedColumns = widgetCount ? {
    mobileSmall: Math.min(columns.mobileSmall, widgetCount),
    mobile: Math.min(columns.mobile, widgetCount),
    tablet: Math.min(columns.tablet, widgetCount),
    desktop: Math.min(columns.desktop, widgetCount),
    largeDesktop: Math.min(columns.largeDesktop, widgetCount),
    ultraWide: Math.min(columns.ultraWide, widgetCount)
  } : columns;

  const gridClasses = [
    'grid',
    // Enhanced responsive grid columns with more breakpoints
    `grid-cols-${adjustedColumns.mobileSmall}`,
    `xs:grid-cols-${adjustedColumns.mobile}`,
    `md:grid-cols-${adjustedColumns.tablet}`,
    `lg:grid-cols-${adjustedColumns.desktop}`,
    `xl:grid-cols-${adjustedColumns.largeDesktop}`,
    `2xl:grid-cols-${adjustedColumns.ultraWide}`,
    
    // Enhanced responsive gaps
    gap.mobileSmall,
    `xs:${gap.mobile}`,
    `md:${gap.tablet}`,
    `lg:${gap.desktop}`,
    `xl:${gap.largeDesktop}`,
    `2xl:${gap.ultraWide}`,
    
    // Enhanced responsive padding
    padding.mobileSmall,
    `xs:${padding.mobile}`,
    `md:${padding.tablet}`,
    `lg:${padding.desktop}`,
    `xl:${padding.largeDesktop}`,
    `2xl:${padding.ultraWide}`,
    
    // Auto-fit grid with minimum widget widths if specified
    minWidgetWidth && [
      'auto-rows-max',
      'items-start',
      'justify-items-stretch'
    ]
  ].flat().filter(Boolean);

  return gridClasses.join(' ');
};

/**
 * Generate responsive container classes with scrolling support
 */
export const generateResponsiveContainerClasses = (
  config: ResponsiveGridConfig,
  enableScrolling: boolean = false,
  deviceType?: DeviceType
): string => {
  const { maxHeight } = config;
  
  const containerClasses = [
    'w-full',
    'relative',
    
    // Responsive max heights
    maxHeight && [
      `max-h-[${maxHeight.mobileSmall}]`,
      `xs:max-h-[${maxHeight.mobile}]`,
      `md:max-h-[${maxHeight.tablet}]`,
      `lg:max-h-[${maxHeight.desktop}]`,
      `xl:max-h-[${maxHeight.largeDesktop}]`,
      `2xl:max-h-[${maxHeight.ultraWide}]`
    ],
    
    // Scrolling configuration
    enableScrolling && [
      'overflow-auto',
      'overscroll-contain',
      'scroll-smooth',
      TOUCH_FRIENDLY_CLASSES.scrollable
    ],
    
    // Mobile-specific optimizations
    (deviceType === 'mobile-small' || deviceType === 'mobile') && [
      'safe-area-inset-bottom',
      'pb-safe',
      '-webkit-overflow-scrolling-touch'
    ]
  ].flat().filter(Boolean);

  return containerClasses.join(' ');
};

/**
 * Enhanced touch-friendly interaction classes with mobile optimization
 */
export const TOUCH_FRIENDLY_CLASSES = {
  // Basic touch optimization
  base: [
    'touch-manipulation', // Optimize for touch
    'select-none', // Prevent text selection on touch
    'tap-highlight-transparent', // Remove tap highlight
    'user-select-none', // Prevent text selection
    '-webkit-touch-callout-none' // Disable iOS callout
  ].join(' '),

  // Interactive elements (buttons, tabs, etc.) with enhanced mobile support
  interactive: [
    'touch-manipulation',
    'active:scale-95', // Touch feedback
    'transition-transform',
    'duration-150',
    'ease-out',
    'cursor-pointer',
    'focus-visible:outline-none',
    'focus-visible:ring-2',
    'focus-visible:ring-ring',
    'focus-visible:ring-offset-2',
    'min-touch-target', // Ensure minimum touch target size
    'relative', // For pseudo-element touch targets
    'tap-highlight-transparent',
    // Enhanced mobile touch feedback
    'active:bg-accent/10',
    'active:border-accent/20'
  ].join(' '),

  // Scrollable containers with enhanced mobile support
  scrollable: [
    'touch-pan-y', // Enable vertical touch scrolling
    'touch-pan-x', // Enable horizontal touch scrolling
    'overscroll-contain', // Prevent scroll chaining
    'scroll-smooth', // Smooth scrolling
    'scrollbar-thin', // Thin scrollbars
    'scrollbar-track-transparent',
    'scrollbar-thumb-border/20',
    'hover:scrollbar-thumb-border/40',
    // iOS-specific optimizations
    '-webkit-overflow-scrolling-touch',
    'overscroll-y-contain', // Prevent bounce scrolling
    // Enhanced momentum scrolling
    'will-change-scroll'
  ].join(' '),

  // Cards and widgets with enhanced mobile interactions
  card: [
    'touch-manipulation',
    'transition-all',
    'duration-200',
    'ease-in-out',
    'cursor-pointer',
    // Enhanced mobile touch feedback
    'active:scale-[0.98]',
    'active:shadow-sm',
    'active:bg-accent/5',
    // Desktop hover effects (only on non-touch devices)
    'hover:shadow-md',
    'hover:scale-[1.02]',
    'hover:border-accent/20',
    // Focus states for accessibility
    'focus-within:ring-2',
    'focus-within:ring-ring',
    'focus-within:ring-offset-2',
    'focus-within:outline-none',
    // Prevent text selection
    'select-none',
    'tap-highlight-transparent'
  ].join(' '),

  // Navigation elements (tabs, buttons) with large touch targets
  navigation: [
    'touch-manipulation',
    'min-h-[44px]', // iOS minimum touch target
    'min-w-[44px]', // iOS minimum touch target
    'flex',
    'items-center',
    'justify-center',
    'relative',
    'transition-all',
    'duration-200',
    'ease-out',
    'cursor-pointer',
    'select-none',
    'tap-highlight-transparent',
    // Enhanced touch feedback
    'active:scale-95',
    'active:bg-accent/10',
    // Focus states
    'focus-visible:outline-none',
    'focus-visible:ring-2',
    'focus-visible:ring-ring',
    'focus-visible:ring-offset-1'
  ].join(' '),

  // Swipe-enabled containers
  swipeable: [
    'touch-pan-x',
    'touch-manipulation',
    'select-none',
    'cursor-grab',
    'active:cursor-grabbing',
    'will-change-transform',
    'transition-transform',
    'duration-300',
    'ease-out'
  ].join(' ')
};

/**
 * Enhanced responsive container classes with mobile-first approach
 */
export const RESPONSIVE_CONTAINER_CLASSES = {
  // Main dashboard container with mobile optimizations
  dashboard: [
    'w-full',
    'min-h-screen',
    'bg-background',
    'text-foreground',
    'overflow-x-hidden', // Prevent horizontal scroll
    'relative',
    // Mobile-specific optimizations
    'safe-area-inset-top',
    'safe-area-inset-bottom',
    // Enhanced touch scrolling
    'touch-manipulation',
    'overscroll-y-contain'
  ].join(' '),

  // Tab container with enhanced mobile support
  tabContainer: [
    'w-full',
    'min-h-0', // Allow shrinking
    'flex',
    'flex-col',
    'relative',
    // Mobile optimizations
    'touch-manipulation',
    'overflow-hidden'
  ].join(' '),

  // Widget grid container with responsive behavior
  widgetGrid: [
    'w-full',
    'min-h-0',
    'relative',
    'overflow-hidden', // Contain scrolling
    // Grid-specific optimizations
    'items-start',
    'justify-items-stretch',
    // Mobile optimizations
    'touch-manipulation'
  ].join(' '),

  // Scrollable content area with enhanced mobile support
  scrollableContent: [
    'overflow-auto',
    'will-change-scroll', // Optimize for scrolling
    'relative',
    'overscroll-contain', // Prevent scroll chaining
    'scroll-smooth',
    // Enhanced mobile scrolling
    '-webkit-overflow-scrolling-touch',
    'overscroll-y-contain',
    TOUCH_FRIENDLY_CLASSES.scrollable
  ].join(' '),

  // Mobile-optimized content wrapper
  mobileContent: [
    'w-full',
    'px-4', // Standard mobile padding
    'py-2',
    'safe-area-inset-left',
    'safe-area-inset-right',
    'touch-manipulation',
    'overflow-hidden'
  ].join(' '),

  // Responsive card container
  cardContainer: [
    'w-full',
    'h-full',
    'flex',
    'flex-col',
    'relative',
    'overflow-hidden',
    'touch-manipulation',
    // Responsive padding
    'p-2',
    'sm:p-3',
    'md:p-4',
    'lg:p-5'
  ].join(' ')
};

/**
 * Enhanced responsive tab navigation classes with mobile-first design
 */
export const RESPONSIVE_TAB_CLASSES = {
  // Tab list container with enhanced mobile support
  tabsList: [
    'flex',
    'w-full',
    'overflow-x-auto', // Horizontal scroll on mobile
    'overflow-y-hidden',
    'scrollbar-none', // Hide scrollbar
    'snap-x', // Snap scrolling
    'snap-mandatory',
    'gap-1',
    'p-1',
    'bg-muted',
    'rounded-lg',
    // Enhanced mobile scrolling
    '-webkit-overflow-scrolling-touch',
    'overscroll-x-contain',
    'touch-pan-x',
    'will-change-scroll',
    TOUCH_FRIENDLY_CLASSES.scrollable
  ].join(' '),

  // Individual tab trigger with enhanced touch support
  tabTrigger: [
    'flex-shrink-0', // Prevent shrinking
    'snap-start', // Snap point
    'px-3',
    'py-2',
    'text-sm',
    'font-medium',
    'rounded-md',
    'transition-all',
    'duration-200',
    'whitespace-nowrap', // Prevent text wrapping
    'min-w-0', // Allow text truncation
    'max-w-xs', // Limit maximum width
    'truncate', // Truncate long text
    // Enhanced mobile touch targets
    'min-h-[44px]', // iOS minimum touch target
    'min-w-[60px]', // Minimum width for touch
    'flex',
    'items-center',
    'justify-center',
    'relative',
    TOUCH_FRIENDLY_CLASSES.navigation
  ].join(' '),

  // Tab content area with enhanced animations
  tabContent: [
    'w-full',
    'min-h-0',
    'focus-visible:outline-none',
    'animate-in',
    'fade-in-0',
    'zoom-in-95',
    'duration-200',
    // Mobile optimizations
    'touch-manipulation',
    'overflow-hidden'
  ].join(' '),

  // Mobile-specific tab list (horizontal scrolling)
  mobileTabsList: [
    'flex',
    'w-auto',
    'min-w-full',
    'overflow-x-auto',
    'overflow-y-hidden',
    'scrollbar-none',
    'snap-x',
    'snap-mandatory',
    'gap-2',
    'px-4',
    'py-2',
    'bg-muted/50',
    'rounded-lg',
    // Enhanced mobile scrolling
    '-webkit-overflow-scrolling-touch',
    'overscroll-x-contain',
    'touch-pan-x',
    'will-change-scroll'
  ].join(' '),

  // Mobile tab trigger with larger touch targets
  mobileTabTrigger: [
    'flex-shrink-0',
    'snap-start',
    'px-4',
    'py-3',
    'text-sm',
    'font-medium',
    'rounded-lg',
    'transition-all',
    'duration-200',
    'whitespace-nowrap',
    'min-w-[120px]', // Larger minimum width for mobile
    'min-h-[48px]', // Larger touch target
    'flex',
    'items-center',
    'justify-center',
    'relative',
    'bg-background/80',
    'border',
    'border-border/50',
    // Enhanced touch feedback
    'active:scale-95',
    'active:bg-accent/10',
    'touch-manipulation',
    'select-none',
    'tap-highlight-transparent'
  ].join(' ')
};

/**
 * Responsive widget sizing utilities
 */
export interface ResponsiveWidgetSize {
  mobile: { width: string; height: string };
  tablet: { width: string; height: string };
  desktop: { width: string; height: string };
  largeDesktop: { width: string; height: string };
}

export const RESPONSIVE_WIDGET_SIZES: Record<string, ResponsiveWidgetSize> = {
  small: {
    mobile: { width: 'w-full', height: 'h-64' },
    tablet: { width: 'w-full', height: 'h-72' },
    desktop: { width: 'w-full', height: 'h-80' },
    largeDesktop: { width: 'w-full', height: 'h-80' }
  },
  medium: {
    mobile: { width: 'w-full', height: 'h-80' },
    tablet: { width: 'w-full', height: 'h-96' },
    desktop: { width: 'w-full', height: 'h-[400px]' },
    largeDesktop: { width: 'w-full', height: 'h-[400px]' }
  },
  large: {
    mobile: { width: 'w-full', height: 'h-96' },
    tablet: { width: 'w-full', height: 'h-[400px]' },
    desktop: { width: 'w-full', height: 'h-[500px]' },
    largeDesktop: { width: 'w-full', height: 'h-[500px]' }
  },
  auto: {
    mobile: { width: 'w-full', height: 'h-auto' },
    tablet: { width: 'w-full', height: 'h-auto' },
    desktop: { width: 'w-full', height: 'h-auto' },
    largeDesktop: { width: 'w-full', height: 'h-auto' }
  }
};

/**
 * Generate responsive widget size classes
 */
export const generateResponsiveWidgetClasses = (size: keyof typeof RESPONSIVE_WIDGET_SIZES): string => {
  const sizeConfig = RESPONSIVE_WIDGET_SIZES[size];
  
  return [
    sizeConfig.mobile.width,
    sizeConfig.mobile.height,
    `md:${sizeConfig.tablet.width}`,
    `md:${sizeConfig.tablet.height}`,
    `lg:${sizeConfig.desktop.width}`,
    `lg:${sizeConfig.desktop.height}`,
    `xl:${sizeConfig.largeDesktop.width}`,
    `xl:${sizeConfig.largeDesktop.height}`
  ].join(' ');
};

/**
 * Enhanced hook for detecting screen size, device type, and capabilities
 */
export const useResponsive = () => {
  const [screenSize, setScreenSize] = React.useState(() => {
    if (typeof window !== 'undefined') {
      return {
        width: window.innerWidth,
        height: window.innerHeight
      };
    }
    return { width: 1024, height: 768 }; // Default for SSR
  });

  const [deviceType, setDeviceType] = React.useState<DeviceType>(() => 
    getDeviceType(screenSize.width)
  );

  const [orientation, setOrientation] = React.useState<DeviceOrientation>(() =>
    getDeviceOrientation(screenSize.width, screenSize.height)
  );

  const [isTouch, setIsTouch] = React.useState(() => isTouchDevice());

  React.useEffect(() => {
    const handleResize = () => {
      const newSize = {
        width: window.innerWidth,
        height: window.innerHeight
      };
      setScreenSize(newSize);
      setDeviceType(getDeviceType(newSize.width));
      setOrientation(getDeviceOrientation(newSize.width, newSize.height));
    };

    const handleOrientationChange = () => {
      // Delay to ensure dimensions are updated
      setTimeout(() => {
        const newSize = {
          width: window.innerWidth,
          height: window.innerHeight
        };
        setScreenSize(newSize);
        setDeviceType(getDeviceType(newSize.width));
        setOrientation(getDeviceOrientation(newSize.width, newSize.height));
      }, 100);
    };

    // Listen for both resize and orientation change events
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    // Update touch detection
    setIsTouch(isTouchDevice());

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  // Enhanced device type checks
  const isMobileSmall = deviceType === 'mobile-small';
  const isMobile = deviceType === 'mobile-small' || deviceType === 'mobile';
  const isTablet = deviceType === 'tablet';
  const isDesktop = deviceType === 'desktop' || deviceType === 'large-desktop' || deviceType === 'ultra-wide';
  const isLargeDesktop = deviceType === 'large-desktop' || deviceType === 'ultra-wide';
  const isUltraWide = deviceType === 'ultra-wide';

  // Orientation checks
  const isPortrait = orientation === 'portrait';
  const isLandscape = orientation === 'landscape';

  // Combined device and orientation checks
  const isMobilePortrait = isMobile && isPortrait;
  const isMobileLandscape = isMobile && isLandscape;
  const isTabletPortrait = isTablet && isPortrait;
  const isTabletLandscape = isTablet && isLandscape;

  // Responsive grid recommendations based on device and content
  const getRecommendedGridConfig = (widgetCount: number = 0): keyof typeof RESPONSIVE_GRID_CONFIGS => {
    if (isMobileSmall) return 'mobile';
    if (isMobile) return widgetCount > 6 ? 'compact' : 'mobile';
    if (isTablet) return widgetCount > 8 ? 'compact' : 'standard';
    if (isUltraWide) return widgetCount > 12 ? 'dense' : 'spacious';
    return 'standard';
  };

  // Responsive column recommendations
  const getRecommendedColumns = (widgetCount: number = 0): number => {
    if (isMobileSmall) return 1;
    if (isMobile) return 1;
    if (isTablet) return Math.min(2, widgetCount);
    if (isDesktop) return Math.min(3, widgetCount);
    if (isLargeDesktop) return Math.min(4, widgetCount);
    if (isUltraWide) return Math.min(6, widgetCount);
    return 3;
  };

  return {
    // Screen information
    screenSize,
    deviceType,
    orientation,
    isTouch,
    
    // Device type checks
    isMobileSmall,
    isMobile,
    isTablet,
    isDesktop,
    isLargeDesktop,
    isUltraWide,
    
    // Orientation checks
    isPortrait,
    isLandscape,
    isMobilePortrait,
    isMobileLandscape,
    isTabletPortrait,
    isTabletLandscape,
    
    // Utility functions
    getRecommendedGridConfig,
    getRecommendedColumns,
    
    // Constants
    breakpoints: BREAKPOINTS
  };
};

import * as React from 'react';