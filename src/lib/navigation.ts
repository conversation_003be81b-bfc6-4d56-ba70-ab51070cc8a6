import { DASHBOARDS } from '@/config/dashboards';

/**
 * Navigation utilities for the dashboard application
 */

/**
 * Get the URL path for a dashboard
 */
export const getDashboardPath = (dashboardId: string): string => {
  const dashboard = DASHBOARDS.find(d => d.id === dashboardId);
  if (!dashboard) {
    throw new Error(`Dashboard with ID "${dashboardId}" not found`);
  }
  
  const urlParam = dashboard.slug || dashboard.id;
  return `/dashboard/${urlParam}`;
};

/**
 * Get dashboard configuration by ID or slug
 */
export const getDashboardByIdOrSlug = (identifier: string) => {
  return DASHBOARDS.find(d => d.id === identifier || d.slug === identifier);
};

/**
 * Validate if a dashboard identifier exists
 */
export const isValidDashboardId = (identifier: string): boolean => {
  return DASHBOARDS.some(d => d.id === identifier || d.slug === identifier);
};

/**
 * Get the first available dashboard
 */
export const getFirstDashboard = () => {
  return DASHBOARDS.length > 0 ? DASHBOARDS[0] : null;
};

/**
 * Get all available dashboard paths for sitemap generation
 */
export const getAllDashboardPaths = (): string[] => {
  return DASHBOARDS.map(dashboard => {
    const urlParam = dashboard.slug || dashboard.id;
    return `/dashboard/${urlParam}`;
  });
};

/**
 * Generate breadcrumb data for a given path
 */
export const generateBreadcrumbs = (pathname: string) => {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs = [{ label: 'Home', href: '/' }];

  if (segments.length === 0) {
    return breadcrumbs;
  }

  // Handle dashboard routes
  if (segments[0] === 'dashboard') {
    breadcrumbs.push({ label: 'Dashboard', href: '/dashboard' });
    
    if (segments.length > 1) {
      const dashboardId = segments[1];
      const dashboard = getDashboardByIdOrSlug(dashboardId);
      
      if (dashboard) {
        breadcrumbs.push({
          label: dashboard.title,
          href: getDashboardPath(dashboard.id),
        });
      }
    }
  }

  // Handle other routes
  else {
    const routeMap: Record<string, string> = {
      users: 'Users',
      settings: 'Settings',
    };

    segments.forEach((segment, index) => {
      const label = routeMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
      const href = '/' + segments.slice(0, index + 1).join('/');
      breadcrumbs.push({ label, href });
    });
  }

  return breadcrumbs;
};

/**
 * Navigation guard utilities
 */
export const navigationGuards = {
  /**
   * Check if user is authenticated (placeholder for future implementation)
   */
  isAuthenticated: (): boolean => {
    // TODO: Implement actual authentication check
    return true;
  },

  /**
   * Check if Sisense is properly configured
   */
  isSisenseConfigured: (): boolean => {
    const sisenseUrl = import.meta.env.VITE_SISENSE_URL;
    const sisenseToken = import.meta.env.VITE_SISENSE_TOKEN;
    return !!(sisenseUrl && sisenseToken);
  },

  /**
   * Check if user has permission to access a specific dashboard
   */
  canAccessDashboard: (dashboardId: string): boolean => {
    // TODO: Implement role-based access control
    return isValidDashboardId(dashboardId);
  },
};

/**
 * URL utilities
 */
export const urlUtils = {
  /**
   * Get query parameters from current URL
   */
  getQueryParams: (): URLSearchParams => {
    return new URLSearchParams(window.location.search);
  },

  /**
   * Update URL with new query parameters
   */
  updateQueryParams: (params: Record<string, string | null>) => {
    const url = new URL(window.location.href);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        url.searchParams.delete(key);
      } else {
        url.searchParams.set(key, value);
      }
    });

    window.history.replaceState({}, '', url.toString());
  },

  /**
   * Get the current page title based on the route
   */
  getPageTitle: (pathname: string): string => {
    const segments = pathname.split('/').filter(Boolean);
    
    if (segments.length === 0) {
      return 'IIJA Dashboard - Infrastructure Investment and Jobs Act Analytics';
    }

    if (segments[0] === 'dashboard') {
      if (segments.length === 1) {
        return 'Compose Dashboard - IIJA Dashboard';
      }
      
      const dashboardId = segments[1];
      const dashboard = getDashboardByIdOrSlug(dashboardId);
      
      if (dashboard) {
        return `${dashboard.title} - IIJA Dashboard`;
      }
    }

    // Default title for other routes
    const routeTitles: Record<string, string> = {
      users: 'Users - IIJA Dashboard',
      settings: 'Settings - IIJA Dashboard',
    };

    return routeTitles[segments[0]] || 'IIJA Dashboard';
  },
};