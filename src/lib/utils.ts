import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Re-export widget filtering utilities for convenience
export { 
  isExcluded, 
  isTabber, 
  isMapWidget, 
  isValidWidget,
  filterWidgets,
  filterWidgetsForTab,
  getUntabbedWidgets,
  categorizeWidgets,
  getWidgetStats,
  debugWidget,
  debugWidgetList
} from './widgetFilters';

export {
  parseTabScript,
  parseAllTabberWidgets,
  validateTabDefinitions,
  getTabStats,
  debugTabParsing
} from './tabParser';
