/**
 * Performance optimization utilities for Sisense dashboard components
 */

import React, { useCallback, useRef, useEffect, useMemo, useState } from 'react';

// Debounce hook for performance optimization
export const useDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  
  return useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]) as T;
};

// Throttle hook for performance optimization
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastCallRef = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  
  return useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastCallRef.current >= delay) {
      lastCallRef.current = now;
      callback(...args);
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        lastCallRef.current = Date.now();
        callback(...args);
      }, delay - (now - lastCallRef.current));
    }
  }, [callback, delay]) as T;
};

// Intersection Observer hook with performance optimizations
export const useOptimizedIntersectionObserver = (
  options: IntersectionObserverInit & {
    freezeOnceVisible?: boolean;
    rootMargin?: string;
    threshold?: number | number[];
  } = {}
) => {
  const {
    freezeOnceVisible = true,
    rootMargin = '50px',
    threshold = 0.1,
    ...observerOptions
  } = options;

  const elementRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | undefined>(undefined);
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);

  const handleIntersection = useCallback(
    ([entry]: IntersectionObserverEntry[]) => {
      const isVisible = entry.isIntersecting;
      setIsIntersecting(isVisible);

      if (isVisible && !hasBeenVisible) {
        setHasBeenVisible(true);
        
        // Disconnect observer if freezeOnceVisible is true
        if (freezeOnceVisible && observerRef.current) {
          observerRef.current.disconnect();
        }
      }
    },
    [hasBeenVisible, freezeOnceVisible]
  );

  useEffect(() => {
    const element = elementRef.current;
    
    if (!element || (freezeOnceVisible && hasBeenVisible)) {
      return;
    }

    observerRef.current = new IntersectionObserver(handleIntersection, {
      rootMargin,
      threshold,
      ...observerOptions,
    });

    observerRef.current.observe(element);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [handleIntersection, rootMargin, threshold, freezeOnceVisible, hasBeenVisible, observerOptions]);

  return {
    ref: elementRef,
    isIntersecting: freezeOnceVisible ? hasBeenVisible || isIntersecting : isIntersecting,
    hasBeenVisible,
  };
};

// Memoization utilities for complex objects
export const useDeepMemo = <T>(value: T, deps: React.DependencyList): T => {
  const ref = useRef<T>(value);
  const depsRef = useRef(deps);

  return useMemo(() => {
    // Simple deep comparison for dependency array
    const depsChanged = deps.some((dep, index) => {
      const prevDep = depsRef.current[index];
      return !Object.is(dep, prevDep);
    });

    if (depsChanged) {
      ref.current = value;
      depsRef.current = deps;
    }

    return ref.current;
  }, [value, deps]);
};

// Performance monitoring utilities - Hook version
export const usePerformanceMonitor = (componentName: string) => {
  const renderCount = useRef(0);
  const renderTimes = useRef<number[]>([]);
  const startTime = useRef<number | undefined>(undefined);

  const startRender = useCallback(() => {
    startTime.current = performance.now();
  }, []);

  const endRender = useCallback(() => {
    if (startTime.current) {
      const renderTime = performance.now() - startTime.current;
      renderTimes.current.push(renderTime);
      renderCount.current += 1;

      // Keep only last 10 render times
      if (renderTimes.current.length > 10) {
        renderTimes.current.shift();
      }

      if (process.env.NODE_ENV === 'development') {
        console.debug(`${componentName} render #${renderCount.current}: ${renderTime.toFixed(2)}ms`);
      }
    }
  }, [componentName]);

  const getStats = useCallback(() => {
    const times = renderTimes.current;
    const avgTime = times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
    const maxTime = times.length > 0 ? Math.max(...times) : 0;
    const minTime = times.length > 0 ? Math.min(...times) : 0;

    return {
      renderCount: renderCount.current,
      averageRenderTime: avgTime,
      maxRenderTime: maxTime,
      minRenderTime: minTime,
      recentRenderTimes: [...times],
    };
  }, []);

  return { startRender, endRender, getStats };
};

// Bundle size optimization utilities
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>
) => {
  return React.lazy(importFn);
};

// Memory optimization utilities
export const useMemoryOptimization = () => {
  const cleanupFunctions = useRef<(() => void)[]>([]);

  const addCleanup = useCallback((fn: () => void) => {
    cleanupFunctions.current.push(fn);
  }, []);

  const cleanup = useCallback(() => {
    cleanupFunctions.current.forEach(fn => {
      try {
        fn();
      } catch (error) {
        console.warn('Cleanup function failed:', error);
      }
    });
    cleanupFunctions.current = [];
  }, []);

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return { addCleanup, cleanup };
};

// Widget-specific performance optimizations
export const useWidgetPerformance = (widgetId: string) => {
  const renderMonitor = usePerformanceMonitor(`Widget-${widgetId}`);
  const memoryOptimization = useMemoryOptimization();
  
  const optimizedProps = useMemo(() => ({
    // Stable references for common props
    onError: (error: Error) => {
      console.error(`Widget ${widgetId} error:`, error);
    },
    onLoad: () => {
      console.debug(`Widget ${widgetId} loaded`);
    },
  }), [widgetId]);

  return {
    ...renderMonitor,
    ...memoryOptimization,
    optimizedProps,
  };
};

// Export performance configuration
export const PERFORMANCE_CONFIG = {
  INTERSECTION_OBSERVER: {
    ROOT_MARGIN: '150px',
    THRESHOLD: 0.1,
    FREEZE_ON_VISIBLE: true,
  },
  DEBOUNCE_DELAYS: {
    SEARCH: 300,
    RESIZE: 100,
    SCROLL: 16, // ~60fps
  },
  THROTTLE_DELAYS: {
    SCROLL: 16, // ~60fps
    RESIZE: 100,
    API_CALLS: 1000,
  },
  CACHE: {
    MAX_SIZE: 50,
    TTL: 5 * 60 * 1000, // 5 minutes
  },
  LAZY_LOADING: {
    CHUNK_SIZE: 10,
    PRELOAD_DISTANCE: 2,
  },
} as const;