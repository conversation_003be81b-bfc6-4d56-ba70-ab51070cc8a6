import { describe, it, expect, vi } from 'vitest';
import { parseTabScript, organizeWidgetsByTabs, isTabberWidget } from '../tabParser';
import type { ApiWidget, TabDefinition } from '@/types/sisense';

describe('tabParser', () => {
  describe('parseTabScript', () => {
    it('parses valid JavaScript tab configuration', () => {
      const script = `
        var config = {
          tabs: [
            {
              title: 'Overview',
              displayWidgetIds: ['widget-1', 'widget-2'],
              hideWidgetIds: []
            },
            {
              title: 'Details',
              displayWidgetIds: ['widget-3'],
              hideWidgetIds: ['widget-4', 'widget-5']
            }
          ]
        };
      `;

      const result = parseTabScript(script, 'tabber-1');

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        title: 'Overview',
        displayWidgetIds: ['widget-1', 'widget-2'],
        hideWidgetIds: [],
        sourceTabberId: 'tabber-1',
      });
      expect(result[1]).toEqual({
        title: 'Details',
        displayWidgetIds: ['widget-3'],
        hideWidgetIds: ['widget-4', 'widget-5'],
        sourceTabberId: 'tabber-1',
      });
    });

    it('handles different variable declaration styles', () => {
      const scripts = [
        'var config = { tabs: [{ title: "Tab1", displayWidgetIds: ["w1"], hideWidgetIds: [] }] };',
        'let config = { tabs: [{ title: "Tab1", displayWidgetIds: ["w1"], hideWidgetIds: [] }] };',
        'const config = { tabs: [{ title: "Tab1", displayWidgetIds: ["w1"], hideWidgetIds: [] }] };',
      ];

      scripts.forEach(script => {
        const result = parseTabScript(script, 'tabber-1');
        expect(result).toHaveLength(1);
        expect(result[0].title).toBe('Tab1');
      });
    });

    it('handles complex nested configurations', () => {
      const script = `
        var config = {
          metadata: {
            version: '2.0',
            author: 'test'
          },
          tabs: [
            {
              title: 'Revenue Analysis',
              displayWidgetIds: ['revenue-chart', 'revenue-table'],
              hideWidgetIds: [],
              settings: {
                color: 'blue',
                icon: 'chart'
              }
            }
          ],
          globalSettings: {
            theme: 'dark'
          }
        };
      `;

      const result = parseTabScript(script, 'tabber-1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Revenue Analysis',
        displayWidgetIds: ['revenue-chart', 'revenue-table'],
        hideWidgetIds: [],
        sourceTabberId: 'tabber-1',
      });
    });

    it('handles malformed JavaScript gracefully', () => {
      const malformedScripts = [
        'var config = { tabs: [{ title: "Incomplete"',
        'invalid javascript syntax {{{',
        'var config = { tabs: [{ title: "Missing quotes }] };',
        'var config = { tabs: [{ title: "Tab1", displayWidgetIds: ["w1"], }] };', // trailing comma
      ];

      malformedScripts.forEach(script => {
        const result = parseTabScript(script, 'tabber-1');
        expect(result).toEqual([]);
      });
    });

    it('handles missing or invalid config structures', () => {
      const invalidConfigs = [
        'var otherVariable = "test";',
        'var config = null;',
        'var config = "string";',
        'var config = 123;',
        'var config = {};',
        'var config = { tabs: null };',
        'var config = { tabs: "string" };',
        'var config = { tabs: 123 };',
      ];

      invalidConfigs.forEach(script => {
        const result = parseTabScript(script, 'tabber-1');
        expect(result).toEqual([]);
      });
    });

    it('validates tab structure and filters invalid tabs', () => {
      const script = `
        var config = {
          tabs: [
            {
              title: 'Valid Tab',
              displayWidgetIds: ['widget-1'],
              hideWidgetIds: []
            },
            {
              // Missing title
              displayWidgetIds: ['widget-2'],
              hideWidgetIds: []
            },
            {
              title: 'Missing displayWidgetIds'
              // Missing displayWidgetIds
            },
            {
              title: 'Invalid displayWidgetIds',
              displayWidgetIds: 'not-an-array',
              hideWidgetIds: []
            },
            {
              title: 'Another Valid Tab',
              displayWidgetIds: ['widget-3', 'widget-4'],
              hideWidgetIds: ['widget-5']
            }
          ]
        };
      `;

      const result = parseTabScript(script, 'tabber-1');

      expect(result).toHaveLength(2);
      expect(result[0].title).toBe('Valid Tab');
      expect(result[1].title).toBe('Another Valid Tab');
    });

    it('handles empty or null inputs', () => {
      expect(parseTabScript('', 'tabber-1')).toEqual([]);
      expect(parseTabScript(null as any, 'tabber-1')).toEqual([]);
      expect(parseTabScript(undefined as any, 'tabber-1')).toEqual([]);
    });

    it('handles tabs with special characters in titles', () => {
      const script = `
        var config = {
          tabs: [
            {
              title: 'Revenue & Profit (Q1-Q4)',
              displayWidgetIds: ['widget-1'],
              hideWidgetIds: []
            },
            {
              title: 'Cost Analysis: 2023/2024',
              displayWidgetIds: ['widget-2'],
              hideWidgetIds: []
            }
          ]
        };
      `;

      const result = parseTabScript(script, 'tabber-1');

      expect(result).toHaveLength(2);
      expect(result[0].title).toBe('Revenue & Profit (Q1-Q4)');
      expect(result[1].title).toBe('Cost Analysis: 2023/2024');
    });
  });

  describe('organizeWidgetsByTabs', () => {
    const mockWidgets: ApiWidget[] = [
      { oid: 'widget-1', title: 'Widget 1', type: 'chart' },
      { oid: 'widget-2', title: 'Widget 2', type: 'table' },
      { oid: 'widget-3', title: 'Widget 3', type: 'indicator' },
      { oid: 'widget-4', title: 'Widget 4', type: 'chart' },
      { oid: 'widget-5', title: 'Widget 5', type: 'map' },
      { oid: 'tabber-1', title: 'Tabber Widget', type: 'tabber' },
    ];

    const mockTabs: TabDefinition[] = [
      {
        title: 'Overview',
        displayWidgetIds: ['widget-1', 'widget-2'],
        hideWidgetIds: [],
        sourceTabberId: 'tabber-1',
      },
      {
        title: 'Details',
        displayWidgetIds: ['widget-3'],
        hideWidgetIds: ['widget-4'],
        sourceTabberId: 'tabber-1',
      },
    ];

    it('organizes widgets into tabs correctly', () => {
      const result = organizeWidgetsByTabs(mockWidgets, mockTabs);

      expect(result.tabbedWidgets).toHaveLength(2);
      
      // Overview tab
      expect(result.tabbedWidgets[0].tab.title).toBe('Overview');
      expect(result.tabbedWidgets[0].widgets).toHaveLength(2);
      expect(result.tabbedWidgets[0].widgets.map(w => w.oid)).toEqual(['widget-1', 'widget-2']);
      
      // Details tab
      expect(result.tabbedWidgets[1].tab.title).toBe('Details');
      expect(result.tabbedWidgets[1].widgets).toHaveLength(1);
      expect(result.tabbedWidgets[1].widgets[0].oid).toBe('widget-3');
    });

    it('identifies untabbed widgets correctly', () => {
      const result = organizeWidgetsByTabs(mockWidgets, mockTabs);

      // widget-5 is not in any tab, widget-4 is hidden, tabber-1 is excluded
      expect(result.untabbedWidgets).toHaveLength(1);
      expect(result.untabbedWidgets[0].oid).toBe('widget-5');
    });

    it('excludes tabber widgets from results', () => {
      const result = organizeWidgetsByTabs(mockWidgets, mockTabs);

      const allWidgetIds = [
        ...result.tabbedWidgets.flatMap(tw => tw.widgets.map(w => w.oid)),
        ...result.untabbedWidgets.map(w => w.oid),
      ];

      expect(allWidgetIds).not.toContain('tabber-1');
    });

    it('handles widgets that appear in multiple tabs', () => {
      const overlappingTabs: TabDefinition[] = [
        {
          title: 'Tab 1',
          displayWidgetIds: ['widget-1', 'widget-2'],
          hideWidgetIds: [],
          sourceTabberId: 'tabber-1',
        },
        {
          title: 'Tab 2',
          displayWidgetIds: ['widget-2', 'widget-3'], // widget-2 appears in both
          hideWidgetIds: [],
          sourceTabberId: 'tabber-1',
        },
      ];

      const result = organizeWidgetsByTabs(mockWidgets, overlappingTabs);

      // widget-2 should appear in both tabs
      expect(result.tabbedWidgets[0].widgets.map(w => w.oid)).toContain('widget-2');
      expect(result.tabbedWidgets[1].widgets.map(w => w.oid)).toContain('widget-2');
    });

    it('handles empty tabs and widgets arrays', () => {
      expect(organizeWidgetsByTabs([], [])).toEqual({
        tabbedWidgets: [],
        untabbedWidgets: [],
      });

      expect(organizeWidgetsByTabs(mockWidgets, [])).toEqual({
        tabbedWidgets: [],
        untabbedWidgets: mockWidgets.filter(w => !isTabberWidget(w)),
      });

      expect(organizeWidgetsByTabs([], mockTabs)).toEqual({
        tabbedWidgets: mockTabs.map(tab => ({ tab, widgets: [] })),
        untabbedWidgets: [],
      });
    });

    it('handles tabs with non-existent widget IDs', () => {
      const tabsWithMissingWidgets: TabDefinition[] = [
        {
          title: 'Tab with Missing Widgets',
          displayWidgetIds: ['widget-1', 'non-existent-widget', 'widget-2'],
          hideWidgetIds: [],
          sourceTabberId: 'tabber-1',
        },
      ];

      const result = organizeWidgetsByTabs(mockWidgets, tabsWithMissingWidgets);

      // Should only include existing widgets
      expect(result.tabbedWidgets[0].widgets).toHaveLength(2);
      expect(result.tabbedWidgets[0].widgets.map(w => w.oid)).toEqual(['widget-1', 'widget-2']);
    });

    it('preserves widget order as specified in tabs', () => {
      const orderedTabs: TabDefinition[] = [
        {
          title: 'Ordered Tab',
          displayWidgetIds: ['widget-3', 'widget-1', 'widget-2'], // Specific order
          hideWidgetIds: [],
          sourceTabberId: 'tabber-1',
        },
      ];

      const result = organizeWidgetsByTabs(mockWidgets, orderedTabs);

      expect(result.tabbedWidgets[0].widgets.map(w => w.oid)).toEqual(['widget-3', 'widget-1', 'widget-2']);
    });
  });

  describe('isTabberWidget', () => {
    it('identifies tabber widgets correctly', () => {
      const tabberWidget: ApiWidget = {
        oid: 'tabber-1',
        title: 'Tabber Widget',
        type: 'tabber',
        subtype: 'tabber',
      };

      expect(isTabberWidget(tabberWidget)).toBe(true);
    });

    it('identifies non-tabber widgets correctly', () => {
      const regularWidgets: ApiWidget[] = [
        { oid: 'widget-1', title: 'Chart', type: 'chart', subtype: 'column' },
        { oid: 'widget-2', title: 'Table', type: 'table', subtype: 'pivot' },
        { oid: 'widget-3', title: 'Indicator', type: 'indicator', subtype: 'numeric' },
        { oid: 'widget-4', title: 'Map', type: 'map', subtype: 'area' },
      ];

      regularWidgets.forEach(widget => {
        expect(isTabberWidget(widget)).toBe(false);
      });
    });

    it('handles widgets with missing type information', () => {
      const widgetsWithMissingType = [
        { oid: 'widget-1', title: 'Widget 1' } as ApiWidget,
        { oid: 'widget-2', title: 'Widget 2', type: undefined } as ApiWidget,
        { oid: 'widget-3', title: 'Widget 3', type: null } as any,
      ];

      widgetsWithMissingType.forEach(widget => {
        expect(isTabberWidget(widget)).toBe(false);
      });
    });

    it('handles case-insensitive type matching', () => {
      const tabberVariants = [
        { oid: 'tabber-1', type: 'TABBER' },
        { oid: 'tabber-2', type: 'Tabber' },
        { oid: 'tabber-3', type: 'tabber' },
      ] as ApiWidget[];

      tabberVariants.forEach(widget => {
        expect(isTabberWidget(widget)).toBe(true);
      });
    });
  });

  describe('Integration scenarios', () => {
    it('handles complete tab parsing and organization workflow', () => {
      const widgets: ApiWidget[] = [
        { oid: 'revenue-chart', title: 'Revenue Chart', type: 'chart' },
        { oid: 'revenue-table', title: 'Revenue Table', type: 'table' },
        { oid: 'cost-chart', title: 'Cost Chart', type: 'chart' },
        { oid: 'profit-indicator', title: 'Profit KPI', type: 'indicator' },
        { oid: 'summary-map', title: 'Summary Map', type: 'map' },
        {
          oid: 'main-tabber',
          title: 'Main Tabber',
          type: 'tabber',
          script: `
            var config = {
              tabs: [
                {
                  title: 'Revenue',
                  displayWidgetIds: ['revenue-chart', 'revenue-table'],
                  hideWidgetIds: []
                },
                {
                  title: 'Costs',
                  displayWidgetIds: ['cost-chart'],
                  hideWidgetIds: ['profit-indicator']
                }
              ]
            };
          `,
        },
      ];

      // Parse tabs from tabber widget
      const tabberWidget = widgets.find(w => w.type === 'tabber')!;
      const tabs = parseTabScript(tabberWidget.script!, tabberWidget.oid);

      // Organize widgets by tabs
      const organized = organizeWidgetsByTabs(widgets, tabs);

      // Verify results
      expect(tabs).toHaveLength(2);
      expect(organized.tabbedWidgets).toHaveLength(2);
      expect(organized.untabbedWidgets).toHaveLength(1); // summary-map

      // Revenue tab
      expect(organized.tabbedWidgets[0].tab.title).toBe('Revenue');
      expect(organized.tabbedWidgets[0].widgets.map(w => w.oid)).toEqual(['revenue-chart', 'revenue-table']);

      // Costs tab
      expect(organized.tabbedWidgets[1].tab.title).toBe('Costs');
      expect(organized.tabbedWidgets[1].widgets.map(w => w.oid)).toEqual(['cost-chart']);

      // Untabbed widgets
      expect(organized.untabbedWidgets[0].oid).toBe('summary-map');
    });

    it('handles multiple tabber widgets', () => {
      const widgets: ApiWidget[] = [
        { oid: 'widget-1', title: 'Widget 1', type: 'chart' },
        { oid: 'widget-2', title: 'Widget 2', type: 'table' },
        { oid: 'widget-3', title: 'Widget 3', type: 'chart' },
        { oid: 'widget-4', title: 'Widget 4', type: 'indicator' },
        {
          oid: 'tabber-1',
          type: 'tabber',
          script: 'var config = { tabs: [{ title: "Tab1", displayWidgetIds: ["widget-1"], hideWidgetIds: [] }] };',
        },
        {
          oid: 'tabber-2',
          type: 'tabber',
          script: 'var config = { tabs: [{ title: "Tab2", displayWidgetIds: ["widget-2"], hideWidgetIds: [] }] };',
        },
      ];

      // Parse tabs from both tabber widgets
      const allTabs = widgets
        .filter(w => w.type === 'tabber')
        .flatMap(w => parseTabScript(w.script!, w.oid));

      const organized = organizeWidgetsByTabs(widgets, allTabs);

      expect(allTabs).toHaveLength(2);
      expect(organized.tabbedWidgets).toHaveLength(2);
      expect(organized.untabbedWidgets).toHaveLength(2); // widget-3, widget-4
    });
  });
});