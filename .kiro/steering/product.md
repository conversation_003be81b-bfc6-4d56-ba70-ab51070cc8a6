# Product Overview

## IIJA v2 Dashboard Application

This is a React-based dashboard application for visualizing Infrastructure Investment and Jobs Act (IIJA) data. The application processes and displays data from Sisense dashboard exports, focusing on federal infrastructure funding, state budgets, contract awards, and material prices.

## Key Features

- Dashboard interface with metrics cards and data visualization
- Multiple data sources from Sisense exports (9 different dashboards)
- Infrastructure and transportation funding analytics
- State-level budget and legislative initiative tracking
- Material price monitoring and contract award analysis

## Data Sources

The application works with exported Sisense dashboard data covering:
- Summary dashboards
- Contract awards
- Value put in place
- Federal aid obligations
- IIJA state funding
- State legislative initiatives
- Federal aid highway fund usage
- State DOT budgets
- Material prices

Each export contains multiple widgets (13-41 per dashboard) and map visualizations.