# Project Structure

## Root Directory Organization

```
├── .env                    # Environment variables
├── .gitignore             # Git ignore patterns
├── .kiro/                 # Kiro IDE configuration and steering
├── README.md              # Project documentation
├── Source_sisense_exports/ # Sisense dashboard export data
├── components.json        # shadcn/ui configuration
├── eslint.config.js       # ESLint configuration
├── index.html            # Vite entry point
├── package.json          # Dependencies and scripts
├── pnpm-lock.yaml        # Package lock file
├── tsconfig.*.json       # TypeScript configurations
└── vite.config.ts        # Vite build configuration
```

## Source Code Structure (`src/`)

```
src/
├── App.tsx               # Main application component
├── main.tsx             # React application entry point
├── index.css            # Global styles and Tailwind imports
├── vite-env.d.ts        # Vite type definitions
├── assets/              # Static assets (images, icons)
├── components/          # React components
│   ├── layout/          # Layout components (DashboardLayout, etc.)
│   ├── theme/           # Theme-related components (ThemeToggle)
│   └── ui/              # shadcn/ui components (button, card, etc.)
└── lib/                 # Utility functions and helpers
    └── utils.ts         # Common utility functions
```

## Data Structure (`Source_sisense_exports/`)

Contains exported Sisense dashboard data:
- `index.json` - Metadata about all dashboard exports
- `*_SDK_*.json` - Individual dashboard export files
- Naming pattern: `{Number}{DashboardName}_SDK_{Version}.json`

## Component Organization Guidelines

### UI Components (`src/components/ui/`)
- Use shadcn/ui components as base building blocks
- Follow the "New York" style variant
- Components should be reusable and composable
- Import from `@/components/ui/[component-name]`

### Layout Components (`src/components/layout/`)
- Contains page layout and structure components
- DashboardLayout provides main application shell
- Should handle responsive design patterns

### Theme Components (`src/components/theme/`)
- Theme switching and customization components
- Dark/light mode toggle functionality

## Path Aliases

Configured path aliases for clean imports:
- `@/*` → `./src/*`
- `@/components` → `./src/components`
- `@/lib` → `./src/lib`
- `@/utils` → `./src/lib/utils`

## File Naming Conventions

- **Components**: PascalCase (e.g., `DashboardLayout.tsx`)
- **Utilities**: camelCase (e.g., `utils.ts`)
- **Styles**: kebab-case (e.g., `index.css`)
- **Config files**: lowercase with extensions (e.g., `vite.config.ts`)

## Import Organization

Follow this import order:
1. React and external libraries
2. Internal components (using `@/` aliases)
3. Relative imports
4. Type-only imports (with `type` keyword)

Example:
```typescript
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import DashboardLayout from '@/components/layout/DashboardLayout'
import type { ComponentProps } from 'react'
```

## Configuration Files

- **TypeScript**: Split configuration (app + node)
- **Tailwind**: Integrated via Vite plugin
- **ESLint**: Modern flat config with React rules
- **shadcn/ui**: Configured for New York style with CSS variables