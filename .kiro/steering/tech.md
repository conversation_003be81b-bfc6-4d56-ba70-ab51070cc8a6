# Technology Stack

## Core Technologies

- **React 19.1.1** - UI framework
- **TypeScript 5.8.3** - Type safety and development experience
- **Vite 7.1.2** - Build tool and development server
- **Tailwind CSS 4.1.12** - Utility-first CSS framework

## UI Components & Styling

- **shadcn/ui** - Component library (New York style)
- **Radix UI** - Headless UI primitives
- **Lucide React** - Icon library
- **Class Variance Authority** - Component variant management
- **Tailwind Merge & clsx** - Conditional class utilities

## Development Tools

- **ESLint 9.33.0** - Code linting with React-specific rules
- **TypeScript ESLint** - TypeScript-aware linting
- **Vite React Plugin** - Fast refresh and HMR

## Build System & Commands

### Development
```bash
pnpm dev          # Start development server
pnpm build        # Build for production (TypeScript + Vite)
pnpm preview      # Preview production build
pnpm lint         # Run ESLint
```

### Project Configuration
- Uses ES modules (`"type": "module"`)
- Path aliases configured: `@/*` maps to `./src/*`
- TypeScript project references for app and node configs
- Tailwind CSS with Vite plugin integration

## Package Manager
- **pnpm** - Fast, disk space efficient package manager