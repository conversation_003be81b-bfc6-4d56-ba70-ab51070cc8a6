# Sisense Compose SDK Development Guidelines

## SDK Overview

When working with Sisense analytics, use the **Compose SDK** for building custom data-driven applications with high flexibility and granular control over individual components.

### SDK Selection Guide
- **Compose SDK**: For building custom data-driven applications
  - **Use Case**: Build data driven applications from scratch
  - **Embeddable Elements**: Data, charts, customizable individual filters
  - **Visualizations**: Sisense charts or your own custom charts
  - **Flexibility**: High - Full control over UI/UX
  - **Languages**: React, Angular, Vue & TypeScript
  - **Best For**: Custom applications, embedded analytics, white-label solutions

- **Embed SDK**: For embedding complete dashboards
  - **Use Case**: Embed dashboards into web applications
  - **Embeddable Elements**: Entire Sisense dashboards
  - **Visualizations**: Sisense widgets only
  - **Flexibility**: Medium - Limited customization
  - **Languages**: JavaScript
  - **Best For**: Quick dashboard embedding with minimal customization

## Core Libraries

Always use these primary libraries for Sisense Compose SDK development:

### Essential Imports
```typescript
// UI Components - Charts, filters, and visual elements
import { Chart } from "@sisense/sdk-ui";

// Data Manipulation - Measures, filters, and data operations
import { measureFactory } from "@sisense/sdk-data";

// Data Model - Your specific data model (replace with actual model)
import * as DM from "./sample-ecommerce"; // or your data model
```

### Key Library Purposes
- **@sisense/sdk-ui**: Provides React components for charts, filters, and UI elements
- **@sisense/sdk-data**: Handles data operations, measure creation, and filtering
- **Data Model**: Contains your data source definitions and field mappings

## Standard Chart Implementation Pattern

Follow this pattern for implementing Sisense charts:

```tsx
import { Chart } from "@sisense/sdk-ui";
import { measureFactory } from "@sisense/sdk-data";
import * as DM from "./sample-ecommerce"; // or your data model

export default function ChartComponent() {
  return (
    <Chart
      dataSet={DM.DataSource}
      chartType={'column'} // or other chart types
      dataOptions={{
        category: [DM.Commerce.AgeRange],
        value: [measureFactory.sum(DM.Commerce.Revenue, 'Sales')],
        breakBy: [DM.Commerce.Category]
      }}
    />
  );
}
```

## Data Configuration Guidelines

### Chart Data Options Structure
- **category**: Array of dimensions for chart categories
- **value**: Array of measures (use measureFactory for aggregations)
- **breakBy**: Array of dimensions for data segmentation

### Measure Factory Functions
- `measureFactory.sum()` - Sum aggregation
- `measureFactory.count()` - Count aggregation
- `measureFactory.avg()` - Average aggregation
- `measureFactory.min()` - Minimum value
- `measureFactory.max()` - Maximum value

## Chart Types
Supported chart types include:
- `'column'` - Column/bar charts (most common)
- `'line'` - Line charts for trends over time
- `'pie'` - Pie charts for proportional data
- `'area'` - Area charts for cumulative data
- `'scatter'` - Scatter plots for correlation analysis
- `'bar'` - Horizontal bar charts
- `'funnel'` - Funnel charts for conversion analysis
- `'polar'` - Polar/radar charts
- `'treemap'` - Treemap for hierarchical data
- `'sunburst'` - Sunburst for nested hierarchical data

## Data Model Expectations

Expect data models to be structured as:
```typescript
// Data model structure
export const DataSource = "your-data-source";
export const Commerce = {
  AgeRange: "dimension",
  Revenue: "measure", 
  Category: "dimension"
};
```

## Component Structure Best Practices

1. **Import Order**: SDK UI components first, then SDK data utilities, then local data models
2. **Component Naming**: Use descriptive names that indicate the chart purpose
3. **Data Options**: Always structure data options with clear category, value, and breakBy properties
4. **TypeScript**: Leverage TypeScript for better development experience

## Advanced Features

### Filters and Interactivity
```typescript
// Add filters to charts
import { Filter } from "@sisense/sdk-data";

// Create filters
const ageFilter = Filter.members(DM.Commerce.AgeRange, ['18-24', '25-34']);

// Apply to chart
<Chart
  dataSet={DM.DataSource}
  chartType={'column'}
  dataOptions={{...}}
  filters={[ageFilter]}
/>
```

### Custom Styling and Themes
```typescript
// Apply custom styling
<Chart
  dataSet={DM.DataSource}
  chartType={'column'}
  dataOptions={{...}}
  styleOptions={{
    legend: { enabled: true, position: 'bottom' },
    colors: ['#FF6B6B', '#4ECDC4', '#45B7D1']
  }}
/>
```

## Integration Guidelines

- **Component Architecture**: Use React functional components for all chart implementations
- **Data Model Import**: Import data models as modules (e.g., `import * as DM from "./data-model"`)
- **Declarative Configuration**: Configure charts declaratively through the `dataOptions` prop
- **Data Aggregation**: Always use measureFactory for data aggregations rather than raw data manipulation
- **Error Handling**: Implement proper error boundaries around Sisense components
- **Loading States**: Handle loading states gracefully while data is being fetched

## Performance Considerations

- **Selective Imports**: Import only necessary components from SDK libraries to reduce bundle size
- **Chart Type Specificity**: Use specific chart types rather than generic configurations
- **Data Model Optimization**: Structure data models efficiently for chart rendering performance
- **Data Source Optimization**: Consider data source optimization for large datasets
- **Memoization**: Use React.memo() for chart components that don't change frequently
- **Lazy Loading**: Implement lazy loading for charts not immediately visible
## Co
mmon Patterns and Best Practices

### 1. Multi-Chart Dashboards
```typescript
import { Chart } from "@sisense/sdk-ui";
import { measureFactory } from "@sisense/sdk-data";
import * as DM from "./data-model";

export default function Dashboard() {
  return (
    <div className="dashboard-grid">
      {/* Revenue Chart */}
      <Chart
        dataSet={DM.DataSource}
        chartType={'column'}
        dataOptions={{
          category: [DM.Commerce.AgeRange],
          value: [measureFactory.sum(DM.Commerce.Revenue, 'Total Revenue')],
        }}
      />
      
      {/* Trend Chart */}
      <Chart
        dataSet={DM.DataSource}
        chartType={'line'}
        dataOptions={{
          category: [DM.Commerce.Date],
          value: [measureFactory.count(DM.Commerce.OrderID, 'Orders')],
        }}
      />
    </div>
  );
}
```

### 2. Dynamic Chart Configuration
```typescript
const chartConfig = {
  revenue: {
    type: 'column',
    category: [DM.Commerce.AgeRange],
    value: [measureFactory.sum(DM.Commerce.Revenue, 'Revenue')]
  },
  orders: {
    type: 'line',
    category: [DM.Commerce.Date],
    value: [measureFactory.count(DM.Commerce.OrderID, 'Orders')]
  }
};

export default function DynamicChart({ chartKey }) {
  const config = chartConfig[chartKey];
  
  return (
    <Chart
      dataSet={DM.DataSource}
      chartType={config.type}
      dataOptions={{
        category: config.category,
        value: config.value
      }}
    />
  );
}
```

### 3. Error Handling Pattern
```typescript
import { ErrorBoundary } from 'react-error-boundary';

function ChartErrorFallback({ error }) {
  return (
    <div className="chart-error">
      <h3>Chart Error</h3>
      <p>Unable to load chart: {error.message}</p>
    </div>
  );
}

export default function SafeChart(props) {
  return (
    <ErrorBoundary FallbackComponent={ChartErrorFallback}>
      <Chart {...props} />
    </ErrorBoundary>
  );
}
```

## Troubleshooting Common Issues

### Data Model Issues
- **Problem**: `Cannot read property of undefined` errors
- **Solution**: Ensure data model is properly imported and fields exist
- **Check**: Verify data model structure matches your Sisense instance

### Performance Issues
- **Problem**: Slow chart rendering
- **Solution**: Optimize data queries, use appropriate aggregations
- **Check**: Monitor network requests and data payload sizes

### Styling Issues
- **Problem**: Charts not matching design requirements
- **Solution**: Use `styleOptions` prop for customization
- **Check**: Refer to Sisense styling documentation for available options

## Development Workflow

1. **Setup**: Install `@sisense/sdk-ui` and `@sisense/sdk-data`
2. **Data Model**: Create or import your data model definitions
3. **Component**: Build chart components using the standard pattern
4. **Integration**: Integrate charts into your application layout
5. **Testing**: Test with real data and various screen sizes
6. **Optimization**: Profile performance and optimize as needed

## Resources and References

- **Official Documentation**: https://developer.sisense.com/guides/sdk/
- **API Reference**: https://developer.sisense.com/guides/sdk/modules/
- **Sample Data Models**: Use sample-ecommerce as reference for structure
- **Community**: Sisense developer community for advanced use cases