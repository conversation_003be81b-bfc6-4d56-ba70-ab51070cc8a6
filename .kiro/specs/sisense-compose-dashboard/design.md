# Design Document

## Overview

The Sisense Compose Dashboard will be a comprehensive React-based dashboard application that leverages the Sisense Compose SDK's WidgetById component to dynamically render widgets from multiple IIJA dashboards. The system will use the existing Sisense REST API to fetch dashboard metadata and widget configurations, then render them using the modern Compose SDK approach instead of the legacy Fusion embedding.

The dashboard will provide a unified interface for viewing transportation construction analytics, federal funding data, state budgets, and material prices across multiple data sources, all while maintaining the existing application's architecture and design patterns.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[React Application] --> B[Dashboard Router]
    B --> C[Compose Dashboard Component]
    C --> D[Sisense Context Provider]
    C --> E[Dashboard Tabs Manager]
    C --> F[Widget Grid Layout]
    
    E --> G[Dashboard Metadata API]
    F --> H[WidgetById Components]
    
    H --> I[Sisense Compose SDK]
    I --> J[Sisense REST API]
    
    G --> K[Dashboard Configuration]
    K --> L[Widget Filtering Logic]
    L --> M[Tab Organization Logic]
```

### Component Hierarchy

```
ComposeDashboard
├── SisenseContextProvider
├── DashboardTabsContainer (shadcn/ui Tabs)
│   ├── DashboardTab (shadcn/ui TabsContent)
│   │   ├── TabsContainer (shadcn/ui Tabs - nested)
│   │   │   ├── WidgetTab (shadcn/ui TabsContent)
│   │   │   │   └── WidgetGrid (shadcn/ui Card grid)
│   │   │   │       └── ComposeWidgetById (shadcn/ui CardContent)
│   │   │   └── UntabbedWidgets
│   │   │       └── WidgetGrid (shadcn/ui Card grid)
│   │   │           └── ComposeWidgetById (shadcn/ui CardContent)
│   │   └── WidgetGrid (shadcn/ui Card grid)
│   │       └── ComposeWidgetById (shadcn/ui CardContent)
├── LoadingStates (shadcn/ui Skeleton)
├── ErrorBoundaries (shadcn/ui Alert)
└── RefreshControls (shadcn/ui Button)
```

## Components and Interfaces

### Core Components

#### 1. ComposeDashboard Component
**Purpose**: Main dashboard container that orchestrates the entire dashboard experience using shadcn/ui layout components
**Props**:
```typescript
interface ComposeDashboardProps {
  className?: string;
  defaultDashboardId?: string;
  showDebugInfo?: boolean;
}
```
**shadcn/ui Components Used**: Card, CardContent, Tabs, TabsList, TabsTrigger, TabsContent

#### 2. SisenseContextProvider Component
**Purpose**: Provides Sisense configuration and authentication context
**Props**:
```typescript
interface SisenseContextProps {
  url: string;
  token: string;
  children: React.ReactNode;
}
```

#### 3. DashboardTabsContainer Component
**Purpose**: Manages dashboard-level tabs and navigation using shadcn/ui Tabs components
**Props**:
```typescript
interface DashboardTabsContainerProps {
  dashboards: DashboardConfig[];
  activeDashboardId: string;
  onDashboardChange: (id: string) => void;
  children: React.ReactNode;
}
```
**shadcn/ui Components Used**: Tabs, TabsList, TabsTrigger, TabsContent, Button, Separator

#### 4. WidgetGrid Component
**Purpose**: Responsive grid layout for widgets using shadcn/ui Card components
**Props**:
```typescript
interface WidgetGridProps {
  widgets: ApiWidget[];
  dashboardOid: string;
  className?: string;
  columns?: number;
}
```
**shadcn/ui Components Used**: Card, CardContent, CardHeader, CardTitle

#### 5. ComposeWidgetById Component
**Purpose**: Enhanced wrapper around Sisense WidgetById with error handling and loading states using shadcn/ui components
**Props**:
```typescript
interface ComposeWidgetByIdProps {
  widgetOid: string;
  dashboardOid: string;
  title?: string;
  showTitle?: boolean;
  showWidgetId?: boolean;
  className?: string;
  height?: number;
  width?: number;
  styleOptions?: WidgetByIdStyleOptions;
  onError?: (error: Error) => void;
  onLoad?: () => void;
}
```
**shadcn/ui Components Used**: Card, CardContent, CardHeader, CardTitle, Skeleton, Alert, AlertDescription, Button

### Data Interfaces

#### Dashboard Configuration
```typescript
interface DashboardConfig {
  id: string;
  title: string;
  slug: string;
  description: string;
}

interface ApiWidget {
  oid: string;
  title?: string;
  type?: string;
  subtype?: string;
  script?: string;
}

interface TabDefinition {
  title: string;
  displayWidgetIds: string[];
  hideWidgetIds: string[];
  sourceTabberId?: string;
}
```

#### API Response Types
```typescript
interface DashboardWidgetsResponse {
  widgets: ApiWidget[];
  tabs: TabDefinition[];
  isLoading: boolean;
  error: string | null;
}

interface SisenseApiError {
  status: number;
  message: string;
  details?: any;
}
```

## Data Models

### Dashboard State Management
```typescript
interface DashboardState {
  activeDashboardId: string;
  dashboards: DashboardConfig[];
  widgetsByDashboard: Record<string, ApiWidget[]>;
  tabsByDashboard: Record<string, TabDefinition[]>;
  loadingStates: Record<string, boolean>;
  errors: Record<string, string | null>;
}
```

### Widget State Management
```typescript
interface WidgetState {
  widgetOid: string;
  dashboardOid: string;
  isLoading: boolean;
  isError: boolean;
  error?: Error;
  isVisible: boolean;
}
```

### Local Storage Schema
```typescript
interface DashboardPreferences {
  activeDashboardId: string;
  collapsedTabs: string[];
  widgetSizes: Record<string, { width: number; height: number }>;
  lastRefreshTime: number;
}
```

## Error Handling

### Error Boundary Strategy
1. **Global Error Boundary**: Catches unhandled errors in the entire dashboard
2. **Dashboard Error Boundary**: Catches errors specific to dashboard loading
3. **Widget Error Boundary**: Catches errors from individual widgets without affecting others

### Error Types and Handling
```typescript
enum ErrorType {
  AUTHENTICATION = 'authentication',
  NETWORK = 'network',
  WIDGET_LOAD = 'widget_load',
  DASHBOARD_LOAD = 'dashboard_load',
  PARSING = 'parsing'
}

interface ErrorHandler {
  type: ErrorType;
  message: string;
  retry?: () => void;
  fallback?: React.ComponentType;
}
```

### Graceful Degradation
- **Widget Load Failure**: Show error card with retry option
- **Dashboard Load Failure**: Show error message with refresh button
- **Network Issues**: Show offline indicator with retry mechanism
- **Authentication Failure**: Redirect to configuration or show auth error

## Testing Strategy

### Unit Testing
- **Component Testing**: Test each component in isolation using React Testing Library
- **Hook Testing**: Test custom hooks with mock data
- **Utility Testing**: Test helper functions and data transformations

### Integration Testing
- **API Integration**: Test Sisense API communication with mock responses
- **Widget Rendering**: Test WidgetById component integration
- **State Management**: Test dashboard state transitions

### End-to-End Testing
- **Dashboard Navigation**: Test switching between dashboards
- **Widget Interaction**: Test widget loading and error states
- **Responsive Design**: Test layout on different screen sizes

### Performance Testing
- **Load Testing**: Test with multiple widgets loading simultaneously
- **Memory Testing**: Monitor memory usage with large datasets
- **Render Performance**: Measure component render times

## Implementation Phases

### Phase 1: Core Infrastructure
1. Set up Sisense Context Provider with authentication
2. Create basic dashboard routing and navigation
3. Implement API service for fetching dashboard metadata
4. Create error boundaries and loading states

### Phase 2: Widget Rendering
1. Implement ComposeWidgetById wrapper component
2. Create WidgetGrid layout component
3. Add widget filtering and exclusion logic
4. Implement basic error handling for widgets

### Phase 3: Advanced Features
1. Add tabber widget parsing and tab organization
2. Implement refresh functionality
3. Add local storage for user preferences
4. Create responsive design optimizations

### Phase 4: Polish and Optimization
1. Add loading skeletons and animations
2. Implement performance optimizations
3. Add comprehensive error handling
4. Create accessibility improvements

## Security Considerations

### Authentication
- Store Sisense tokens securely using environment variables
- Implement token refresh mechanism if needed
- Handle authentication errors gracefully

### Data Protection
- Validate all API responses before processing
- Sanitize widget titles and descriptions
- Implement proper CORS handling

### Error Information
- Avoid exposing sensitive information in error messages
- Log detailed errors server-side while showing user-friendly messages
- Implement proper error reporting for debugging

## Performance Optimizations

### Lazy Loading
- Implement lazy loading for off-screen widgets
- Use React.lazy for code splitting of dashboard components
- Load widget data on-demand when tabs are activated

### Caching Strategy
- Cache dashboard metadata in local storage
- Implement stale-while-revalidate pattern for widget data
- Use React Query or SWR for API state management

### Rendering Optimizations
- Use React.memo for widget components
- Implement virtual scrolling for large widget lists
- Optimize re-renders with proper dependency arrays

### Bundle Optimization
- Tree-shake unused Sisense SDK components
- Implement dynamic imports for dashboard-specific code
- Optimize asset loading and compression